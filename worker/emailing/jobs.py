import asyncio
import logging
from collections import OrderedDict, defaultdict
from datetime import (
    timedelta,
)
from itertools import chain

import sqlalchemy as sa
from aiohttp import web

import app.telegram.notifications as telegram_notifications
from app.auth import utils as auth
from app.auth.constants import COMPANY_ID_REQUEST_KEY
from app.auth.db import (
    SelectUsersBulkInput,
    select_company_by_role_id,
    select_expected_company,
    select_user,
    select_users,
    select_users_bulk,
    update_roles,
)
from app.auth.db import select_coworkers as select_coworkers_db
from app.auth.enums import RoleActivationSource, RoleStatus
from app.auth.tables import (
    company_table,
    is_active_filter,
    role_table,
    token_table,
    user_table,
)
from app.auth.types import UpdateRoleDict, User
from app.auth.utils import create_coworker_roles
from app.auth.validators import validate_user_permission
from app.billing.utils import (
    generate_reason_bill_notification,
    is_company_has_pro_or_higher_rate,
)
from app.billing.validators import validate_bill_exists_by_id
from app.contacts import utils as contacts
from app.documents.constants import MAX_NUMBER_OF_FILES_FOR_SIGNERS
from app.documents.db import (
    select_document,
    select_document_for_owner_email,
    select_unfinished_documents,
)
from app.documents.emailing import (
    send_about_document_access,
    send_documents_to_signer,
    send_signed_by_coworkers_documents,
)
from app.documents.notifications import DocumentInboxNotification
from app.documents.utils import share_document
from app.events import document_actions
from app.feedbacks.constants import FEEDBACKS_EMAILS
from app.feedbacks.db import select_feedbacks_by_time_range
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.flow import utils as flow_utils
from app.groups.db import select_groups
from app.groups.utils import get_group_members_by_group_ids
from app.i18n import _
from app.lib import emailing, utm_params
from app.lib.datetime_utils import (
    parse_utc_timestamp,
    utc_now,
)
from app.lib.emailing import STATUS_TITLES, can_receive_notification, send_email
from app.lib.enums import DocumentFolder, DocumentStatus, NotificationType
from app.lib.helpers import add_quotes, csv_writer, update_urls
from app.lib.types import (
    DataDict,
    StrList,
)
from app.lib.urls import build_static_url, build_url
from app.mobile.notifications import notifications as mobile_notifications
from app.models import select_all
from app.notifications.db import insert_notifications
from app.notifications.enums import EmailPermission, NotificationName, NotificationSource
from app.notifications.utils import prepare_unsubscribe_block
from app.services import services
from app.signatures.db import select_document_signers
from worker import topics
from worker.emailing.constants import LAST_EXECUTION_TIME_REDIS_KEY
from worker.emailing.utils import (
    SignerWithInfo,
    complete_registration_for_users,
    filter_documents_awaiting_signers,
    filter_only_allowed_emails,
    get_more_companies_data,
    get_roles_ids_with_pending_roles,
    invite_new_company_coworkers,
    prepare_coworkers_data,
    select_companies_with_activity,
    select_coworkers,
    select_document_signers_with_info,
    select_existed_users,
    select_expiring_tokens,
    select_inactive_companies_query,
    select_new_documents_details,
    select_new_documents_totals,
    select_recipients_for_sign_process_finished,
    select_unregistered_recipients_for_reminder,
    send_reminder_to_unregistered_user,
    send_roles_to_crm,
)
from worker.utils import (
    retry_config,
)

DictWithListOfDicts = defaultdict[str, list[DataDict]]

logger = logging.getLogger(__name__)


@retry_config(max_attempts=5)
async def send_payment_successful_email(
    __: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """Send email to bill creator about successfull payment"""

    bill_id = data['bill_id']
    async with services.db.acquire() as conn:
        bill = await validate_bill_exists_by_id(conn, bill_id)

    subject = _('Успішна оплата рахунку Вчасно')
    template_name = 'pb_successfull_payment'

    home_page_url = services.config.app.domain
    reason = generate_reason_bill_notification(bill)
    if not reason:
        logger.info(
            'Can not generate reason for successful payment notification',
            extra={'bill_id': bill_id},
        )

    context = {
        'home_page_url': home_page_url,
        'reason': reason,
    }

    await send_email(
        recipient_mixed=bill.email,
        subject=subject,
        template_name=template_name,
        context=context,
    )


# TODO: Refator to smaller jobs
@retry_config(max_attempts=5)
async def send_notification_to_signers(
    __: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Sending email to signers with notification that they should sign a document
    after coworker sign or during parallel signing initiated by document owner
    :param data: dict:
        - 'documents_ids': List[str] - documents ids that will be shown in the email letter
        - 'more_documents_count': int - amount of files that won't show
                                    in the letter
        - 'coworker': dict:
            - 'name': str - coworker's full name
            - 'email': str - coworker's email
        - 'signers': List[dict]:
            - 'name': str - signer's name
            - 'email': str - signer's email
            - 'telegram_chat_id': int - telegram chat id
            - 'company_id': str - signer company_id
            - 'can_receive_inbox': bool
            - 'can_receive_notifications': bool
            - 'group_name': str | None - name of the group
            - 'language': str | None - language of the recipient ('en', 'uk')
    """

    documents_ids = data.get('documents_ids', [])
    more_documents_count = data.get('more_documents_count')
    coworker = data.get('coworker', {})
    signers = data.get('signers', [])

    logger.info('Start sending notification to signers')

    if not documents_ids:
        logger.info('documents was not provided in "data" argument')
        return

    not_filtered_emails = []
    company_has_pro_mapping: dict[str, bool] = {}

    async with services.db_readonly.acquire() as conn:
        documents = await select_unfinished_documents(conn, documents_ids)
        if not documents:
            logger.info('Nothing to send because of all documents in final status')
            return

        recipients = await select_users_bulk(
            conn=conn,
            input=[
                SelectUsersBulkInput(
                    email=signer['email'],
                    company_id=signer['company_id'],
                )
                for signer in signers
            ],
        )

        for recipient in recipients:
            company_id = recipient.company_id
            if company_id not in company_has_pro_mapping:
                company_has_pro_ = await is_company_has_pro_or_higher_rate(conn, company_id)
                company_has_pro_mapping[company_id] = company_has_pro_

    for signer in signers:
        if not can_receive_notification(signer, NotificationType.inbox):
            logger.info('Skip sending notification to signer', extra={'signer': signer})
            continue

        not_filtered_emails.append(signer.get('email'))
        company_has_pro = company_has_pro_mapping.get(signer['company_id'], False)
        await send_documents_to_signer(
            signer=signer,
            coworker=coworker,
            documents=documents,
            company_has_pro=company_has_pro,
            more_documents_count=more_documents_count,
        )
        await telegram_notifications.send_documents_to_signer(
            signer=signer,
            documents=documents,
            more_documents_count=more_documents_count,
        )

    for recipient in recipients:
        await mobile_notifications.send_push_notification_about_documents_for_signer(
            recipient=recipient,
            documents=documents,
            more_documents_count=more_documents_count,
        )

    logger.info(
        'Sending notification to signers was finished',
        extra={'emails': not_filtered_emails},
    )


# TODO: Refator to smaller jobs
@retry_config(max_attempts=5)
async def send_email_to_document_owner(
    __: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Sending notification to owner and coworkers (if parallel signing)
    with notification that they should sign a document after 3p sign
    :param data: dict:
        - 'company_edrpou': str - document owner edrpou
        - 'document_id': str - id of document for sending
        - 'sender_role_id': Optional[str] - id role that sent document
    """

    document_id: str = data['document_id']
    recipient_edrpou: str = data['company_edrpou']  # also is document owner
    sender_role_id: str | None = data.get('sender_role_id')
    sender_edrpou: str | None = data.get('sender_edrpou')

    async with services.db.acquire() as conn:
        # Get optional sender user. Sender can be None if document was sent
        # from sign session without role_id.
        sender: User | None = None
        if sender_role_id is not None:
            sender = await auth.get_user(conn, role_id=sender_role_id)

        sender_config = await auth.get_company_config(
            conn=conn,
            company_edrpou=sender_edrpou,
        )

        # Get recipient company signers
        signers = await select_document_signers(
            conn=conn,
            document_id=document_id,
            company_edrpou=recipient_edrpou,
        )
        if not signers:
            logger.warning(
                'No signers for company and document',
                extra=data,
            )
            return

        # send only to first signer when no order in signing document
        signers = signers[:1] if signers[0].order else signers

        # select full info about signers
        signers_roles_ids: list[str] = [signer.role_id for signer in signers if signer.role_id]
        signers_group_ids: list[str] = [signer.group_id for signer in signers if signer.group_id]

        role_group_mapping = {}
        signers_roles_all_ids = signers_roles_ids[:]

        if signers_group_ids:
            groups = await select_groups(conn, ids=signers_group_ids)
            groups_mapping = {group.id: group for group in groups}

            group_role_mapping = await get_group_members_by_group_ids(
                conn,
                group_ids=signers_group_ids,
            )
            role_group_mapping = {
                role_id: group_id
                for group_id, role_ids in group_role_mapping.items()
                for role_id in role_ids
            }
            signers_roles_all_ids += chain.from_iterable(group_role_mapping.values())

        recipients = await select_users(conn=conn, roles_ids=signers_roles_all_ids)

        # Get config for recipient company
        recipient_config = await auth.get_company_config(
            conn=conn,
            company_edrpou=recipient_edrpou,
        )

        document = await select_document_for_owner_email(conn, document_id)
        if not document:
            logger.warning('Could not find document', extra={'data': data})
            return

        # Get state of document flow for multilateral documents
        flows_state = await flow_utils.get_flows_state(conn, document_id=document_id)

        for recipient in recipients:
            group_name = None
            # mark only if recipient is group member and not signer by role
            if recipient.id in role_group_mapping and not recipient.id not in signers_roles_ids:
                group_id = role_group_mapping[recipient.id]
                group_name = groups_mapping[group_id].name

            notification = DocumentInboxNotification(
                conn=conn,
                recipient=recipient,
                document=document,
                sender=sender,
                recipient_has_multiple_roles=False,
                sender_config=sender_config,
                recipient_config=recipient_config,
                flows_state=flows_state,
                group_name=group_name,
            )
            await notification.send()


@retry_config(max_attempts=5)
async def invite_coworkers(app: web.Application, data: DataDict, logger: logging.Logger) -> None:
    """
    Sending invites to given EMAIL/EDRPOU pairs. Steps for sending invites:
    1) Filter only emails that are valid for company.
    2) Send invites for unregistered users.
    3) Crete and activate role for registered users.

    :param data: dict:
        - emails: list[str] - list of emails
        - role_id: str - role id of user that initiate sending invites.

    """
    all_emails = data.get('emails', [])
    role_id: str | None = data.get('role_id')

    if not role_id:
        logging.exception('No role_id for inviting coworkers')
        return

    async with app['db'].acquire() as conn:
        invited_by = await select_user(conn, role_id=role_id)
        company = await select_company_by_role_id(conn, role_id)

        if not invited_by or not company:
            logging.exception('User or company not found', extra={'role_id': role_id})
            return

        edrpou = invited_by.company_edrpou
        validate_user_permission(invited_by, {'can_invite_coworkers'})

        logger.info(
            'Start sending coworker invitations',
            extra={'count_emails': len(all_emails), 'role_id': role_id},
        )

        # 0. Filter only valid emails
        email_domains = company.email_domains
        emails = filter_only_allowed_emails(all_emails, email_domains)

        # 1. Select existed users
        existed_users = await select_existed_users(conn, emails)
        existed_emails = {existed_user.email for existed_user in existed_users}

        # 2. Send emails to unregistered users
        new_user_emails = set(emails) - existed_emails
        await invite_new_company_coworkers(
            conn,
            emails=new_user_emails,
            user=invited_by,
            company=company,
        )

        # 3. Select existed coworkers and filter coworkers with valid email
        # domain
        coworkers = await select_coworkers(conn, emails, edrpou, email_domains)
        coworkers_ids = {coworker.id for coworker in coworkers}
        # 4. Prepare data for database operation
        roles_data, not_finished_users_ids = prepare_coworkers_data(
            users=existed_users,
            coworkers_ids=coworkers_ids,
            company_id=invited_by.company_id,
            invited_by_role_id=invited_by.role_id,
        )

        # 5. Prepare role update to pending roles
        role_ids_for_update = get_roles_ids_with_pending_roles(coworkers)

        # 6. Insert coworkers roles and activate unregistered users
        async with conn.begin():
            roles = await create_coworker_roles(
                conn=conn,
                data=roles_data,
                invited_by=invited_by,
            )
            # TAG: role_activation
            new_role_status: UpdateRoleDict = {
                'status': RoleStatus.active,
                'activated_by': invited_by.role_id,
                'date_activated': sa.func.now(),
                'activation_source': RoleActivationSource.invite,
                'invited_by': invited_by.role_id,
                'date_invited': sa.func.now(),
            }
            await update_roles(conn, role_ids_for_update, new_role_status)
            await complete_registration_for_users(conn, not_finished_users_ids)
            await contacts.add_role_to_contact_recipients_indexation(
                conn=conn,
                roles_ids=role_ids_for_update,
            )

    # 7. Send coworkers to CRM
    if roles:
        new_role_ids = {role.id for role in roles}
        role_ids_for_crm = set(role_ids_for_update).union(new_role_ids)
        await send_roles_to_crm(roles_ids=role_ids_for_crm, company_id=company.id)

    logger.info(
        'Sending coworker invitations finished',
        extra={
            'count_all_emails': len(all_emails),
            'count_emails': len(emails),
            'count_existed_users': len(existed_users),
            'count_new_user_emails': len(new_user_emails),
            'count_coworkers': len(coworkers_ids),
            'count_user_update': len(not_finished_users_ids),
            'role_id': role_id,
        },
    )


@retry_config(max_attempts=5)
async def send_first_notification_to_signers(
    __: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Send first notification to document signers (internal sign process)
    :param data:
        - document_ids: List[str]
        - current_company_id: str
        - current_role_id: str

    WARNING: Pay attention to the fact that this function should work for all server documents
    at once. And each of the documents can have different signer settings.
    For example, the same signer can be at the same time in this function:
     - first signer of the ordered sign process
     - last signer of the parallel sign process
     - one of the signers of the parallel sign process
    """
    documents_ids: list[str] = data.get('document_ids', [])
    current_role_id: str = data['current_role_id']
    current_company_id: str = data['current_company_id']

    async with services.db.acquire() as conn:
        documents = await select_unfinished_documents(conn, documents_ids)
        if not documents:
            logger.info('No active documents for notification', extra=data)
            return

        current_company = await select_expected_company(conn, company_id=current_company_id)

        # Filter documents based on which side should sign now
        # This prevents sending notifications to wrong company's signers
        valid_document_ids = await filter_documents_awaiting_signers(
            conn=conn,
            documents=documents,
            company_edrpou=current_company.edrpou,
        )
        if not valid_document_ids:
            logger.info('No documents with correct company side for notification', extra=data)
            return

        signers = await select_document_signers_with_info(
            conn=conn,
            documents_ids=valid_document_ids,
            company_id=current_company_id,
        )
        if not signers:
            logger.info('No signers for notification', extra=data)
            return

        # Group over document_id and remove assigner if it is necessary
        signers_filtered = []
        for signer in signers:
            # Skip sending to user who does action that will trigger this job to avoid
            # spamming about a document on which he or she is currently working.
            #
            # One exception that it should work only when a sign process was created from the web.
            # This is because most of the time API integration is written in that way that author
            # can be not aware of his actions that are performed by integration on his behalf.
            if signer.role_id == current_role_id and signer.is_web_source:
                logger.info(
                    msg='Skip sending first notification to signer',
                    extra={
                        'current_role_id': current_role_id,
                        'assigner': signer.signer_assigner,
                        'email': signer.user_email,
                        'role_id': signer.role_id,
                        'source': signer.signer_source,
                        'order': signer.signer_order,
                    },
                )
                continue

            signers_filtered.append(signer)

        signers = signers_filtered

        # Collect roles and their documents
        signers_mapping: dict[str, SignerWithInfo] = {}
        signers_documents_mapping: defaultdict[str, list[SignerWithInfo]] = defaultdict(list)
        assigners_roles_ids: set[str] = set()
        for signer in signers:
            # The same role can be signer for more than one document, database JOIN
            # duplicates basic information about user, so we can keep any instance
            # of object for each role
            signers_mapping[signer.role_id] = signer
            signers_documents_mapping[signer.role_id].append(signer)
            assigners_roles_ids.add(signer.signer_assigner)

        # Collect information about assigners (most of the time it's only one user)
        assigners = await select_users(conn, roles_ids=list(assigners_roles_ids))
        assigners_mapping: dict[str, DataDict] = {
            assigner.role_id: {
                'name': assigner.full_name,
                'email': assigner.email,
            }
            for assigner in assigners
        }

    logger.info(
        'Sending first notification to signers',
        extra={'count_signers': len(signers_mapping)},
    )

    values = []
    for role_id, signer in signers_mapping.items():
        coworker_documents = signers_documents_mapping[role_id]
        first_documents = coworker_documents[:MAX_NUMBER_OF_FILES_FOR_SIGNERS]
        more_documents_count = len(coworker_documents) - len(first_documents)

        # Information about who sent the document to the signers:
        # Job SEND_NOTIFICATION_TO_SIGNERS can tolerate missing assigner data, so it's
        # OK to provide empty dict when we don't want to display it or don't have
        # assigner data
        assigner_data: DataDict = {}
        if not signer.is_assigner:
            assigner_data = assigners_mapping.get(signer.signer_assigner, {})

        values.append(
            {
                'documents_ids': [item.document_id for item in first_documents],
                'more_documents_count': more_documents_count,
                'coworker': assigner_data,
                'signers': [signer.to_signer_dict()],
            }
        )

    await services.kafka.send_records(
        topic=topics.SEND_NOTIFICATION_TO_SIGNERS,
        values=values,
    )


@retry_config(max_attempts=5)
async def send_document_to_recipient_job(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Wrapper around heavy function `share_document`

    :param data: dict
        - recipient_edrpou: str
        - recipient_email: str
        - document_id: str
        - current_role_id: str
        - first_name: Optional[str]

    """
    logger.info('Start document sharing job', extra=data)

    recipient_edrpou: str = data['recipient_edrpou']
    recipient_email: str = data['recipient_email']
    document_id: str = data['document_id']
    current_role_id: str = data['current_role_id']

    async with app['db'].acquire() as conn:
        current_user = await auth.get_user(conn, role_id=current_role_id)
        if not current_user:
            logger.info('Current user was not found', extra=data)
            return

        await share_document(
            conn=conn,
            recipient_edrpou=recipient_edrpou,
            recipient_email=recipient_email,
            document_id=document_id,
            sender=current_user,
        )
        logger.info('Document was shared', extra=data)


@retry_config(max_attempts=5, delay_minutes=1)
async def send_notification_about_sign_process(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """Send notification about signed documents by coworkers from company
    side to uploader and signers assigner

    :param data: dict
        - start_time: timestamp when job message pushed to queue
    """
    logger.info('Start sending notification about sign process')

    # Extract time from data
    start_time = parse_utc_timestamp(data['start_time'])

    # Get lower bound for selecting documents
    # Storing last execution time in Redis to avoid sending the same documents
    # twice or missing documents
    # if the job is scheduled and executed not exactly once per hour.
    last_execution_time_raw = await services.redis.get(LAST_EXECUTION_TIME_REDIS_KEY)
    if last_execution_time_raw:
        last_execution_time = parse_utc_timestamp(float(last_execution_time_raw))
    else:
        # Calculate the correct time for selecting documents from last 1 hours
        last_execution_time = start_time - timedelta(hours=1)

    if last_execution_time < start_time - timedelta(hours=3):
        # Just to make sure that we don't have too big range for selection of documents,
        # reset it after 3h if something goes wrong.
        # Yes, user can miss some documents, but it is better than
        # having none because of request timeout
        last_execution_time = start_time - timedelta(hours=1)

    logger.info(
        msg='Selecting recipients for sign process finished',
        extra={
            'start_time': start_time,
            'last_execution_time': last_execution_time,
        },
    )
    async with services.db_readonly.acquire() as conn:
        recipients = await select_recipients_for_sign_process_finished(
            conn,
            from_time=last_execution_time,
            to_time=start_time,
        )

    # Group recipients by role_id and append documents to recipient
    recipients_mapping: dict[str, DataDict] = {}
    seen_documents: defaultdict[str, set[str]] = defaultdict(set)  # { role1: {doc1, doc2, ...} }
    for recipient in recipients:
        role_id = recipient.role_id

        # Add role ot recipient mapping only once
        if role_id not in recipients_mapping:
            recipients_mapping[role_id] = {
                'company_id': recipient.company_id,
                'email': recipient.user_email,
                'first_name': recipient.user_name,
                'company_name': recipient.company_name,
                'company_edrpou': recipient.company_edrpou,
                'documents': [],
                'as_sign_assigner': False,
                'as_document_uploader': False,
            }
            recipient_data = recipients_mapping[role_id]
        else:
            recipient_data = recipients_mapping[role_id]

        # User can be in both roles — as someone who uploaded a document and as someone who
        # created a sign process
        if recipient.is_assigner:
            recipient_data['as_sign_assigner'] = True
        else:
            recipient_data['as_document_uploader'] = True

        # Documents can be duplicated because a user can be selected as assigner or uploader
        if recipient.document_id not in seen_documents[role_id]:
            document_base = {'id': recipient.document_id, 'title': recipient.document_title}
            recipient_data['documents'].append(document_base)
            seen_documents[role_id].add(recipient.document_id)

    logger.info(
        msg='Signed documents by coworkers',
        extra={
            'start_time': start_time,
            'last_execution_time': last_execution_time,
            'document_count': len(recipients),
        },
    )

    records = list(recipients_mapping.values())
    await services.kafka.send_records(
        topic=topics.SEND_SIGNED_BY_COWORKERS_DOCUMENTS,
        values=records,
    )
    await services.redis.set(
        LAST_EXECUTION_TIME_REDIS_KEY,
        start_time.timestamp(),
        ex=60 * 60 * 24,  # 60 seconds * 60 minutes * 24 hours = 1 day
    )
    logger.info('Signed by coworker documents was sent')


@retry_config(max_attempts=5)
async def send_signed_by_coworker_documents_job(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Send notification about signed documents by coworkers. Stateless function that
    doesn't select any data, just send notification

     :param data: dict
        - email: str - recipient email
        - company_id: str - recipient company ID
        - first_name: Optional[str] - first name of recipient
        - company_edrpou: str - recipient company edrpou
        - company_name: Optional[str] -  recipeint company name
        - documents: List[Dict[str, str]] - signed documents, dict must have`id`
            and `title` keys
        - as_sign_assigner: Optional[bool] - flag that indicates if recipient
            is sign assigner or not
        - as_document_uploader: Optional[bool] - flag that indicates if recipient
            is document uploader or not
    """
    email: str = data['email']
    company_id: str = data['company_id']
    first_name: str | None = data.get('first_name')
    company_edrpou: str = data['company_edrpou']
    company_name: str | None = data['company_name']
    documents: list[dict[str, str]] = data['documents']
    as_sign_assigner: bool = data.get('as_sign_assigner', True)
    as_document_uploader: bool = data.get('as_document_uploader', False)

    await send_signed_by_coworkers_documents(
        email=email,
        first_name=first_name,
        company_id=company_id,
        company_edrpou=company_edrpou,
        company_name=company_name,
        documents=documents,
        as_sign_assigner=as_sign_assigner,
        as_document_uploader=as_document_uploader,
    )


@retry_config(max_attempts=5)
async def send_notification_about_document_access(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    :param data:
        - document_id: str - id of document with access
        - document_title: str - title of document with access
        - coworker_email: str - who opened the access
        - coworker_role_id: str - who opened the access
        - role_id: str - who must receive email
        - comment: str - comment from coworker who opened the acess
    """
    document_id: str = data['document_id']
    coworker_email: str = data['coworker_email']
    coworker_role_id: str = data.get('coworker_role_id', None)
    role_id: str = data['role_id']
    comment: str = data['comment']

    async with app['db'].acquire() as conn:
        user = await select_user(conn, role_id=role_id)
        document = await select_document(conn, document_id=document_id)

    if not user:
        logger.warning(
            msg='No user exists for notification about document access',
            extra={'data': data},
        )
        return

    if not document:
        logger.warning(
            msg='No document exists for notification about document access',
            extra={'data': data},
        )
        return

    await send_about_document_access(
        user=user,
        document_id=document_id,
        document_title=document.title,
        coworker_email=coworker_email,
        comment=comment,
    )

    # if a record arrives without a field
    # TODO remove condition when some time has passed
    if not coworker_role_id:
        return

    async with services.db.acquire() as conn:
        company = await select_company_by_role_id(conn, coworker_role_id)
        if not company:
            return

    await document_actions.add_document_action(
        document_action=document_actions.DocumentAction(
            action=document_actions.Action.access_granted,
            document_id=document.id,
            document_edrpou_owner=document.edrpou_owner,
            document_title=document.title,
            company_id=company.id,
            company_edrpou=company.edrpou,
            email=coworker_email,
            role_id=coworker_role_id,
            extra={'to': user.email},
        )
    )


@retry_config(max_attempts=10)
async def send_reminders_to_unregistered_users(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Select unregistered recipients and set jobs for sending reminder email to
    this recipients
    :param data:
        - iteration: int - iteration number
    """
    iteration: int = data.get('iteration', 0)

    # TODO: remove after all users previous will be notified at least twice
    limit: int = data.get('limit', 2)

    if iteration >= limit:
        logger.info('Iteration limit exceed', extra={'limit': limit})
        return

    async with app['db'].acquire() as conn:
        recipients = await select_unregistered_recipients_for_reminder(conn)
        if not recipients:
            logger.info('Last unregistered recipient bucket processed')
            return

        processed_recipients: list[tuple[str, str]] = []
        for recipient in recipients:
            recipient_key = (recipient.company_edrpou, recipient.email)
            if recipient_key in processed_recipients:
                logger.info(msg='Skip duplicated recipient', extra={'recipient': recipient_key})
                continue

            try:
                await send_reminder_to_unregistered_user(
                    app=app,
                    conn=conn,
                    edrpou=recipient.company_edrpou,
                    email=recipient.email,
                )
                processed_recipients.append(recipient_key)
            except Exception as exc:
                logger.exception(
                    msg='Sending email to unregistered user failed',
                    extra=dict(recipient),
                    exc_info=exc,
                )

        # retry job with next iteration.
        await app['kafka'].send_record(
            topic=topics.SEND_REMINDERS_TO_UNREGISTERED_USERS,
            value={'iteration': iteration + 1, 'limit': limit},
        )

    logger.info('All jobs are set to unregistered reminders')


async def send_daily_feedbacks_job(
    __: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    today = utc_now()
    yesterday = today - timedelta(days=1)

    async with services.db.acquire() as conn:
        feedbacks = await select_feedbacks_by_time_range(
            conn=conn,
            date_from=yesterday,
            date_to=today,
        )

    if not feedbacks:
        return

    csv_rows = [
        {
            'role_id': feedback['role_id'],
            'email': feedback['email'],
            'phone': feedback['phone'],
            'edrpou': feedback['edrpou'],
            'feedback': feedback['feedback'],
            'date_created': feedback['date_created'].isoformat(),
        }
        for feedback in feedbacks
    ]

    fieldnames = [
        'role_id',
        'email',
        'phone',
        'edrpou',
        'feedback',
        'date_created',
    ]
    csv_content = csv_writer(headers=fieldnames, rows=csv_rows)

    yesterday_str = yesterday.strftime('%d.%m')
    subject = _('Vchasno_EDO_feedbacks_{yesterday}').bind(yesterday=yesterday_str)
    filename = f'{subject}.csv'

    attachment = emailing.Attachment(body=csv_content, content_type='text/csv', name=filename)

    await emailing.send_email(
        recipient_mixed=FEEDBACKS_EMAILS,
        subject=subject,
        template_name='feedbacks_report',
        context={},
        attachments=[attachment],
    )


async def send_reminder_about_new_documents_job(
    __: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """Iterate inactive companies notifying its coworkers about updates.

    Initial reminder rules:

    - is inactive more than 3 days (no document action)
    - has new documents, comments or rejects during this period
    - has unsigned documents
    """

    logger.info('Start sending reminders about new documents')

    # TODO[ID]: Refactor to utility function
    urls = {
        'all_documents': build_url('app', tail='', get=utm_params.REMINDER),
        'multi_sign': build_url(
            route='app',
            tail='',
            get=dict(
                utm_params.REMINDER_MULTI_SIGN,
                folder_id=DocumentFolder.inbox.value,
                status_id=DocumentStatus.signed_and_sent.value,
            ),
        ),
        'one_sign': build_url(
            route='app',
            tail='',
            get=dict(
                utm_params.REMINDER_ONE_SIGN,
                folder_id=DocumentFolder.inbox.value,
                signatures_to_finish=1,
                status_id=DocumentStatus.finished.value,
            ),
        ),
        'rejects': build_url(
            route='app',
            tail='',
            get=dict(
                utm_params.REMINDER_REJECTS,
                folder_id=DocumentFolder.outgoing.value,
                status_id=DocumentStatus.reject.value,
            ),
        ),
        'comments': build_url(
            route='app',
            tail='',
            get=dict(
                utm_params.REMINDER_COMMENTS,
                folder_id=DocumentFolder.has_comments.value,
            ),
        ),
    }

    async with services.db_readonly.acquire() as readonly_conn:
        async for recipient in readonly_conn.execute(select_inactive_companies_query()):  # type: ignore
            # TODO: Refactor to custom function instead of inner loop
            if recipient.days_inactive % 3 != 0:
                continue

            # Collect totals and example details
            blocks = ('multi_sign', 'one_sign', 'rejects', 'comments')
            company_edrpou = recipient.company_edrpou
            company_id = str(recipient.company_id)
            date_from = recipient.max_date_created
            totals: dict[str, dict[str, int | str]] = dict(
                zip(
                    blocks,
                    await select_new_documents_totals(readonly_conn, company_edrpou, date_from),
                )
            )
            if not sum(total['count'] for total in totals.values()):  # type: ignore
                continue

            # Add urls for total values in template table
            for block in totals:
                totals[block]['url'] = urls[block]

            # Subject and labels
            company_name = add_quotes(recipient.company_name)
            company_label = (
                f'{company_name}, ' if company_name else ''
            ) + f'ЄДРПОУ/ІПН {company_edrpou}'
            subject = _(
                'Нагадуємо, партнери компанії {company_label} чекають на опрацювання документів'
            ).format(company_label=company_label)
            url_text = [
                'До всіх документів, що очікують вашого підпису',
                'До всіх документів, що не потребують вашого підпису',
                'До всіх відхилених документів',
                'До всіх документів з коментарями',
            ]

            companies = await select_companies_with_activity(
                readonly_conn, edrpou=company_edrpou, date_from=date_from
            )
            more_companies_data = get_more_companies_data(companies)

            details = OrderedDict(
                zip(
                    blocks,
                    await select_new_documents_details(readonly_conn, company_edrpou, date_from),
                )
            )
            for block, document in details.items():
                if not document:
                    continue

                is_one_sign = block == 'one_sign'
                status_title = STATUS_TITLES[document['status_id']]
                if company_edrpou == document['edrpou_owner']:
                    document_status_title = (
                        status_title['owner_one_sign_title']
                        if is_one_sign
                        else status_title['owner_title']
                    )
                else:
                    document_status_title = (
                        status_title['recipient_one_sign_title']
                        if is_one_sign
                        else status_title['recipient_title']
                    )

                data = details[block]
                data['status_title'] = document_status_title
                data['status_color'] = status_title['color']
                data['documents_url_text'] = url_text[blocks.index(block)]
                data['documents_url'] = urls[block]
                data['document_url'] = build_url(
                    route='app',
                    tail='/documents/{}'.format(document['id']),
                    get=utm_params.REMINDER_DOCUMENT_VIEW,
                )

            # Send one email per coworker, enabling personal `unsubscribe` link
            coworkers = await select_coworkers_db(
                readonly_conn, company_id, role_table.c.can_receive_reminders.is_(True)
            )
            if not coworkers:
                logger.warning(
                    'Attempt to send reminder to empty emails list. Maybe all '
                    'coworkers turned off receiving reminders',
                    extra={'edrpou': company_edrpou},
                )
                continue

            company_has_pro = await is_company_has_pro_or_higher_rate(readonly_conn, company_id)

            async with services.db.acquire() as conn:
                for coworker in coworkers:
                    if not can_receive_notification(coworker, NotificationType.reminder):
                        continue

                    email = coworker.email
                    role_id = coworker.role_id

                    # Prepare `unsubscribe` meta
                    payload = {
                        'role_id': role_id,
                        'permission': EmailPermission.can_receive_reminders.value,
                    }
                    unsubscribe_block = await prepare_unsubscribe_block(payload=payload)

                    youtube_url = 'https://youtu.be/2IqxWC8ShD8'  # about PRO
                    youtube_link = (
                        f'{youtube_url}?utm_medium=email&utm_source=youtube_link'
                        '&utm_campaign=reminder_about_new_documents'
                    )
                    context = {
                        'totals': totals,
                        'details': details,
                        'urls': update_urls(urls, query={COMPANY_ID_REQUEST_KEY: company_id}),
                        'company_label': company_label,
                        'url_label': 'Перейти до документів',
                        'unsubscribe': unsubscribe_block,
                        'companies': companies,
                        'more_companies_data': more_companies_data,
                        'image_sofia': build_static_url('images/sofia.png'),
                        'youtube_link': youtube_link,
                        'enable_pro_advice_block': get_flag(
                            FeatureFlags.ENABLE_PRO_ADVICE_BLOCK_IN_EMAILS
                        )
                        and not company_has_pro,
                    }
                    await emailing.send_email(
                        recipient_mixed=email,
                        subject=subject,
                        template_name='reminder_new_documents',
                        context=context,
                    )
                    # Save notification meta to database
                    await insert_notifications(
                        conn,
                        [
                            {
                                'role_id': role_id,
                                'company_id': company_id,
                                'document_id': document_id,
                                'email': email,
                                'name': NotificationName.reminder_about_new_documents,
                                'source': NotificationSource.email,
                            }
                            for document_id in recipient.document_ids.split(',')
                        ],
                    )
                    logger.info(
                        'Sent reminder about new documents',
                        extra={
                            'recipient_edrpou': company_edrpou,
                            'recipient_email': email,
                        },
                    )

    logger.info('Sent reminders about new documents. All OK!')


async def send_reminder_about_token_expiration_job(
    __: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Notifies admins and owner of a token 7 days before token expiration.
    """

    logger.info('Start send_reminder_about_token_expiration')

    subject = _('Закінчується термін дії токену у «Вчасно»')

    async with services.db.acquire() as conn:
        tokens = await select_expiring_tokens(conn)

        company_roles_mappings: defaultdict[str, StrList] = defaultdict(list)

        roles = await select_all(
            conn,
            sa.select(
                [
                    role_table.c.id,
                    user_table.c.email,
                    company_table.c.edrpou,
                ]
            )
            .select_from(
                token_table.join(role_table, role_table.c.id == token_table.c.role_id)
                .join(
                    company_table,
                    company_table.c.id == role_table.c.company_id,
                )
                .join(
                    user_table,
                    user_table.c.id == role_table.c.user_id,
                ),
            )
            .where(
                sa.and_(
                    company_table.c.edrpou.in_([token.edrpou for token in tokens]),
                    role_table.c.can_receive_token_expiration.is_(True),
                    is_active_filter,
                )
            ),
        )

        for role in roles:
            company_roles_mappings[role.edrpou].append(role.email)

        for token in tokens:
            context = {
                'expiration_date': token.date_expired,
                'company_name': token.name,
                'edrpou': token.edrpou,
                'user_email': token.email,
            }
            logger.info(
                'Handle token',
                extra={
                    'context': context,
                    'roles': company_roles_mappings[token.edrpou],
                },
            )
            jobs = []
            for email in {*company_roles_mappings[token.edrpou], token.email}:
                jobs.append(
                    emailing.send_email(
                        recipient_mixed=email,
                        subject=subject,
                        template_name='reminder_token_expiration',
                        context=context,
                    )
                )
            await asyncio.gather(*jobs)
