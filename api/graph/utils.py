import asyncio
import json
import logging
from functools import partial
from typing import Any, TypeVar

from aiohttp import web
from hiku.engine import Context, Engine
from hiku.graph import Graph
from hiku.readers.graphql import read
from hiku.result import denormalize

from api.graph.constants import (
    AUTH_SUPER_ADMIN_PERMISSIONS,
    COMPANY_CONFIG,
    CONTEXT_STORAGE,
    DB_ENGINE_KEY,
    DB_READONLY_KEY,
    ES_KEY,
    REQUEST_KEY,
    SIGN_SESSION_ID_KEY,
    USER_KEY,
)
from api.graph.exceptions import (
    GraphQLBaseError,
    GraphQLCancelledError,
    GraphQLError,
    GraphQLSyntaxError,
    GraphQLUnhandledError,
    GraphQLValidationError,
)
from api.graph.validators import QueryValidator, validate_query_graph
from app.auth.constants import (
    AUTH_USER_COMPANY_CONFIG,
    AUTH_USER_SUPER_ADMIN_PERMISSIONS,
)
from app.auth.schemas import CompanyConfig
from app.auth.types import AuthUser, BaseUser, User
from app.auth.utils import get_sign_session_id_from_request, has_super_admin_access
from app.lib import validators
from app.lib.enums import UserRole
from app.lib.helpers import soft_json_serializer
from app.lib.types import DataDict
from app.services import services

logger = logging.getLogger(__name__)
T = TypeVar('T')


def graphql_response(
    *,
    data: DataDict | None = None,
    error: GraphQLBaseError | None = None,
) -> web.Response:
    """
    Prepare response for GraphQL query
    """
    if error:
        return web.json_response(
            # currently we support only one error to simplify the logic, but in general
            # graphql spec allows multiple errors at once
            data={'data': None, 'errors': [error.to_response()]},
            status=error.http_status or 400,
        )

    if data:
        return web.json_response(
            data={'data': data},
            dumps=partial(json.dumps, default=soft_json_serializer),
            status=200,
        )

    raise TypeError('data or errors keyword argument missed')


async def query_graph(
    *,
    request: web.Request,
    user: AuthUser | User | BaseUser | None,
    query: str,
    variables: DataDict | None = None,
) -> DataDict:
    """Process request to GraphQL.

    1. Prepare and validate Hiku query
    2. Execute query in graph
    3. Denormalize results
    """
    engine: Engine = request.app['hiku_engine']
    graph: Graph = request.app['hiku_graph']
    query = read(query, variables)

    # Validate query
    validator = QueryValidator(graph)
    validator.visit(query)
    # usually 10 is good enough, but we have some complex queries
    # for introspection, so increase it a bit
    validator.check_depth(13)
    if validator.errors.list:
        raise GraphQLValidationError(validator.errors.list)

    # Execute graph
    context: dict[str, Any] = {
        USER_KEY: user,
        DB_ENGINE_KEY: services.db_readonly,
        DB_READONLY_KEY: services.db_readonly,
        SIGN_SESSION_ID_KEY: get_sign_session_id_from_request(request),
        ES_KEY: services.es,
        REQUEST_KEY: request,
        AUTH_SUPER_ADMIN_PERMISSIONS: request[AUTH_USER_SUPER_ADMIN_PERMISSIONS],
        COMPANY_CONFIG: request[AUTH_USER_COMPANY_CONFIG],
        CONTEXT_STORAGE: {},
    }
    result = await engine.execute(graph, query, context)

    # Denormalize results
    return denormalize(graph, result)


async def process_query_graph(
    *,
    request: web.Request,
    user: AuthUser | User | BaseUser | None,
) -> web.Response:
    data = validate_query_graph(await validators.validate_json_request(request))
    try:
        # Currently we have 30 sec timeout on istio level for all requests.
        # And we want to track slow graph queries,
        # so we set timeout to request_timeout * 2 = 29 sec.
        async with asyncio.timeout(services.config.app.request_timeout * 2):
            graph_data = await query_graph(
                request=request,
                user=user,
                query=data.query,
                variables=data.variables,
            )
    except GraphQLSyntaxError:
        logger.exception('GraphQL syntax error')
        return graphql_response(error=GraphQLSyntaxError())

    except GraphQLValidationError as err:
        logger.error(
            'Validation error on executing GraphQL',
            extra=dict(data, errors=err.errors),
        )
        return graphql_response(error=err)

    except GraphQLError as err:
        return graphql_response(error=err)

    except (asyncio.CancelledError, TimeoutError):
        logger.warning(
            'Slow query on executing GraphQL',
            extra={
                'query': data.query,
                'variables': str(data.variables),
            },
            exc_info=True,
        )
        return graphql_response(error=GraphQLCancelledError())

    except Exception:
        logger.exception(
            'Unhandled exception on executing GraphQL',
            extra={
                'query': data.query,
                'variables': str(data.variables),
            },
        )
        return graphql_response(error=GraphQLUnhandledError())

    return graphql_response(data=graph_data)


def can_view_coworkers_graph(user: User) -> bool:
    """
    Check if the user can view coworkers
    """
    return user.user_role == UserRole.admin.value or user.can_view_coworkers


def get_raw_graph_user(
    ctx: Context,
) -> User | BaseUser | AuthUser | None:
    """
    All requests to GraphQL can be authenticated by sign session (AuthUser),
    by web and mobile session (BaseUser or User) or without authentication at all (None).
    """
    return ctx[USER_KEY]


def get_graph_user(ctx: Context) -> User | None:
    """ """
    user = get_raw_graph_user(ctx)
    if isinstance(user, User):
        return user
    return None


def get_base_graph_user(ctx: Context) -> BaseUser | User | None:
    """
    Returns BaseUser or User if available, otherwise None.
    This is useful for cases where we need to handle both types of users.
    """
    user = get_raw_graph_user(ctx)
    if isinstance(user, BaseUser | User):
        return user

    return None


def get_wide_graph_user(ctx: Context) -> User | AuthUser | None:
    """
    Returns User or AuthUser if available, otherwise None.
    This is useful for cases where we need to handle both types of users.
    """
    user = get_raw_graph_user(ctx)
    if isinstance(user, User | AuthUser):
        return user

    return None


def check_sa_graph_user(ctx: Context, *, required_permissions: set[str]) -> User | None:
    """
    Returns User if the user is a superadmin, otherwise None.
    This is useful for cases where we need to ensure the user has superadmin permissions.
    """
    user = get_graph_user(ctx)
    if not user:
        return None

    super_admin_permissions = get_graph_super_admin_permissions(ctx)
    company_config = get_graph_company_config(ctx)

    if has_super_admin_access(
        user=user,
        company_config=company_config,
        required_permissions=required_permissions,
        super_admin_permissions=super_admin_permissions,
    ):
        return user

    return None


def get_graph_company_config(ctx: Context) -> CompanyConfig:
    """
    Returns company config from the context.
    This is useful for cases where we need to access company-specific configurations.
    """
    return ctx[COMPANY_CONFIG]


def get_graph_sign_session_id(ctx: Context) -> str | None:
    """
    Returns sign session ID from the context.
    This is useful for cases where we need to access sign session ID.
    """
    return ctx[SIGN_SESSION_ID_KEY]


def get_graph_super_admin_permissions(ctx: Context) -> set[str]:
    """
    Returns super admin permissions from the context.
    This is useful for cases where we need to access super admin permissions.
    """
    return ctx[AUTH_SUPER_ADMIN_PERMISSIONS] or set()


def get_graph_request(ctx: Context) -> web.Request:
    """
    Returns the web request from the context.
    This is useful for cases where we need to access the original request object.
    """
    return ctx[REQUEST_KEY]
