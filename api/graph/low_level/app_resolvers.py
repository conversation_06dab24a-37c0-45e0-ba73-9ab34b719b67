import asyncio
import dataclasses
import logging
from collections import defaultdict
from datetime import (
    datetime,
    timedelta,
)
from typing import (
    Any,
    cast,
)

import sqlalchemy as sa
from elasticmagic import Bool
from elasticmagic.agg import Terms
from hiku.engine import (
    Context,
    Nothing,
    pass_context,
)
from sqlalchemy import func
from sqlalchemy.dialects import postgresql
from typing_extensions import deprecated

from api.db import get_document_listing_date_mapping
from api.graph.constants import (
    DB_ENGINE_KEY,
    DB_READONLY_KEY,
    ES_KEY,
)
from api.graph.context import get_graph_context_dict__document_date_listing
from api.graph.low_level import types as low_level_types
from api.graph.low_level.utils import (
    get_options_from_fields,
    prepare_resolve_es_documents_options,
    select_document_id_for_graph,
    track_graph_resolve_time,
)
from api.graph.types import (
    FieldList,
    OptionalStr,
)
from api.graph.utils import (
    can_view_coworkers_graph,
    check_sa_graph_user,
    get_base_graph_user,
    get_graph_sign_session_id,
    get_graph_super_admin_permissions,
    get_graph_user,
    get_raw_graph_user,
    get_wide_graph_user,
)
from api.private.super_admin.constants import SURVEY_TO_REDIS_KEY
from app.auth import two_factor
from app.auth.db import (
    exists_company_by_id,
    get_super_admin_permissions_for_roles,
    select_companies_by_edrpou,
    select_companies_by_ids,
    select_company_by_id,
    select_tokens_for_graph,
)
from app.auth.enums import RoleStatus
from app.auth.tables import (
    active_role_company_join,
    company_table,
    role_table,
    user_active_role_company_join,
    user_table,
)
from app.auth.types import AuthUser, SuperAdminPermissions, User, is_wide_user_type
from app.auth.utils import (
    get_companies_configs_by_edrpous,
    get_companies_configs_by_ids,
    get_companies_configs_by_roles_ids,
    get_company_config,
    get_default_company_config,
)
from app.banner.tables import banner_content_table
from app.billing.db import (
    select_accounts,
    select_bills_by_rate_extension_ids,
    select_companies_rate_extensions_for_graph,
    select_companies_rates_for_graph,
    select_companies_trial_rates_for_graph,
)
from app.billing.enums import (
    AccountType,
    CompanyRateStatus,
)
from app.billing.tables import billing_account_table
from app.comments.tables import comment_table
from app.config import is_test_environ
from app.contacts.db import select_count_unregistered_contacts
from app.contacts.tables import (
    contact_person_table,
    contact_table,
)
from app.csat.db import select_csat_surveys
from app.document_versions.utils import (
    get_latest_document_versions_available_for_company,
)
from app.documents.db import (
    exists_invalid_signed_company_docs,
    select_documents_recipients_by_ids,
)
from app.documents.resolvers import (
    handle_resolve_document_not_found,
    resolve_documents_from_es,
    schedule_jobs_on_resolve_document,
)
from app.documents.tables import (
    delete_request_table,
    document_link_table,
    document_recipients_table,
    document_table,
    listing_table,
)
from app.documents.utils import (
    get_documents_access_filters,
)
from app.documents_required_fields.db import (
    get_document_required_fields_by_companies_ids,
)
from app.es.enums import ESQuerySource
from app.es.models.document import Document
from app.es.utils import (
    build_listing_query,
    count_es_documents,
    fetch_es,
)
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.flow.tables import doc_flow_table
from app.lib.database import DBRow
from app.lib.datetime_utils import utc_now
from app.lib.enums import UserRole
from app.lib.helpers import (
    group_list,
    unique_list_with_original_order,
)
from app.lib.types import (
    AnyList,
    DataDict,
    IntList,
    StrDict,
    StrList,
)
from app.models import (
    select_all,
)
from app.profile.constants import (
    KEP_POPUP_DATE_FORMAT,
    KEP_POPUP_DATE_KEY,
    KEP_POPUP_TIMEOUT,
)
from app.reviews.db import (
    select_review_requests,
    select_reviews,
)
from app.reviews.tables import (
    review_setting_table,
    review_status_table,
)
from app.services import services
from app.signatures.tables import (
    document_signer_table,
    signature_table,
)
from app.signatures.validators import can_sign_by_user_for_graph
from app.tags.db import (
    select_tags_by_contacts,
    select_tags_by_document_templates_ids,
    select_tags_by_documents_for_graph,
    select_tags_by_ids,
)
from app.tags.tables import (
    contact_tag_table,
    document_template_tag_table,
    role_tag_table,
    tag_table,
)
from app.tags.utils import get_tags_access_filter, select_tags_for_document_filter

logger = logging.getLogger(__name__)

EMPTY_STR_LIST: list[str] = []
EMPTY_TUPLE_LIST: list[tuple[str, str]] = []


@pass_context
async def resolve_billing_accounts(ctx: Context, ids: StrList) -> list[StrList]:
    """Return only active accounts."""
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        accounts = await select_accounts(
            conn=conn,
            clause=sa.and_(
                billing_account_table.c.company_id.in_(ids),
                billing_account_table.c.type.in_(
                    [
                        AccountType.client_rate,
                        AccountType.client_bonus,
                        AccountType.client_debit,
                        AccountType.client_bonus_custom,
                    ]
                ),
            ),
        )

    accounts_map: defaultdict[str, StrList] = defaultdict(list)
    for account in accounts:
        accounts_map[account.company_id].append(account.id)

    return [accounts_map.get(company_id, []) for company_id in ids]


@pass_context
async def resolve_unregistered_document_view(
    ctx: Context, fields: StrList, ids: StrList
) -> list[list[bool]]:
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        config = await get_company_config(conn, company_id=ids[0])
    return [[config.allow_unregistered_document_view]]


@pass_context
async def resolve_company_rates(ctx: Context, ids: StrList) -> list[StrList]:
    """Resolve active and new company rates"""
    user = get_base_graph_user(ctx)
    if not user:
        return [[] for _ in ids]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        rates = await select_companies_rates_for_graph(conn, ids)

    rates_map: defaultdict[str, StrList] = defaultdict(list)
    for rate in rates:
        rates_map[rate.company_id].append(rate.id_)

    return [rates_map.get(company_id, []) for company_id in ids]


@pass_context
async def resolve_company_rate_extensions(ctx: Context, ids: StrList) -> list[StrList]:
    """Resolve rate extensions for active company rates"""
    base_user = get_base_graph_user(ctx)
    if not base_user:
        return [[] for _ in ids]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        extensions = await select_companies_rate_extensions_for_graph(conn, ids)

    extensions_map: defaultdict[str, StrList] = defaultdict(list)
    for extension in extensions:
        extensions_map[extension.company_id].append(extension.id)

    return [extensions_map.get(company_id, []) for company_id in ids]


@pass_context
async def resolve_company_trial_rates(ctx: Context, ids: StrList) -> list[StrList]:
    base_user = get_base_graph_user(ctx)
    if not base_user:
        return [[] for _ in ids]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        rates = await select_companies_trial_rates_for_graph(conn, ids)

    mapping = group_list(rates, lambda r: r.company_id)
    return [[rate.id_ for rate in mapping[company_id]] for company_id in ids]


@pass_context
async def resolve_comments(ctx: Context) -> StrList:
    user = get_graph_user(ctx)
    if not user:
        return []

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        rows = await select_all(
            conn,
            (sa.select([comment_table.c.id]).where(comment_table.c.role_id == user.role_id)),
        )
    return [row.id for row in rows]


@pass_context
async def resolve_comments_from_document_node(ctx: Context, document_ids: StrList) -> list[StrList]:
    document_ids_cleaned = list(filter(None, document_ids))
    rows: list[DBRow] = []
    user = get_raw_graph_user(ctx)
    if not user or not is_wide_user_type(user):
        return [[] for _ in document_ids]

    if isinstance(user, AuthUser):
        company_id_clause = comment_table.c.access_company_id.is_(None)
    else:
        company_id_clause = sa.or_(
            comment_table.c.access_company_id.is_(None),
            comment_table.c.access_company_id == user.company_id,
        )

    if document_ids_cleaned:
        async with ctx[DB_ENGINE_KEY].acquire() as conn:
            rows = await select_all(
                conn,
                (
                    sa.select([comment_table.c.id, comment_table.c.document_id])
                    .where(
                        sa.and_(
                            comment_table.c.document_id.in_(document_ids_cleaned),
                            company_id_clause,
                        )
                    )
                    .order_by(comment_table.c.date_created.asc())
                ),
            )

    mapping: defaultdict[str, StrList] = defaultdict(list)
    for row in rows:
        mapping[row.document_id].append(row.id)

    return [mapping.get(item, []) for item in document_ids]


@pass_context
async def resolve_companies(ctx: Context, options: StrDict) -> StrList:
    user = get_base_graph_user(ctx)
    if not user:
        return []

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        rows = await select_all(
            conn,
            (
                sa.select([func.distinct(company_table.c.id).label('id')])
                .select_from(user_active_role_company_join)
                .where(user_table.c.id == user.id)
            ),
        )
    return [str(row.id) for row in rows]


@pass_context
async def resolve_company(ctx: Context, options: StrDict) -> OptionalStr:
    user = get_base_graph_user(ctx)
    if not user:
        return Nothing

    company_id = options['id']
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        if not await exists_company_by_id(conn, company_id, user_id=user.id):
            return Nothing
    return company_id


@pass_context
async def resolve_company_edrpou_from_role_node(
    ctx: Context, fields: FieldList, role_ids: StrList
) -> list[StrList]:
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        rows = await select_all(
            conn,
            (
                sa.select([company_table.c.edrpou, role_table.c.id])
                .select_from(active_role_company_join)
                .where(role_table.c.id.in_(role_ids))
            ),
        )
    mapping = {row.id: row.edrpou for row in rows}
    return [[mapping.get(idx, Nothing)] for idx in role_ids]


@pass_context
async def resolve_company_config_from_role_node(
    ctx: Context,
    fields: FieldList,
    roles_ids: StrList,
) -> list[AnyList]:
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        mapping = await get_companies_configs_by_roles_ids(conn, roles_ids=roles_ids)

    default_config = get_default_company_config()
    return [[mapping.get(role_id, default_config).to_dict()] for role_id in roles_ids]


@pass_context
async def resolve_company_from_document_node(
    ctx: Context, edrpou_list: StrList
) -> list[OptionalStr]:
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        rows = await select_all(
            conn,
            (
                sa.select([company_table.c.id, company_table.c.edrpou]).where(
                    company_table.c.edrpou.in_(edrpou_list)
                )
            ),
        )
    mapping = {row.edrpou: row.id for row in rows}
    return [mapping.get(item, Nothing) for item in edrpou_list]


@pass_context
async def resolve_contact_from_document_node(
    ctx: Context, edrpou_list: StrList
) -> list[OptionalStr]:
    user = get_graph_user(ctx)
    if not user:
        return [Nothing for _ in edrpou_list]

    conditions = list(filter(None, edrpou_list))
    current_user_edrpou = user.company_edrpou
    current_user_company_id = user.company_id
    rows: list[DBRow] = []

    if conditions:
        query = (
            sa.select([contact_table.c.edrpou, contact_table.c.id])
            .select_from(contact_table)
            .where(
                sa.and_(
                    contact_table.c.company_id == current_user_company_id,
                    contact_table.c.edrpou.in_(conditions),
                    contact_table.c.edrpou != current_user_edrpou,
                )
            )
        )

        async with ctx[DB_ENGINE_KEY].acquire() as conn:
            rows = await select_all(conn, query)

    mapping = {row.edrpou: row.id for row in rows}
    return [mapping.get(item, Nothing) for item in edrpou_list]


@pass_context
async def resolve_contact_person_from_document_node(
    ctx: Context, email_list: StrList
) -> list[OptionalStr]:
    user = get_graph_user(ctx)
    if not user:
        return [Nothing for _ in email_list]

    conditions = list(filter(None, email_list))
    current_user_company_id = user.company_id
    rows: list[DBRow] = []

    if conditions:
        query = (
            sa.select([contact_person_table.c.email, contact_person_table.c.id])
            .select_from(
                contact_person_table.join(
                    contact_table,
                    contact_table.c.id == contact_person_table.c.contact_id,
                )
            )
            .where(
                sa.and_(
                    contact_table.c.company_id == current_user_company_id,
                    contact_person_table.c.email.in_(conditions),
                )
            )
        )

        async with ctx[DB_ENGINE_KEY].acquire() as conn:
            rows = await select_all(conn, query)

    mapping = {row.email: row.id for row in rows}
    return [mapping.get(item, Nothing) for item in email_list]


async def _resolve_count_documents_from_es(
    ctx: Context, options: DataDict, user: User | AuthUser
) -> int:
    es = ctx[ES_KEY]

    # User without company doesn't have any documents
    if not user.company_edrpou:
        return 0

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        options = await prepare_resolve_es_documents_options(conn, options, user)

    es_query = await build_listing_query(user, es.documents, options, True)
    return await count_es_documents(es_query, source=ESQuerySource.graph)


@pass_context
async def resolve_current_company_edrpou(
    ctx: Context, _: FieldList, ids: StrList
) -> list[list[str | None]]:
    user = get_wide_graph_user(ctx)
    if not user:
        return [[None] for _ in ids]

    return [[user.company_edrpou] for _ in ids]


@pass_context
async def resolve_companies_config(
    ctx: Context, _: FieldList, ids: StrList
) -> list[list[DataDict]]:
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        configs = await get_companies_configs_by_ids(
            conn=conn,
            companies_ids=ids,
        )
    result = []

    for company_id in ids:
        # We always return some config for company even if it doesn't exist in DB.
        # This is needed to prevent errors when frontend by mistake wrongly might assume
        # default value.
        config = configs.get(company_id) or get_default_company_config()
        result.append([config.model_dump(mode='json')])

    return result


@track_graph_resolve_time
@pass_context
async def resolve_document_can_sign_field(
    ctx: Context,
    _: FieldList,
    ids: StrList,
) -> list[list[bool]]:
    """Check if document can be signed by current user."""

    user = get_wide_graph_user(ctx)
    if not user:
        return [[False] for _ in ids]

    res = {}
    tasks = []
    for document_id in ids:
        tasks.append(
            can_sign_by_user_for_graph(
                document_id=document_id,
                user=user,
            )
        )

    for document_id, can_sign in zip(ids, await asyncio.gather(*tasks)):
        res[document_id] = can_sign

    return [[res[document_id]] for document_id in ids]


@pass_context
async def resolve_count_documents(ctx: Context, fields: FieldList) -> IntList:
    user = get_graph_user(ctx)
    if not user:
        return [0]

    options = get_options_from_fields(fields)

    if get_flag(FeatureFlags.ES_SEARCH):
        count = await _resolve_count_documents_from_es(ctx, options, user)
    else:
        count = 0

    return [count or 0]


@pass_context
async def resolve_active_rates(ctx: Context, _: FieldList, ids: StrList) -> list[list[StrList]]:
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        rates = await select_companies_rates_for_graph(conn, companies_ids=ids)

    rate_map: defaultdict[str, set[str]] = defaultdict(set)
    for rate in rates:
        if rate.status == CompanyRateStatus.active:
            rate_map[rate.company_id].add(rate.rate.value)

    return [[list(rate_map.get(company_id) or [])] for company_id in ids]


@pass_context
async def resolve_current_role(ctx: Context) -> str | None:
    user = get_graph_user(ctx)
    if not user:
        return Nothing

    return user.role_id or Nothing


@pass_context
async def resolve_current_roles(ctx: Context, _: FieldList, ids: StrList) -> list[list[str | None]]:
    return [[await resolve_current_role(ctx)] for _ in ids]


@pass_context
async def resolve_current_user_email(
    ctx: Context, _: FieldList, ids: StrList
) -> list[list[str | None]]:
    user = get_raw_graph_user(ctx)
    return [[user.email if user else None] for _ in ids]


@pass_context
async def resolve_roles_for_company(ctx: Context, ids: StrList, options: DataDict) -> list[StrList]:
    user = get_graph_user(ctx)
    if not user or not can_view_coworkers_graph(user):
        return [[] for _ in ids]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        selectable = [role_table.c.id, role_table.c.company_id]
        filters = [
            role_table.c.company_id.in_(ids),
            role_table.c.status != RoleStatus.user_deleted,
        ]

        if options['tags'] is not None:
            tags_ids = options['tags']
            tags_aggregated = postgresql.array_agg(role_tag_table.c.tag_id)
            query = (
                sa.select(selectable)
                .select_from(
                    role_table.join(role_tag_table, role_tag_table.c.role_id == role_table.c.id)
                )
                .group_by(role_table.c.id)
                .where(sa.and_(*filters))
                .having(tags_aggregated.contains(tags_ids))
            )
        elif options['hasInvalidSignatures'] is True:
            # filter coworkers by exists of invalid signatures on docs
            filters.append(
                sa.exists([signature_table.c.id]).where(
                    sa.and_(
                        signature_table.c.role_id == role_table.c.id,
                        signature_table.c.is_valid.is_(False),
                    )
                )
            )
            query = sa.select(selectable).where(sa.and_(*filters))
        else:
            query = sa.select(selectable).where(sa.and_(*filters))

        roles = await select_all(conn, query)

    roles_map: defaultdict[str, set[str]] = defaultdict(set)
    for role in roles:
        roles_map[role.company_id].add(role.id)

    return [list(roles_map.get(company_id, [])) for company_id in ids]


@pass_context
async def resolve_current_sign_session(ctx: Context) -> OptionalStr:
    return get_graph_sign_session_id(ctx) or Nothing


@pass_context
async def resolve_current_user(ctx: Context) -> OptionalStr:
    user = get_base_graph_user(ctx)
    if not user:
        return Nothing

    return user.id


@pass_context
async def resolve_document(ctx: Context, options: StrDict) -> OptionalStr:
    user = get_wide_graph_user(ctx)
    if not user:
        return Nothing

    sign_session_id = get_graph_sign_session_id(ctx)

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        document_id = await select_document_id_for_graph(
            conn=conn,
            user=user,
            document_id=options['id'],
        )
        if not document_id:
            return await handle_resolve_document_not_found(
                conn=conn,
                options=options,
                user=user,
            )

    await schedule_jobs_on_resolve_document(
        document_id=document_id,
        sign_session_id=sign_session_id,
        user=user,
    )

    return document_id


@pass_context
async def resolve_documents(ctx: Context, options: DataDict) -> low_level_types.DocumentsList:
    user = get_wide_graph_user(ctx)
    if not user or not user.company_edrpou or not user.role_id:
        return low_level_types.DocumentsList(count=0, document_ids=())

    if not get_flag(FeatureFlags.ES_SEARCH):
        # We don't support resolving documents from DB
        return low_level_types.DocumentsList(count=0, document_ids=())

    count_document_ids, document_ids = await resolve_documents_from_es(ctx, options, user)
    return low_level_types.DocumentsList(
        count=count_document_ids,
        document_ids=tuple(document_ids),
    )


@pass_context
async def resolve_reviews(ctx: Context, ids: StrList, options: DataDict) -> list[StrList]:
    """When we have link from node (not from root) to node we
    should return a list of lists in the order in which we received `ids`.
    """
    user = get_graph_user(ctx)
    if not user:
        return [[] for _ in ids]

    company_edrpou = user.company_edrpou

    last_reviews_only = options.get('add_is_last_condition', False)
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        reviews = await select_reviews(
            conn=conn,
            company_edrpou=company_edrpou,
            document_ids=ids,
            last_reviews_only=last_reviews_only,
            with_cancellations=True,
        )

    reviews_map: defaultdict[str, StrList] = defaultdict(list)
    for r in reviews:
        reviews_map[r.document_id].append(r.id)

    return [reviews_map.get(document_id, []) for document_id in ids]


@pass_context
async def resolve_review_requests(ctx: Context, ids: StrList, options: DataDict) -> list[StrList]:
    user = get_graph_user(ctx)
    if not user:
        return [[] for _ in ids]

    company_id = user.company_id

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        review_requests = await select_review_requests(
            conn,
            company_id=company_id,
            document_ids=ids,
            is_all_requests=options.get('is_all_requests', False),
        )

    review_request_map: defaultdict[str, StrList] = defaultdict(list)
    for r in review_requests:
        review_request_map[r.document_id].append(r.id)

    return [review_request_map.get(document_id, []) for document_id in ids]


@pass_context
async def resolve_roles(ctx: Context, options: DataDict) -> list[OptionalStr]:
    user = get_graph_user(ctx)
    if not user:
        return []

    filters = [
        role_table.c.company_id == user.company_id,
        role_table.c.status == RoleStatus.active,
    ]

    if options.get('canSignAndRejectDocument') is not None:
        filters.append(
            sa.or_(
                role_table.c.user_role == UserRole.admin,
                role_table.c.can_sign_and_reject_document.is_(True),
            )
        )

    if options.get('canSignAndRejectDocumentExternal') is not None:
        filters.append(
            sa.or_(
                role_table.c.user_role == UserRole.admin,
                role_table.c.can_sign_and_reject_document_external.is_(True),
            )
        )
    if options.get('canSignAndRejectDocumentInternal') is not None:
        filters.append(
            sa.or_(
                role_table.c.user_role == UserRole.admin,
                role_table.c.can_sign_and_reject_document_internal.is_(True),
            )
        )

    if options.get('id') is not None:
        filters.append(role_table.c.id == options['id'])

    query = (
        sa.select([role_table.c.id]).where(sa.and_(*filters)).order_by(role_table.c.date_created)
    )

    if options['search']:
        search = '%{}%'.format(options['search'])
        query = query.select_from(
            role_table.join(user_table, (user_table.c.id == role_table.c.user_id))
        ).where(
            sa.or_(
                user_table.c.email.ilike(search),
                user_table.c.first_name.ilike(search),
                user_table.c.second_name.ilike(search),
                user_table.c.last_name.ilike(search),
            )
        )

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        return [role.id for role in await select_all(conn, query)]


@pass_context
async def resolve_signers(ctx: Context, ids: StrList) -> list[StrList]:
    user = get_graph_user(ctx)
    if not user:
        return [[] for _ in ids]

    query = sa.select([document_signer_table.c.id, document_signer_table.c.document_id]).where(
        sa.and_(
            document_signer_table.c.document_id.in_(ids),
            document_signer_table.c.company_id == user.company_id,
        )
    )

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        signers = await select_all(conn, query)

    signers_map: defaultdict[str, StrList] = defaultdict(list)
    for s in signers:
        signers_map[s.document_id].append(s.id)

    return [signers_map.get(document_id, []) for document_id in ids]


@pass_context
async def resolve_date_listing(
    ctx: Context,
    _: FieldList,
    documents_ids: list[str],
) -> list[list[datetime | Nothing]]:
    """
    Date listing for admin is date when first record in listing_table was written
    for given document and given company. Date listing for non admin users is date
    when access to document was given for this user.

    NOTE: Changing this logic don't forget about order_by clause in resolve_documents
    function
    """
    user = get_wide_graph_user(ctx)
    if not user:
        return [[Nothing] for _ in documents_ids]

    listing_dates_context = get_graph_context_dict__document_date_listing(ctx)

    mapping: dict[str, datetime] = {}
    for document_id in documents_ids:
        if listing_date := listing_dates_context.get(document_id):
            mapping[document_id] = listing_date

    db_ids = list(set(documents_ids) - set(mapping.keys()))
    if db_ids and get_flag(FeatureFlags.ENABLE_LISTING_DATE_FROM_ES):
        logger.warning(
            msg='Selecting listing dates from DB',
            extra={
                'db_ids': db_ids,
                'user_id': user.id,
                'listing_dates_context': listing_dates_context,
                'existing_mapping': mapping,
            },
        )
        # To catch that case earlier in tests
        if is_test_environ():
            raise ValueError('Some documents ids are not in listing_dates_context')

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        db_mapping = await get_document_listing_date_mapping(conn, db_ids, user)

    mapping.update(db_mapping)

    return [[mapping.get(document_id, Nothing)] for document_id in documents_ids]


@pass_context
async def resolve_is_contact_registered(
    ctx: Context, fields: FieldList, ids: StrList
) -> list[list[bool]]:
    query = (
        sa.select([contact_table.c.id])
        .select_from(
            contact_table.join(company_table, contact_table.c.edrpou == company_table.c.edrpou)
            .join(
                role_table,
                sa.and_(
                    role_table.c.company_id == company_table.c.id,
                    role_table.c.status == RoleStatus.active,
                ),
            )
            .join(
                user_table,
                sa.and_(
                    role_table.c.user_id == user_table.c.id,
                    user_table.c.registration_completed.is_(True),
                ),
            )
        )
        .where(contact_table.c.id.in_(ids))
    )

    async with ctx[DB_READONLY_KEY].acquire() as conn:
        contacts = await select_all(conn, query)

    contacts_map: dict[str, list[bool]] = {}
    for contact in contacts:
        contacts_map[contact.id] = [True]

    return [contacts_map.get(contact_id, [False]) for contact_id in ids]


@pass_context
async def resolve_2fa_enabled_by_rule(
    ctx: Context, fields: FieldList, ids: StrList
) -> list[list[bool]]:
    query = (
        sa.select(
            [
                sa.distinct(company_table.c.edrpou),
                role_table.c.user_id,
                user_table.c.phone,
            ]
        )
        .select_from(user_active_role_company_join)
        .where(role_table.c.user_id.in_(ids))
    )

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        rows = await select_all(conn, query)

        user_edrpou_map: defaultdict[str, StrList] = defaultdict(list)
        user_phone_map: dict[str, str] = {}
        user_edrpous: list[str] = []
        for row in rows:
            user_edrpous.append(row.edrpou)
            user_edrpou_map[row.user_id].append(row.edrpou)
            user_phone_map[row.user_id] = row.phone

        configs = await get_companies_configs_by_edrpous(
            conn=conn,
            companies_edrpous=user_edrpous,
        )

    result: dict[str, list[bool]] = {}
    for user_id, edrpous in user_edrpou_map.items():
        default_config = get_default_company_config()
        enabled_config = any(
            (configs.get(edrpou, default_config)).enable_2fa_for_internal_users
            for edrpou in edrpous
        )
        if enabled_config and two_factor.is_2fa_phone_valid(user_phone_map[user_id]):
            result[user_id] = [True]

    return [result.get(user_id, [False]) for user_id in ids]


@pass_context
async def resolve_count_unregistered_contacts(ctx: Context, fields: FieldList) -> IntList:
    user = get_graph_user(ctx)
    if not user:
        return [0]

    company_id = user.company_id
    async with ctx[DB_READONLY_KEY].acquire() as conn:
        return [await select_count_unregistered_contacts(conn, company_id)]


@pass_context
async def resolve_review_status(ctx: Context, fields: FieldList, ids: StrList) -> list[OptionalStr]:
    user = get_wide_graph_user(ctx)
    if not user or not user.company_edrpou:
        return [Nothing for _ in ids]

    company_edrpou = user.company_edrpou

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        document_versions = await get_latest_document_versions_available_for_company(
            conn,
            document_ids=ids,
            company_edrpou=company_edrpou,
        )

        query = sa.select(
            [
                review_status_table.c.status,
                review_status_table.c.document_id,
                review_status_table.c.document_version_id,
            ]
        ).where(
            sa.and_(
                review_status_table.c.document_id.in_(ids),
                review_status_table.c.edrpou == company_edrpou,
                sa.or_(
                    review_status_table.c.document_version_id.in_(
                        [v.id for v in document_versions]
                    ),
                    review_status_table.c.document_version_id.is_(None),
                ),
            )
        )

        review_statuses = await select_all(conn, query)

    review_status_map: dict[str, list[bool]] = {}
    for review_status in review_statuses:
        review_status_map[review_status.document_id] = [review_status.status.value]

    return [review_status_map.get(doc_id, [None]) for doc_id in ids]


@pass_context
async def resolve_review_status_for_version(
    ctx: Context, fields: FieldList, ids: StrList
) -> list[OptionalStr]:
    user = get_wide_graph_user(ctx)
    if not user:
        return [Nothing for _ in ids]

    company_edrpou = user.company_edrpou
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        query = sa.select(
            [
                review_status_table.c.status,
                review_status_table.c.document_version_id,
            ]
        ).where(
            sa.and_(
                review_status_table.c.document_version_id.in_(ids),
                review_status_table.c.edrpou == company_edrpou,
            )
        )

        review_statuses = await select_all(conn, query)

    review_status_map: dict[str, list[bool]] = {}
    for review_status in review_statuses:
        review_status_map[review_status.document_version_id] = [review_status.status.value]

    return [review_status_map.get(version_id, [None]) for version_id in ids]


@pass_context
async def resolve_document_recipient_fields(
    ctx: Context,
    fields: FieldList,
    documents_ids: StrList,
) -> list[list[OptionalStr]]:
    """
    Build query that select one recipient per document from "document_recipients" table.

    Historically we have stored recipients in "documents" table becouse we had only one recipient
    per document. Later we added support for multiple recipients and moved recipients to
    "document_recipients" table. In some places on UI we still need have code that expects
    that one recipients in "Document" model. This function is used to build query that select
    one recipient per document, which will be then injected into "Document" model of GraphQL
    object as "recipient_*" fields.
    """
    emails = document_recipients_table.c.emails

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        query = (
            sa.select(
                [
                    document_recipients_table.c.edrpou.label('edrpou_recipient'),
                    sa.func.array_to_string(emails, ', ').label('email_recipient'),
                    document_recipients_table.c.document_id,
                    document_recipients_table.c.is_emails_hidden,
                    document_table.c.edrpou_recipient.label('edrpou_recipient_old'),
                    document_table.c.email_recipient.label('email_recipient_old'),
                ]
            )
            .select_from(
                document_table.join(
                    document_recipients_table,
                    document_table.c.id == document_recipients_table.c.document_id,
                )
            )
            .where(
                sa.and_(
                    sa.and_(
                        # To make query fast we find all documents by IDs in "documents" table
                        # and only then join with "document_recipients" table. Not vice versa.
                        # It should work because number of IDs almost always is smaller than 250
                        # (max size of the page in UI), "documents" table is at least twice smaller
                        # than "document_recipients" table and filters on "document_recipients"
                        # table remove only small part of rows.
                        document_table.c.id.in_(documents_ids),
                        # Flow documents is "multilateral" documents that can have multiple
                        # recipients. We don't need to select recipient in "old" way for such
                        # documents because code that handle multilateral documents already
                        # select works with "document_recipients" table.
                        document_recipients_table.c.from_flow.is_(False),
                        # We are storing also owner of the document in "document_recipients" table
                        # along side with second recipient. Here we filter out owner of the document
                        # because "recipient_*" fields are used only for second recipient.
                        document_recipients_table.c.edrpou != document_table.c.edrpou_owner,
                    )
                )
            )
        )

        document_recipients = await select_all(conn, query)

    recipients_map = {}
    for recipient in document_recipients:
        recipients_map[recipient.document_id] = dict(recipient)

    # Build result in format that is expected by hiku. For each document we have list of values for
    # each field: [[doc1_email, doc2_edrpou, ...], [doc2_email, doc2_edrpou, ...], ...]
    results: list[list[OptionalStr]] = []
    for document_id in documents_ids:
        recipient = recipients_map.get(document_id, {})  # type: ignore[assignment]
        values: list[OptionalStr] = []

        # possible keys: edrpou_recipient, email_recipient, is_recipient_email_hidden
        for field in fields:
            _name: str = field.name
            value: OptionalStr = None
            if _name == 'edrpou_recipient':
                value = recipient.get('edrpou_recipient') or recipient.get('edrpou_recipient_old')
            elif _name == 'email_recipient':
                # Show email when is_emails_hidden is not True
                if recipient.get('is_emails_hidden') is not True:
                    value = recipient.get('email_recipient') or recipient.get('email_recipient_old')
            elif _name == 'is_recipient_email_hidden':
                value = recipient.get('is_emails_hidden')
            values.append(value)

        results.append(values)

    return results


@pass_context
async def resolve_document_recipients_emails(
    ctx: Context, _: FieldList, ids: StrList
) -> list[OptionalStr]:
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        recipients = await select_documents_recipients_by_ids(conn=conn, recipients_ids=ids)

    recipients_mapping = {recipient.id: recipient for recipient in recipients}

    result: list[OptionalStr] = []
    for recipient_id in ids:
        recipient = recipients_mapping.get(recipient_id)

        if recipient is None or recipient.is_emails_hidden:
            result.append([None])
        else:
            result.append([recipient.emails])

    return result


@pass_context
async def resolve_doc_children(ctx: Context, ids: StrList) -> list[list[tuple[str, str]]]:
    user = get_graph_user(ctx)
    if not user:
        return [[] for _ in ids]

    links_query = sa.select(
        [
            sa.distinct(document_link_table.c.child_id),
            document_link_table.c.parent_id,
            document_link_table.c.company_edrpou,
        ]
    ).where(document_link_table.c.parent_id.in_(ids))

    # TODO: check access using elastic

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        links = await select_all(conn, links_query)
        if links:
            docs_access = await select_all(
                conn,
                query=(
                    sa.select([listing_table.c.document_id])
                    .select_from(listing_table)
                    .where(
                        sa.and_(
                            listing_table.c.document_id.in_([link.child_id for link in links]),
                            get_documents_access_filters(user),
                        )
                    )
                ),
            )
            accessible_doc_ids = {doc.document_id for doc in docs_access}
            links = [link for link in links if link.child_id in accessible_doc_ids]

    link_map: defaultdict[str, list[tuple[str, str]]] = defaultdict(list)
    for link in links:
        link_map[link.parent_id].append((link.child_id, link.company_edrpou))

    return [link_map.get(doc_id, []) for doc_id in ids]


@pass_context
async def resolve_doc_parent(ctx: Context, ids: StrList) -> list[Nothing | tuple[str, str]]:
    user = get_graph_user(ctx)
    if not user:
        return [Nothing for _ in ids]

    links_query = sa.select(
        [
            document_link_table.c.parent_id,
            document_link_table.c.child_id,
            document_link_table.c.company_edrpou,
        ]
    ).where(document_link_table.c.child_id.in_(ids))

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        links = await select_all(conn, links_query)
        if links:
            docs_access = await select_all(
                conn,
                query=(
                    sa.select([listing_table.c.document_id])
                    .select_from(listing_table)
                    .where(
                        sa.and_(
                            listing_table.c.document_id.in_([link.parent_id for link in links]),
                            get_documents_access_filters(user),
                        )
                    )
                ),
            )
            accessible_doc_ids = {doc.document_id for doc in docs_access}
            links = [link for link in links if link.parent_id in accessible_doc_ids]

    link_map: dict[str, tuple[str | None, str]] = {}
    for link in links:
        link_map[link.child_id] = (link.parent_id, link.company_edrpou)

    return [link_map.get(doc_id, Nothing) for doc_id in ids]


@pass_context
async def resolve_review_setting(ctx: Context, ids: StrList) -> list[str | Nothing]:
    user = get_graph_user(ctx)
    if not user:
        return [Nothing for _ in ids]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        settings = await select_all(
            conn,
            (
                sa.select([review_setting_table.c.id, review_setting_table.c.document_id]).where(
                    sa.and_(
                        review_setting_table.c.document_id.in_(ids),
                        review_setting_table.c.company_id == user.company_id,
                    )
                )
            ),
        )

    setting_map: DataDict = {}
    for s in settings:
        setting_map[s.document_id] = s.id

    return [setting_map.get(doc_id, Nothing) for doc_id in ids]


@pass_context
async def resolve_tags_for_document_filter(ctx: Context) -> StrList:
    """Resolve tags visible in document filter for user"""
    user = get_graph_user(ctx)
    if not user:
        return []

    async with ctx[DB_READONLY_KEY].acquire() as conn:
        tags = await select_tags_for_document_filter(conn=conn, user=user)

    return unique_list_with_original_order(tags)


@pass_context
async def resolve_tag_access(ctx: Context, _: FieldList, tags_ids: StrList) -> list[list[bool]]:
    user = get_graph_user(ctx)
    if not user:
        return [[False] for _ in tags_ids]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        tags_access_filters = await get_tags_access_filter(conn, user)

        tags = await select_tags_by_ids(conn, tags_ids, filters=[tags_access_filters])

    tags_ids_with_access = {tag.id for tag in tags}
    return [[tag_id in tags_ids_with_access] for tag_id in tags_ids]


@pass_context
async def resolve_tags_by_documents(ctx: Context, ids_list: StrList) -> list[list[str]]:
    """Resolve document_tags.id list for given ids of documents"""
    user = get_graph_user(ctx)
    if not user:
        return [[] for _ in ids_list]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        tags = await select_tags_by_documents_for_graph(
            conn=conn,
            documents_ids=ids_list,
            company_id=user.company_id,
        )

    tags_map = defaultdict[str, list[str]](list)
    for tag in tags:
        tags_map[tag.document_id].append(tag.tag_id)

    return [tags_map.get(tag_id, []) for tag_id in ids_list]


@pass_context
async def resolve_tags_by_contacts(ctx: Context, contacts_ids: StrList) -> list[OptionalStr]:
    """Resolve contact_tag.id list for given ids of contacts"""
    user = get_graph_user(ctx)
    if not user:
        return [[] for _ in contacts_ids]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        tags_access_filters = await get_tags_access_filter(conn, user)

        tags = await select_tags_by_contacts(
            conn=conn,
            contacts_ids=contacts_ids,
            selectable=[contact_tag_table.c.contact_id, tag_table.c.id],
            filters=[tags_access_filters],
        )

    tags_map: defaultdict[str, StrList] = defaultdict(list)
    for tag in tags:
        tags_map[tag.contact_id].append(tag.id)

    return [tags_map.get(contact_id, []) for contact_id in contacts_ids]


@pass_context
async def resolve_tags_by_document_template(
    ctx: Context, document_templates_ids: StrList
) -> list[OptionalStr]:
    """Resolve document_template_tag.id list for given ids of contacts"""
    user = get_graph_user(ctx)
    if not user:
        return [[] for _ in document_templates_ids]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        tags_access_filters = await get_tags_access_filter(conn, user)

        tags = await select_tags_by_document_templates_ids(
            conn=conn,
            template_ids=document_templates_ids,
            selectable=[
                document_template_tag_table.c.document_templates_id,
                tag_table.c.id,
            ],
            filters=[tags_access_filters],
        )

    tags_map: defaultdict[str, StrList] = defaultdict(list)
    for tag in tags:
        tags_map[tag.document_templates_id].append(tag.id)

    return [tags_map.get(template_id, []) for template_id in document_templates_ids]


@pass_context
async def resolve_has_token(ctx: Context, fields: FieldList, role_ids: StrList) -> list[list[bool]]:
    user = get_graph_user(ctx)
    if not user:
        return [[False] for role_id in role_ids]

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        tokens = await select_tokens_for_graph(conn, role_ids, user)

    mapping = group_list(tokens, lambda t: t.role_id)
    return [[bool(mapping.get(role_id))] for role_id in role_ids]


@pass_context
async def resolve_doc_delete_request(ctx: Context, ids: StrList) -> list[str | Nothing]:
    user = get_base_graph_user(ctx)
    if not user:
        return [Nothing for _ in ids]

    query = (
        sa.select([delete_request_table.c.id, delete_request_table.c.document_id]).where(
            delete_request_table.c.document_id.in_(ids)
        )
    ).order_by(delete_request_table.c.date_rejected, delete_request_table.c.seqnum.asc())

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        delete_requests = await select_all(conn, query)

    delete_requests_map: DataDict = {}
    for request in delete_requests:
        delete_requests_map[request.document_id] = request.id
    return [delete_requests_map.get(doc_id, Nothing) for doc_id in ids]


@pass_context
async def resolve_doc_flows(ctx: Context, ids: StrList) -> list[list[str]]:
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        flows = await select_all(
            conn,
            (sa.select([doc_flow_table]).where(doc_flow_table.c.document_id.in_(ids))),
        )
    flows_map: defaultdict[str, StrList] = defaultdict(list)
    for flow in flows:
        flows_map[flow.document_id].append(flow.id)
    return [flows_map.get(doc_id, []) for doc_id in ids]


@pass_context
async def resolve_is_user_role_admin(ctx: Context, _: FieldList, ids: StrList) -> list[list[bool]]:
    user = get_graph_user(ctx)
    if not user:
        return [[False] for _ in ids]

    return [[user.user_role == UserRole.admin.value] for _ in ids]


@pass_context
async def resolve_flow_receivers(
    ctx: Context, _: FieldList, ids: StrList
) -> list[list[dict[str, list[str] | str]] | list[Nothing]]:
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        flow_recipients = await select_all(
            conn,
            (
                sa.select([document_recipients_table, doc_flow_table.c.id.label('flow_id')])
                .select_from(
                    document_recipients_table.join(
                        doc_flow_table,
                        doc_flow_table.c.receivers_id == document_recipients_table.c.id,
                    )
                )
                .where(
                    sa.and_(
                        doc_flow_table.c.id.in_(ids),
                        doc_flow_table.c.document_id == document_recipients_table.c.document_id,
                        document_recipients_table.c.from_flow.is_(True),
                    )
                )
            ),
        )
    flow_recipients_map: defaultdict[str, list[dict[str, str | list[str]]]] = defaultdict(list)
    for flow_recipient in flow_recipients:
        flow_recipients_map[flow_recipient.flow_id].append(
            {
                'edrpou': flow_recipient.edrpou,
                'emails': flow_recipient.emails or [],
                'is_emails_hidden': flow_recipient.is_emails_hidden,
            }
        )
    return [flow_recipients_map.get(flow_id, [Nothing]) for flow_id in ids]


@pass_context
async def resolve_banner_content(
    ctx: Context, _: FieldList, ids: StrList
) -> list[list[list[DataDict] | None]]:
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        banner_contents = await select_all(
            conn=conn,
            query=(
                sa.select([banner_content_table]).where(banner_content_table.c.banner_id.in_(ids))
            ),
        )
    content_map: defaultdict[str, list[DataDict]] = defaultdict(list)
    for content in banner_contents:
        content_map[content.banner_id].append(dict(content))
    return [[content_map.get(banner_id)] for banner_id in ids]


@pass_context
async def resolve_contact_person_email(
    ctx: Context, _: FieldList, ids: StrList
) -> list[list[OptionalStr]]:
    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        contacts = await select_all(
            conn,
            (sa.select([contact_person_table]).where(contact_person_table.c.id.in_(ids))),
        )

    contacts_mapping = {contact.id: contact for contact in contacts}

    result: list[list[OptionalStr]] = []
    for contact_id in ids:
        contact = contacts_mapping.get(contact_id)
        if contact is None or contact.is_email_hidden:
            result.append([None])
        else:
            result.append([contact.email])
    return result


async def resolve_links(fields: FieldList, ids: list[tuple[str, str]]) -> list[list[str]]:
    result = []
    for document_id, edrpou in ids:
        data = {'documentId': document_id, 'creatorEdrpou': edrpou}
        result.append([data[field.name] for field in fields])
    return result


@pass_context
async def resolve_document_required_fields(ctx: Context, options: DataDict) -> StrList:
    user = get_wide_graph_user(ctx)
    if not user:
        return []

    companies_ids = options.get('companies_ids') or []
    edrpous = options.get('edrpous') or []

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        if edrpous:
            companies = await select_companies_by_edrpou(
                conn=conn,
                edrpous=edrpous,
                selectable=[company_table.c.id],
            )
            companies_ids.extend([c.id for c in companies])

        if not companies_ids and user.company_id:
            companies_ids.append(user.company_id)

        if not companies_ids:
            return []

        required_fields = await get_document_required_fields_by_companies_ids(
            conn=conn, companies_ids=companies_ids
        )

    return [required_field.id for required_field in required_fields]


@pass_context
async def resolve_used_documents_count(
    ctx: Context, fields: FieldList, ids: StrList
) -> list[list[int]]:
    """
    Count all documents that company has.
    - inbox
    - outbox
    - internal

    Makes a single ES request to count documents for all companies at once.
    """
    if not ids:
        return [[]]

    # TODO: remove it if es works fine
    if get_flag(FeatureFlags.DISABLE_DOCUMENT_COUNTER):
        return [[0] for _ in ids]

    async with ctx[DB_READONLY_KEY].acquire() as conn:
        companies = await select_companies_by_ids(
            conn=conn,
            companies_ids=ids,
            selectable=[company_table.c.id, company_table.c.edrpou],
        )

    id_to_edrpou = {company.id: company.edrpou for company in companies}
    edrpous = list(id_to_edrpou.values())

    # Build a single query that counts documents per edrpou using terms aggregation
    es_query = (
        ctx[ES_KEY]
        .documents.search_query()
        .filter(Bool.must(Document.access_edrpou.in_(edrpous)))
        .aggs(
            counts_per_edrpou=Terms(
                field=Document.access_edrpou,
                size=len(edrpous),
            )
        )
        .size(0)
    )
    es_result = await fetch_es(query=es_query, source=ESQuerySource.graph)
    edrpou_counts = {
        bucket.key: bucket.doc_count
        for bucket in es_result.get_aggregation('counts_per_edrpou').buckets
    }

    return [[edrpou_counts.get(id_to_edrpou[company_id], 0)] for company_id in ids]


@pass_context
async def resolve_company_has_invalid_signed_docs(
    ctx: Context, fields: FieldList, ids: StrList
) -> list[list[bool]]:
    if not get_flag(FeatureFlags.INVALID_SIGNATURES):
        return [[False] for _ in ids]

    async with ctx[DB_READONLY_KEY].acquire() as conn:
        mapping = {}
        for company_id in ids:
            company = await select_company_by_id(conn, company_id)
            if not company:
                mapping[company_id] = False
                continue

            mapping[company_id] = await exists_invalid_signed_company_docs(conn, company.edrpou)

    return [[mapping.get(company_id, False)] for company_id in ids]


@pass_context
async def show_kep_app_popup(ctx: Context, fields: FieldList, ids: list[str]) -> list[list[bool]]:
    user = get_base_graph_user(ctx)
    if not user:
        return [[False]]

    extra = user.extra or {}
    if last_shown_str := cast(str, extra.get(KEP_POPUP_DATE_KEY)):
        try:
            last_shown_date = datetime.strptime(last_shown_str, KEP_POPUP_DATE_FORMAT).date()
        except (ValueError, TypeError):
            return [[True]]
        else:
            next_show = last_shown_date + timedelta(days=KEP_POPUP_TIMEOUT)
            return [[utc_now().date() > next_show]]

    return [[True]]


@pass_context
async def resolve_super_admin_permissions(
    ctx: Context, fields: FieldList, ids: StrList
) -> list[list[bool]]:
    user = get_graph_user(ctx)
    if not user:
        return [[False] for _ in ids]

    if ids == [user.role_id]:  # currentRole request
        permissions = get_graph_super_admin_permissions(ctx)
        return [[field.name in permissions for field in fields]]

    async with ctx[DB_READONLY_KEY].acquire() as conn:
        roles_sa_permissions = await get_super_admin_permissions_for_roles(conn, ids)
    return [
        [field.name in roles_sa_permissions.get(role_id, {}) for field in fields] for role_id in ids
    ]


@deprecated('DOC-5280: Remove after FE update. This is for backward-compatibility only')
@pass_context
async def resolve_is_super_admin(
    ctx: Context, _fields: FieldList, user_ids: list[str]
) -> list[list[bool]]:
    """Shows that user has ANY of super admin permissions set to True"""
    logger.warning('isSuperAdmin flag is deprecated')
    fields = dataclasses.fields(SuperAdminPermissions)
    super_admin_permissions = get_graph_super_admin_permissions(ctx)
    return [[any(field.name in super_admin_permissions for field in fields)]]


@pass_context
async def resolve_rate_extension_bill_document_id(
    ctx: Context, fields: FieldList, ids: StrList
) -> list[list[str]]:
    bills_map: dict[str, list[str]] = {}

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        rows = await select_bills_by_rate_extension_ids(conn, rate_extension_ids=ids)

    for row in rows:
        # {rate_extension.id : document_id}
        bills_map[row.id] = [row.document_id]

    return [bills_map.get(rate_extension_id, []) for rate_extension_id in ids]


@pass_context
async def resolve_active_surveys(
    ctx: Context,
    fields: FieldList,
    user_ids: list[Any],
) -> list[list[list[str]]]:
    """
    Return list of active surveys for current user.

    Return value: [[[survey1, survey2, ...] for _ in fields] for _ in user_ids]]
    """
    user = get_base_graph_user(ctx)
    if not user:
        return [[[]]]

    res = []
    for survey, redis_key in SURVEY_TO_REDIS_KEY.items():
        if user.email:
            is_member = await services.redis.sismember(redis_key, user.email)
            if is_member:
                res.append(survey)

    return [[res]]


@pass_context
async def resolve_latest_csat_survey(ctx: Context, *args: Any) -> list[list[str | Nothing]]:
    """
    Resolve date of latest user csat answer (or skip csat)
    """
    latest_csat_date = None

    user = get_base_graph_user(ctx)

    async with ctx[DB_ENGINE_KEY].acquire() as conn:
        latest_csats = await select_csat_surveys(conn, user_id=user.id, limit=1)  # type: ignore

    if latest_csats:
        latest_csat_date = latest_csats[0].date_created

    return [[latest_csat_date]]


@pass_context
async def resolve_coworker_user_phone(
    ctx: Context, fields: Any, user_ids: list[str]
) -> list[list[str | Nothing]]:
    """
    Resolve phone of CoworkerUser only if user is superadmin.
    Other users don't have access to CoworkerUser phone field.
    """

    user = check_sa_graph_user(ctx, required_permissions={'can_view_client_data'})
    if not user:
        return [[Nothing] for _ in user_ids]

    query = sa.select([user_table.c.id, user_table.c.phone]).where(user_table.c.id.in_(user_ids))

    async with ctx[DB_READONLY_KEY].acquire() as conn:
        rows = await select_all(conn, query)

    mapping: dict[str, str] = {row.id: row.phone for row in rows}
    return [[mapping.get(user_id)] for user_id in user_ids]
