from functools import partial

import hiku
from hiku.expr.core import S, if_
from hiku.graph import Field, Graph, GraphTransformer, Link, Node, Option, Root
from hiku.introspection.graphql import (
    QUERY_ROOT_NAME,
    _async_wrapper,
    type_name_field_func,
)
from hiku.sources.graph import SubGraph
from hiku.types import (
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    Integer,
    Optional,
    Record,
    Sequence,
    String,
    TypeRef,
)

from api.graph.data import LINKS
from api.graph.high_level.resolvers import (
    comment_is_rejection,
    delete_request_is_input,
    document_display_company_edrpou,
    document_display_company_email,
    document_display_company_name,
    document_is_imported,
    document_is_input,
    document_is_one_sign,
    document_status_text,
    enum_value,
    format_flow_receiver,
    get_banner_content_field,
    is_admin,
    is_flow_can_be_signed,
    is_flow_complete,
    is_fop,
    is_master_admin,
    is_not_none,
    is_user_registered,
    registration_referral_url,
    sign_session_url,
    unarchive_document_extension,
)
from api.graph.low_level import (
    app_resolvers,
    resolvers,
)
from api.graph.low_level.app_resolvers import (
    resolve_active_surveys,
    resolve_latest_csat_survey,
)
from api.graph.low_level.graph import LOW_LEVEL_GRAPH
from api.graph.low_level.resolvers import direct_link
from api.graph.root import ROOT
from app.auth import resolvers as auth_resolvers
from app.auth.resolvers import (
    has_role_permission,
    is_auth_phone_enabled,
    resolve_user_by_role_id,
)
from app.auth.session_manager.resolvers import resolve_login_sessions
from app.banner import resolvers as banner_resolvers
from app.billing import resolvers as billing_resolvers
from app.directories.resolvers import resolve_directory_parents_list, resolve_documents_directories
from app.document_antivirus.resolvers import (
    resolve_antivirus_checks_for_document,
    resolve_antivirus_checks_for_document_version,
)
from app.document_automation import resolvers as document_automation_resolvers
from app.document_automation.resolvers import resolve_automation_condition_involved_companies
from app.document_revoke.resolvers import (
    resolve_document_revoke_signatures,
    resolve_documents_revokes,
)
from app.document_versions.resolvers import resolve_document_versions_for_document
from app.documents import resolvers as documents_resolvers
from app.documents_fields import resolvers as documents_fields_resolvers
from app.drafts.resolvers import draft_date_scheduled_deletion, resolve_drafts_for_document
from app.flow import resolvers as flows_resolvers
from app.groups.resolvers import (
    resolve_documents_pending_group_reviewer,
    resolve_documents_pending_group_signer,
    resolve_group_members_by_group,
    resolve_template_with_group,
    resolve_viewer_groups_for_documents,
)
from app.mobile.resolvers import resolve_has_active_mobile_app, resolve_has_mobile_app
from app.tags import resolvers as tags_resolvers
from app.templates.resolvers import (
    resolve_is_favorite,
    resolve_preview_url,
    template_creator_role_id,
)
from app.trigger_notifications import resolvers as trigger_notifications

BILLING_ACCOUNT_SG = SubGraph(LOW_LEVEL_GRAPH, 'BillingAccount')
BILLING_TRANSACTION_SG = SubGraph(LOW_LEVEL_GRAPH, 'BillingTransaction')
BILL_SRC = SubGraph(LOW_LEVEL_GRAPH, 'Bill')
BILL_SERVICE_SG = SubGraph(LOW_LEVEL_GRAPH, 'BillService')
BILLING_COMPANY_CONFIG_SG = SubGraph(LOW_LEVEL_GRAPH, 'BillingCompanyConfig')
BONUS_SG = SubGraph(LOW_LEVEL_GRAPH, 'Bonus')
COMPANY_RATE_SG = SubGraph(LOW_LEVEL_GRAPH, 'CompanyRate')
RATE_EXTENSION_SG = SubGraph(LOW_LEVEL_GRAPH, 'RateExtension')
COMMENT_SG = SubGraph(LOW_LEVEL_GRAPH, 'Comment')
COMPANY_SG = SubGraph(LOW_LEVEL_GRAPH, 'Company')
CONTACT_SG = SubGraph(LOW_LEVEL_GRAPH, 'Contact')
CONTACT_PERSON_SG = SubGraph(LOW_LEVEL_GRAPH, 'ContactPerson')
CONTACT_PERSON_PHONE_SG = SubGraph(LOW_LEVEL_GRAPH, 'ContactPersonPhone')
DOCUMENT_SG = SubGraph(LOW_LEVEL_GRAPH, 'Document')
DOCUMENT_ACCESS_SG = SubGraph(LOW_LEVEL_GRAPH, 'DocumentAccess')
TAG_SG = SubGraph(LOW_LEVEL_GRAPH, 'Tag')
DOCUMENT_SIGNER_SG = SubGraph(LOW_LEVEL_GRAPH, 'DocumentSigner')
REVIEW_SG = SubGraph(LOW_LEVEL_GRAPH, 'Review')
REVIEW_REQUEST_SG = SubGraph(LOW_LEVEL_GRAPH, 'ReviewRequest')
REVIEW_SETTING_SG = SubGraph(LOW_LEVEL_GRAPH, 'ReviewSetting')
ROLE_SG = SubGraph(LOW_LEVEL_GRAPH, 'Role')
SIGN_SESSION_SG = SubGraph(LOW_LEVEL_GRAPH, 'SignSession')
SIGNATURE_SG = SubGraph(LOW_LEVEL_GRAPH, 'Signature')
USER_SG = SubGraph(LOW_LEVEL_GRAPH, 'User')
USER_ONBOARDING_SG = SubGraph(LOW_LEVEL_GRAPH, 'UserOnboarding')
USER_META_SG = SubGraph(LOW_LEVEL_GRAPH, 'UserMeta')
DELETE_REQUEST_SG = SubGraph(LOW_LEVEL_GRAPH, 'DeleteRequest')
DOCUMENTS_FLOW_SG = SubGraph(LOW_LEVEL_GRAPH, 'DocumentFlow')
DOCUMENT_RECIPIENT_SG = SubGraph(LOW_LEVEL_GRAPH, 'DocumentRecipient')
DOCUMENTS_FIELD_SG = SubGraph(LOW_LEVEL_GRAPH, 'DocumentsField')
DOCUMENT_CATEGORY_SG = SubGraph(LOW_LEVEL_GRAPH, 'DocumentCategory')
DOCUMENTS_PARAMETER_SG = SubGraph(LOW_LEVEL_GRAPH, 'DocumentParameter')
DOCUMENT_AUTOMATION_TEMPLATE_SG = SubGraph(LOW_LEVEL_GRAPH, 'DocumentAutomationTemplate')
DOCUMENT_AUTOMATION_CONDITION_SG = SubGraph(LOW_LEVEL_GRAPH, 'DocumentAutomationCondition')
TRIGGER_NOTIFICATION_SG = SubGraph(LOW_LEVEL_GRAPH, 'TriggerNotification')
BANNER_SG = SubGraph(LOW_LEVEL_GRAPH, 'Banner')
BANNER_CONTENT_SG = SubGraph(LOW_LEVEL_GRAPH, 'BannerContent')
DOCUMENT_VERSION_SG = SubGraph(LOW_LEVEL_GRAPH, 'DocumentVersion')
DOCUMENT_REQUIRED_FIELD_SG = SubGraph(LOW_LEVEL_GRAPH, 'DocumentRequiredField')
ANTIVIRUS_CHECK_SG = SubGraph(LOW_LEVEL_GRAPH, 'AntivirusCheck')
DRAFT_ANTIVIRUS_CHECK_SG = SubGraph(LOW_LEVEL_GRAPH, 'DraftAntivirusCheck')
CLOUD_SIGNER_SG = SubGraph(LOW_LEVEL_GRAPH, 'CloudSigner')
DOCUMENT_META_SG = SubGraph(LOW_LEVEL_GRAPH, 'DocumentMeta')
GROUP_SG = SubGraph(LOW_LEVEL_GRAPH, 'Group')
GROUP_MEMBER_SG = SubGraph(LOW_LEVEL_GRAPH, 'GroupMember')
GROUP_DOCUMENT_ACCESS_SG = SubGraph(LOW_LEVEL_GRAPH, 'GroupDocumentAccess')
CONTACT_RECIPIENT_SG = SubGraph(LOW_LEVEL_GRAPH, 'ContactRecipient')
DRAFT_SG = SubGraph(LOW_LEVEL_GRAPH, 'Draft')
LOGIN_SESSION_SG = SubGraph(LOW_LEVEL_GRAPH, 'LoginSession')
TEMPLATE_SG = SubGraph(LOW_LEVEL_GRAPH, 'Template')
DIRECTORY_SG = SubGraph(LOW_LEVEL_GRAPH, 'DocumentDirectory')
DOCUMENT_REVOKE_SG = SubGraph(LOW_LEVEL_GRAPH, 'DocumentRevoke')
DOCUMENT_REVOKE_SIGNATURE_SG = SubGraph(LOW_LEVEL_GRAPH, 'DocumentRevokeSignature')

# List of basic fields that can be safely used in different contexts
# without leaking user-sensitive data. Use it to show basic information about
# coworker or recipient.
BASE_USERS_FIELDS = [
    Field('id', String, USER_SG.c(S.this.id)),
    Field('email', String, USER_SG.c(S.this.email)),
    Field('firstName', Optional[String], USER_SG.c(S.this.first_name)),
    Field('secondName', Optional[String], USER_SG.c(S.this.second_name)),
    Field('lastName', Optional[String], USER_SG.c(S.this.last_name)),
]

ROLE_FIELDS = [
    Field('id', String, ROLE_SG.c(S.this.id)),
    Field('companyId', String, ROLE_SG.c(S.this.company_id)),
    Field('companyEdrpou', String, ROLE_SG.c(S.this.company_edrpou)),
    Field('userId', String, ROLE_SG.c(S.this.user_id)),
    Field('userRole', Integer, ROLE_SG.c(S.this.user_role)),
    Field('canViewDocument', Boolean, ROLE_SG.c(S.this.can_view_document)),
    Field('canCommentDocument', Boolean, ROLE_SG.c(S.this.can_comment_document)),
    Field('canUploadDocument', Boolean, ROLE_SG.c(S.this.can_upload_document)),
    Field('canDownloadDocument', Boolean, ROLE_SG.c(S.this.can_download_document)),
    Field('canPrintDocument', Boolean, ROLE_SG.c(S.this.can_print_document)),
    Field('canDeleteDocument', Boolean, ROLE_SG.c(S.this.can_delete_document)),
    Field('canArchiveDocuments', Boolean, ROLE_SG.c(S.this.can_archive_documents)),
    Field('canEditTemplates', Boolean, ROLE_SG.c(S.this.can_edit_templates)),
    Field('canEditDirectories', Boolean, ROLE_SG.c(S.this.can_edit_directories)),
    Field(
        'canRemoveItselfFromApproval', Boolean, ROLE_SG.c(S.this.can_remove_itself_from_approval)
    ),
    Field('canDeleteArchivedDocuments', Boolean, ROLE_SG.c(S.this.can_delete_archived_documents)),
    Field(
        'canSignAndRejectDocument',
        Boolean,
        ROLE_SG.c(S.this.can_sign_and_reject_document),
    ),
    Field(
        'canSignAndRejectDocumentExternal',
        Boolean,
        ROLE_SG.c(S.this.can_sign_and_reject_document_external),
    ),
    Field(
        'canSignAndRejectDocumentInternal',
        Boolean,
        ROLE_SG.c(S.this.can_sign_and_reject_document_internal),
    ),
    Field('canInviteCoworkers', Boolean, ROLE_SG.c(S.this.can_invite_coworkers)),
    Field(
        'canChangeDocumentSignersAndReviewers',
        Boolean,
        ROLE_SG.c(S.this.can_change_document_signers_and_reviewers),
    ),
    Field('canDeleteDocumentExtended', Boolean, ROLE_SG.c(S.this.can_delete_document_extended)),
    Field('canDownloadActions', Boolean, ROLE_SG.c(S.this.can_download_actions)),
    Field('canEditCompanyContact', Boolean, ROLE_SG.c(S.this.can_edit_company_contact)),
    Field('canEditRequiredFields', Boolean, ROLE_SG.c(S.this.can_edit_required_fields)),
    Field('canEditSecurity', Boolean, ROLE_SG.c(S.this.can_edit_security)),
    Field('canViewPrivateDocument', Boolean, ROLE_SG.c(S.this.can_view_private_document)),
    Field('hasSignedDocuments', Boolean, ROLE_SG.c(S.this.has_signed_documents)),
    Field('canEditCompany', Boolean, ROLE_SG.c(S.this.can_edit_company)),
    Field('canEditRoles', Boolean, ROLE_SG.c(S.this.can_edit_roles)),
    Field('canCreateTags', Boolean, ROLE_SG.c(S.this.can_create_tags)),
    Field(
        'canEditDocumentTemplates',
        Boolean,
        ROLE_SG.c(S.this.can_edit_document_automation),
    ),
    Field('canEditDocumentFields', Boolean, ROLE_SG.c(S.this.can_edit_document_fields)),
    Field('canEditDocumentCategory', Boolean, ROLE_SG.c(S.this.can_edit_document_category)),
    Field('canReceiveInbox', Boolean, ROLE_SG.c(S.this.can_receive_inbox)),
    Field('canReceiveInboxAsDefault', Boolean, ROLE_SG.c(S.this.can_receive_inbox_as_default)),
    Field('canReceiveComments', Boolean, ROLE_SG.c(S.this.can_receive_comments)),
    Field('canReceiveRejects', Boolean, ROLE_SG.c(S.this.can_receive_rejects)),
    Field('canReceiveReminders', Boolean, ROLE_SG.c(S.this.can_receive_reminders)),
    Field('canReceiveReviews', Boolean, ROLE_SG.c(S.this.can_receive_reviews)),
    Field(
        'canReceiveReviewProcessFinished',
        Boolean,
        ROLE_SG.c(S.this.can_receive_review_process_finished),
    ),
    Field(
        'canReceiveReviewProcessFinishedAssigner',
        Boolean,
        ROLE_SG.c(S.this.can_receive_review_process_finished_assigner),
    ),
    Field(
        'canReceiveSignProcessFinished',
        Boolean,
        ROLE_SG.c(S.this.can_receive_sign_process_finished),
    ),
    Field(
        'canReceiveSignProcessFinishedAssigner',
        Boolean,
        ROLE_SG.c(S.this.can_receive_sign_process_finished_assigner),
    ),
    Field('canReceiveNotifications', Boolean, ROLE_SG.c(S.this.can_receive_notifications)),
    Field('canReceiveAccessToDoc', Boolean, ROLE_SG.c(S.this.can_receive_access_to_doc)),
    Field(
        'canReceiveDeleteRequests',
        Boolean,
        ROLE_SG.c(S.this.can_receive_delete_requests),
    ),
    Field(
        'canReceiveFinishedDocs',
        Boolean,
        ROLE_SG.c(S.this.can_receive_finished_docs),
    ),
    Field('canReceiveNewRoles', Boolean, ROLE_SG.c(S.this.can_receive_new_roles)),
    Field('canReceiveTokenExpiration', Boolean, ROLE_SG.c(S.this.can_receive_token_expiration)),
    Field('canReceiveEmailChange', Boolean, ROLE_SG.c(S.this.can_receive_email_change)),
    Field('canViewCoworkers', Boolean, ROLE_SG.c(S.this.can_view_coworkers)),
    Field(
        'canReceiveAdminRoleDeletion', Boolean, ROLE_SG.c(S.this.can_receive_admin_role_deletion)
    ),
    Field('sortDocuments', String, ROLE_SG.c(enum_value(S.this.sort_documents))),
    Field('showInviteTooltip', Boolean, ROLE_SG.c(S.this.show_invite_tooltip)),
    Field('showChildDocuments', Boolean, ROLE_SG.c(S.this.show_child_documents)),
    Field('position', Optional[String], ROLE_SG.c(S.this.position)),
    Field('status', String, ROLE_SG.c(enum_value(S.this.status))),
    Field('allowedIps', Sequence[String], ROLE_SG.c(S.this.allowed_ips)),
    Field('allowedApiIps', Optional[Sequence[String]], ROLE_SG.c(S.this.allowed_api_ips)),
    Field('dateCreated', String, ROLE_SG.c(S.this.date_created)),
    Field('dateUpdated', String, ROLE_SG.c(S.this.date_updated)),
    Field('dateDeleted', Optional[String], ROLE_SG.c(S.this.date_deleted)),
    Field('deletedBy', Optional[String], ROLE_SG.c(S.this.deleted_by)),
    Field('invitedBy', Optional[String], ROLE_SG.c(S.this.invited_by)),
    Field('activatedBy', Optional[String], ROLE_SG.c(S.this.activated_by)),
    Field('activationSource', Optional[String], ROLE_SG.c(enum_value(S.this.activation_source))),
    Field('dateAgreed', String, ROLE_SG.c(S.this.date_agreed)),
    Field('dateInvited', Optional[String], ROLE_SG.c(S.this.date_invited)),
    Field('dateActivated', Optional[String], ROLE_SG.c(S.this.date_activated)),
    Field('isDefaultRecipient', Boolean, ROLE_SG.c(S.this.is_default_recipient)),
    Field('isAdmin', Boolean, ROLE_SG.c(is_admin(S.this.user_role))),
    Field(
        'isMasterAdmin',
        Boolean,
        ROLE_SG.c(
            is_master_admin(
                S.this.user_role,
                S.this.company_edrpou,
                S.this.company_config,
            )
        ),
    ),
    Field('hasFewSignatures', Boolean, ROLE_SG.c(S.this.has_few_signatures)),
    Field('hasFewReviews', Boolean, ROLE_SG.c(S.this.has_few_reviews)),
    # TODO: DOC-5280: Remove after FE update. This is for backward-compatibility only
    Field(
        'isSuperAdmin',
        Boolean,
        ROLE_SG.c(S.this.is_super_admin),
    ),
    Field(
        'canViewClientData',
        Boolean,
        ROLE_SG.c(S.this.can_view_client_data),
    ),
    Field(
        'canEditClientData',
        Boolean,
        ROLE_SG.c(S.this.can_edit_client_data),
    ),
    Field(
        'canEditSpecialFeatures',
        Boolean,
        ROLE_SG.c(S.this.can_edit_special_features),
    ),
    Field(
        'registrationReferralUrl',
        String,
        ROLE_SG.c(if_(S.this.company.is_dealer, registration_referral_url(S.this.id), '')),
    ),
]


# List of basic fields that can be safely used in different contexts without leaking
# company-sensitive data.
BASE_COMPANY_FIELDS = [
    Field('id', Optional[String], COMPANY_SG.c(S.this.id)),
    Field('name', Optional[String], COMPANY_SG.c(S.this.name)),
    Field('edrpou', String, COMPANY_SG.c(S.this.edrpou)),
]


def _build_role_user_link(type_: str) -> list[hiku.graph.AbstractBase]:
    """
    Minimal set of fields for role with a link to the user. You can use this function
    if you need to quickly build SomeRole -> SomeUser link.
    """
    return [
        Field('id', String, ROLE_SG.c(S.this.id)),
        Field('userId', String, ROLE_SG.c(S.this.user_id)),
        Field('position', Optional[String], ROLE_SG.c(S.this.position)),
        Link('user', TypeRef[type_], resolvers.direct_link, requires='userId'),
    ]


HIGH_LEVEL_GRAPH = Graph(
    [
        # Auth models
        Node(
            # WARNING: be aware that this node contains sensitive information, so don't use
            #  in the context of a recipient company. Use "CommentCompany", "DraftCompany",
            #  "CompanyDocumentOwner", etc. or create your own company nodes that have fewer fields
            #  and links to avoid leaking sensitive information.
            'Company',
            [
                Field('id', String, COMPANY_SG.c(S.this.id)),
                Field('edrpou', String, COMPANY_SG.c(S.this.edrpou)),
                Field('ipn', String, COMPANY_SG.c(S.this.ipn)),
                Field('name', Optional[String], COMPANY_SG.c(S.this.name)),
                Field('fullName', Optional[String], COMPANY_SG.c(S.this.full_name)),
                Field('isLegal', Boolean, COMPANY_SG.c(S.this.is_legal)),
                Field(
                    'renderSignatureInInterface',
                    Boolean,
                    COMPANY_SG.c(S.this.render_signature_in_interface),
                ),
                Field(
                    'renderSignatureOnPrintDocument',
                    Boolean,
                    COMPANY_SG.c(S.this.render_signature_on_print_document),
                ),
                Field(
                    'allowUnregisteredDocumentView',
                    Boolean,
                    COMPANY_SG.c(S.this.allow_unregistered_document_view),
                ),
                Field(
                    'activityField',
                    Optional[String],
                    COMPANY_SG.c(S.this.activity_field),
                ),
                Field(
                    'employeesNumber',
                    Optional[String],
                    COMPANY_SG.c(S.this.employees_number),
                ),
                Field('phone', Optional[String], COMPANY_SG.c(S.this.phone)),
                Field('dateCreated', String, COMPANY_SG.c(S.this.date_created)),
                Field('dateUpdated', String, COMPANY_SG.c(S.this.date_updated)),
                Field('config', Record, app_resolvers.resolve_companies_config),
                Field('activeRates', Sequence[String], app_resolvers.resolve_active_rates),
                Field(
                    'usedDocumentCount',
                    Integer,
                    app_resolvers.resolve_used_documents_count,
                ),
                Field(
                    'hasInvalidSignedDocuments',
                    Boolean,
                    app_resolvers.resolve_company_has_invalid_signed_docs,
                ),
                Field('isFop', Boolean, COMPANY_SG.c(is_fop(S.this.edrpou))),
                Field('emailDomains', Sequence[String], COMPANY_SG.c(S.this.email_domains)),
                Field('allowedIps', Sequence[String], COMPANY_SG.c(S.this.allowed_ips)),
                Field('allowedApiIps', Sequence[String], COMPANY_SG.c(S.this.allowed_api_ips)),
                Field(
                    'inactivityTimeout', Optional[Integer], COMPANY_SG.c(S.this.inactivity_timeout)
                ),
                Link(
                    'billingAccounts',
                    Sequence[TypeRef['BillingAccount']],
                    app_resolvers.resolve_billing_accounts,
                    requires='id',
                ),
                Link(
                    'bills',
                    Sequence[TypeRef['Bill']],
                    billing_resolvers.resolve_companies_bills,
                    requires='id',
                ),
                Link(
                    'rates',
                    Sequence[TypeRef['CompanyRate']],
                    app_resolvers.resolve_company_rates,
                    requires='id',
                ),
                Link(
                    'rateExtensions',
                    Sequence[TypeRef['RateExtension']],
                    app_resolvers.resolve_company_rate_extensions,
                    requires='id',
                ),
                Link(
                    'trialRates',
                    Sequence[TypeRef['CompanyRate']],
                    app_resolvers.resolve_company_trial_rates,
                    requires='id',
                ),
                Link(
                    'roles',
                    Sequence[TypeRef['CoworkerRole']],
                    app_resolvers.resolve_roles_for_company,
                    options=[
                        Option('tags', Optional[Sequence[String]], default=None),
                        Option('hasInvalidSignatures', Optional[Boolean], default=None),
                    ],
                    requires='id',
                ),
                Link(
                    name='billingCompanyConfig',
                    type_=TypeRef['BillingCompanyConfig'],
                    func=billing_resolvers.resolve_billing_company_config,
                    requires='id',
                ),
            ],
        ),
        Node(
            'CommentCompany',
            [
                Field('id', String, COMPANY_SG.c(S.this.id)),
                Field('edrpou', String, COMPANY_SG.c(S.this.edrpou)),
                Field('isLegal', Boolean, COMPANY_SG.c(S.this.is_legal)),
            ],
        ),
        Node('CompanyDocumentOwner', BASE_COMPANY_FIELDS),
        Node('CompanyDocumentRecipient', BASE_COMPANY_FIELDS),
        Node('VersionCompany', BASE_COMPANY_FIELDS),
        Node('DraftCompany', BASE_COMPANY_FIELDS),
        Node('DocumentRequiredFieldCompany', BASE_COMPANY_FIELDS),
        Node('TemplateCompany', BASE_COMPANY_FIELDS),
        Node('AutomationConditionCompany', BASE_COMPANY_FIELDS),
        Node('DocumentAvailableCompany', BASE_COMPANY_FIELDS),
        Node('DocumentRevokeCompany', BASE_COMPANY_FIELDS),
        Node(
            'Review',
            [
                Field('id', String, REVIEW_SG.c(S.this.id)),
                Field('documentId', String, REVIEW_SG.c(S.this.document_id)),
                Field('roleId', String, REVIEW_SG.c(S.this.role_id)),
                Field('groupId', String, REVIEW_SG.c(S.this.group_id)),
                Field(
                    'documentVersionId',
                    Optional[String],
                    REVIEW_SG.c(S.this.document_version_id),
                ),
                Field('type', String, REVIEW_SG.c(enum_value(S.this.type))),
                Field('userEmail', String, REVIEW_SG.c(S.this.user_email)),
                Field('dateCreated', String, REVIEW_SG.c(S.this.date_created)),
                Link(
                    'role',
                    TypeRef['ReviewRole'],
                    resolvers.direct_link,
                    requires='roleId',
                ),
                Link(
                    'group',
                    TypeRef['Group'],
                    resolvers.direct_link,
                    requires='groupId',
                ),
                Link(
                    'documentVersion',
                    Optional[TypeRef['DocumentVersion']],
                    resolvers.direct_link,
                    requires='documentVersionId',
                ),
            ],
        ),
        Node(
            'ReviewRequest',
            [
                Field('id', String, REVIEW_REQUEST_SG.c(S.this.id)),
                Field('documentId', String, REVIEW_REQUEST_SG.c(S.this.document_id)),
                Field(
                    'documentVersionId',
                    String,
                    REVIEW_REQUEST_SG.c(S.this.document_version_id),
                ),
                Field('fromRoleId', String, REVIEW_REQUEST_SG.c(S.this.from_role_id)),
                Field('toRoleId', String, REVIEW_REQUEST_SG.c(S.this.to_role_id)),
                Field('toGroupId', String, REVIEW_REQUEST_SG.c(S.this.to_group_id)),
                Field('status', String, REVIEW_REQUEST_SG.c(enum_value(S.this.status))),
                Field('order', Optional[Integer], REVIEW_REQUEST_SG.c(S.this.order)),
                Field('dateCreated', String, REVIEW_REQUEST_SG.c(S.this.date_created)),
                Field('dateUpdated', String, REVIEW_REQUEST_SG.c(S.this.date_updated)),
                Link(
                    'toGroup',
                    TypeRef['Group'],
                    resolvers.direct_link,
                    requires='toGroupId',
                ),
                Link(
                    'fromRole',
                    TypeRef['ReviewRequestRole'],
                    resolvers.direct_link,
                    requires='fromRoleId',
                ),
                Link(
                    'toRole',
                    TypeRef['ReviewRequestRole'],
                    resolvers.direct_link,
                    requires='toRoleId',
                ),
                Link(
                    'documentVersion',
                    Optional[TypeRef['DocumentVersion']],
                    resolvers.direct_link,
                    requires='documentVersionId',
                ),
            ],
        ),
        Node(
            'ReviewSetting',
            [
                Field('id', String, REVIEW_SETTING_SG.c(S.this.id)),
                Field('documentId', String, REVIEW_SETTING_SG.c(S.this.document_id)),
                Field('companyId', String, REVIEW_SETTING_SG.c(S.this.company_id)),
                Field('isRequired', Boolean, REVIEW_SETTING_SG.c(S.this.is_required)),
                Field('isParallel', Boolean, REVIEW_SETTING_SG.c(S.this.is_parallel)),
                Field('dateCreated', String, REVIEW_SETTING_SG.c(S.this.date_created)),
                Field('dateUpdated', String, REVIEW_SETTING_SG.c(S.this.date_updated)),
            ],
        ),
        Node(
            # WARNING: be aware that this node contains sensitive information, so don't use
            # in the context of a recipient company. Use "CommentRole", "CoworkerRole",
            # "SignerRole", etc. or create your own role nodes that have fewer fields and
            # links to avoid leaking sensitive information.
            'Role',
            [
                *ROLE_FIELDS,
                Link(
                    'company',
                    TypeRef['Company'],
                    resolvers.direct_link,
                    requires='companyId',
                ),
                Link('user', TypeRef['User'], resolvers.direct_link, requires='userId'),
            ],
        ),
        Node(
            'CoworkerRole',
            [
                *ROLE_FIELDS,
                Field('hasToken', Boolean, app_resolvers.resolve_has_token),
                Link(
                    'tags',
                    Sequence[TypeRef['Tag']],
                    tags_resolvers.resolve_tags_by_roles,
                    requires='id',
                ),
                Link(
                    'fields',
                    Sequence[TypeRef['DocumentsField']],
                    documents_fields_resolvers.resolve_fields_by_roles,
                    requires='id',
                ),
                Link(
                    'user',
                    TypeRef['CoworkerUser'],
                    resolvers.direct_link,
                    requires='userId',
                ),
            ],
        ),
        Node(
            'SignerRole',
            [
                Field(
                    'canSignAndRejectDocument',
                    Boolean,
                    ROLE_SG.c(
                        has_role_permission(
                            S.this.can_sign_and_reject_document,
                            S.this.user_role,
                        )
                    ),
                ),
                Field(
                    'canSignAndRejectDocumentExternal',
                    Boolean,
                    ROLE_SG.c(
                        has_role_permission(
                            S.this.can_sign_and_reject_document_external,
                            S.this.user_role,
                        )
                    ),
                ),
                Field(
                    'canSignAndRejectDocumentInternal',
                    Boolean,
                    ROLE_SG.c(
                        has_role_permission(
                            S.this.can_sign_and_reject_document_internal,
                            S.this.user_role,
                        )
                    ),
                ),
                *_build_role_user_link('SignerUser'),
            ],
        ),
        Node('ReviewRole', _build_role_user_link('ReviewUser')),
        Node('ReviewRequestRole', _build_role_user_link('ReviewRequestUser')),
        Node(
            'DocumentAutomationTemplateRole',
            [
                Field(
                    'canSignAndRejectDocument',
                    Boolean,
                    ROLE_SG.c(
                        has_role_permission(
                            S.this.can_sign_and_reject_document,
                            S.this.user_role,
                        )
                    ),
                ),
                Field(
                    'canSignAndRejectDocumentExternal',
                    Boolean,
                    ROLE_SG.c(
                        has_role_permission(
                            S.this.can_sign_and_reject_document_external,
                            S.this.user_role,
                        )
                    ),
                ),
                Field(
                    'canSignAndRejectDocumentInternal',
                    Boolean,
                    ROLE_SG.c(
                        has_role_permission(
                            S.this.can_sign_and_reject_document_internal,
                            S.this.user_role,
                        )
                    ),
                ),
                *_build_role_user_link('DocumentAutomationTemplateUser'),
            ],
        ),
        Node(
            'CommentRole',
            [
                Field('id', String, ROLE_SG.c(S.this.id)),
                Field('userId', String, ROLE_SG.c(S.this.user_id)),
                Field('companyId', String, ROLE_SG.c(S.this.company_id)),
                Link(
                    'user',
                    TypeRef['CommentUser'],
                    resolvers.direct_link,
                    requires='userId',
                ),
                Link(
                    'company',
                    TypeRef['CommentCompany'],
                    resolvers.direct_link,
                    requires='companyId',
                ),
            ],
        ),
        Node(
            'SignSessionRole',
            [
                Field('status', String, ROLE_SG.c(enum_value(S.this.status))),
                *_build_role_user_link('SignSessionUser'),
            ],
        ),
        Node(
            'VersionRole',
            [
                Field('companyId', String, ROLE_SG.c(S.this.company_id)),
                Link(
                    'company',
                    TypeRef['VersionCompany'],
                    resolvers.direct_link,
                    requires='companyId',
                ),
                *_build_role_user_link('VersionUser'),
            ],
        ),
        Node(
            'DraftRole',
            [
                Field('companyId', String, ROLE_SG.c(S.this.company_id)),
                Link(
                    'company',
                    TypeRef['DraftCompany'],
                    resolvers.direct_link,
                    requires='companyId',
                ),
                *_build_role_user_link('DraftUser'),
            ],
        ),
        Node(
            'TemplateRole',
            [
                Field('companyId', String, ROLE_SG.c(S.this.company_id)),
                Link(
                    'company',
                    TypeRef['TemplateCompany'],
                    resolvers.direct_link,
                    requires='companyId',
                ),
                *_build_role_user_link('TemplateUser'),
            ],
        ),
        Node('DocumentAccessRole', _build_role_user_link('DocumentAccessUser')),
        Node(
            'DocumentAvailableRole',
            [
                Field('id', String, ROLE_SG.c(S.this.id)),
                Field('companyId', String, ROLE_SG.c(S.this.company_id)),
                Link(
                    'company',
                    TypeRef['DocumentAvailableCompany'],
                    resolvers.direct_link,
                    requires='companyId',
                ),
            ],
        ),
        Node(
            # WARNING: be aware that this node contains sensitive information, so don't use
            # in the context of a recipient company. Use "CommentUser", "DraftUser",
            # "DocumentOwner", etc. or create your own user nodes that have fewer fields and
            # links to avoid leaking sensitive information.
            'User',
            [
                Field('id', String, USER_SG.c(S.this.id)),
                Field('email', String, USER_SG.c(S.this.email)),
                Field('phone', Optional[String], USER_SG.c(S.this.phone)),
                Field('firstName', Optional[String], USER_SG.c(S.this.first_name)),
                Field('secondName', Optional[String], USER_SG.c(S.this.second_name)),
                Field('lastName', Optional[String], USER_SG.c(S.this.last_name)),
                Field('emailConfirmed', Boolean, USER_SG.c(S.this.email_confirmed)),
                Field(
                    'registrationCompleted',
                    Boolean,
                    USER_SG.c(S.this.registration_completed),
                ),
                Field(
                    'isAutogeneratedPassword',
                    Boolean,
                    USER_SG.c(S.this.is_autogenerated_password),
                ),
                Field('isPhoneVerified', Boolean, USER_SG.c(S.this.is_phone_verified)),
                Field('is2FAEnabledInProfile', Boolean, USER_SG.c(S.this.is_2fa_enabled)),
                Field(
                    'isAuthPhoneEnabled',
                    Boolean,
                    USER_SG.c(is_auth_phone_enabled(S.this.auth_phone)),
                ),
                Field('trialAutoEnable', Boolean, USER_SG.c(S.this.trial_auto_enabled)),
                Field('registrationMethod', String, USER_SG.c(S.this.registration_method)),
                Field('source', String, USER_SG.c(S.this.source)),
                Field('createdBy', String, USER_SG.c(S.this.created_by)),
                Field(
                    'is2FAEnabledByRule',
                    Boolean,
                    app_resolvers.resolve_2fa_enabled_by_rule,
                ),
                Field(
                    'isSubscribedEsputnik',
                    Boolean,
                    USER_SG.c(S.this.is_subscribed_esputnik),
                ),
                Field('dateCreated', String, USER_SG.c(S.this.date_created)),
                Field('dateUpdated', String, USER_SG.c(S.this.date_updated)),
                Field(
                    name='language',
                    type_=Optional[String],
                    func=USER_SG.c(enum_value(S.this.language)),
                ),
                Field(
                    'hasPassword',
                    Boolean,
                    USER_SG.c(is_not_none(S.this.password)),
                ),
                Link(
                    'roles',
                    Sequence[TypeRef['Role']],
                    LINKS['user']['role'],
                    requires='id',
                ),
                Link(
                    'onboarding',
                    TypeRef['UserOnboarding'],
                    auth_resolvers.resolve_user_onboarding,
                    requires='id',
                ),
                Link(
                    'userMeta',
                    Optional[TypeRef['UserMeta']],
                    direct_link,
                    requires='id',
                ),
                Field(
                    'showKEPAppPopup',
                    Boolean,
                    app_resolvers.show_kep_app_popup,
                ),
                Field(
                    'bannerPromoKasaShowCount',
                    Optional[Integer],
                    banner_resolvers.resolve_banner_promo_kasa_show_count,
                ),
                Link(
                    'sessions',
                    Sequence[TypeRef['LoginSession']],
                    resolve_login_sessions,
                    requires='id',
                ),
                Field(
                    'activeSurveys',
                    Sequence[String],
                    resolve_active_surveys,
                ),
                Field(
                    'latestCsatSurveyDate',
                    Optional[String],
                    resolve_latest_csat_survey,
                ),
            ],
        ),
        Node(
            'LoginSession',
            [
                Field('id', type_=String, func=LOGIN_SESSION_SG.c(S.this.id)),
                Field('accessedAt', type_=String, func=LOGIN_SESSION_SG.c(S.this.accessed_at)),
                Field('ip', type_=String, func=LOGIN_SESSION_SG.c(S.this.ip)),
                Field('browser', String, LOGIN_SESSION_SG.c(S.this.browser)),
                Field(
                    'browserVersion', Optional[String], LOGIN_SESSION_SG.c(S.this.browser_version)
                ),
                Field('os', String, LOGIN_SESSION_SG.c(S.this.os)),
                Field('osVersion', Optional[String], LOGIN_SESSION_SG.c(S.this.os_version)),
                Field('device', String, LOGIN_SESSION_SG.c(S.this.device)),
                Field('country', Optional[String], LOGIN_SESSION_SG.c(S.this.country)),
                Field('city', Optional[String], LOGIN_SESSION_SG.c(S.this.city)),
                Field('isCurrent', Boolean, LOGIN_SESSION_SG.c(S.this.is_current)),
            ],
        ),
        Node(
            'UserOnboarding',
            [
                Field(
                    'hasCheckedCompanies',
                    Boolean,
                    USER_ONBOARDING_SG.c(S.this.has_checked_companies),
                ),
                Field(
                    'hasInvitedRecipient',
                    Boolean,
                    USER_ONBOARDING_SG.c(S.this.has_invited_recipient),
                ),
                Field(
                    'hasInvitedCoworker',
                    Boolean,
                    USER_ONBOARDING_SG.c(S.this.has_invited_coworker),
                ),
                Field(
                    'hasUploadedDocument',
                    Boolean,
                    USER_ONBOARDING_SG.c(S.this.has_uploaded_document),
                ),
                Field(
                    'isSkipped',
                    Boolean,
                    USER_ONBOARDING_SG.c(S.this.is_skipped),
                ),
                Field(
                    'hasSeenNewUploading',
                    Boolean,
                    USER_ONBOARDING_SG.c(S.this.has_seen_new_uploading),
                ),
            ],
        ),
        Node(
            'UserMeta',
            [
                Field(
                    'mobileUsage',
                    Boolean,
                    USER_META_SG.c(S.this.mobile_usage),
                ),
                Field(
                    'hasActiveMobileApp',
                    Boolean,
                    resolve_has_active_mobile_app,
                ),
                Field(
                    'hasMobileApp',
                    Boolean,
                    resolve_has_mobile_app,
                ),
            ],
        ),
        Node('SignerUser', BASE_USERS_FIELDS),
        Node('ReviewRequestUser', BASE_USERS_FIELDS),
        Node('ReviewUser', BASE_USERS_FIELDS),
        Node('DocumentAutomationTemplateUser', BASE_USERS_FIELDS),
        Node('CommentUser', BASE_USERS_FIELDS),
        Node('SignatureUser', BASE_USERS_FIELDS),
        Node('SignaturePlaceholderUser', BASE_USERS_FIELDS),
        Node('RevokeUser', BASE_USERS_FIELDS),
        Node(
            'CoworkerUser',
            [
                *BASE_USERS_FIELDS,
                Field(
                    name='phone',
                    type_=Optional[String],
                    func=app_resolvers.resolve_coworker_user_phone,
                ),
                Field(
                    'isRegistered',
                    Boolean,
                    USER_SG.c(
                        is_user_registered(
                            S.this.is_logged_once,
                            S.this.email_confirmed,
                            S.this.registration_completed,
                        )
                    ),
                ),
                Link(
                    'sessions',
                    Sequence[TypeRef['LoginSession']],
                    resolve_login_sessions,
                    requires='id',
                ),
            ],
        ),
        Node('DocumentUser', BASE_USERS_FIELDS),
        Node(
            'SignSessionUser',
            [
                Field('id', String, USER_SG.c(S.this.id)),
                Field('phone', String, USER_SG.c(S.this.phone)),
                Field('isPhoneVerified', Boolean, USER_SG.c(S.this.is_phone_verified)),
            ],
        ),
        Node('VersionUser', BASE_USERS_FIELDS),
        Node('DraftUser', BASE_USERS_FIELDS),
        Node('TemplateUser', BASE_USERS_FIELDS),
        Node('DocumentAccessUser', BASE_USERS_FIELDS),
        # Billing models
        Node(
            'BillingAccount',
            [
                Field('id', String, BILLING_ACCOUNT_SG.c(S.this.id)),
                Field('companyId', String, BILLING_ACCOUNT_SG.c(S.this.company_id)),
                Field('initiatorId', String, BILLING_ACCOUNT_SG.c(S.this.initiator_id)),
                Field('type', String, BILLING_ACCOUNT_SG.c(enum_value(S.this.type))),
                Field('rate', String, BILLING_ACCOUNT_SG.c(enum_value(S.this.rate))),
                Field('status', String, BILLING_ACCOUNT_SG.c(enum_value(S.this.status))),
                Field('amount', Integer, BILLING_ACCOUNT_SG.c(S.this.amount)),
                Field('source', String, BILLING_ACCOUNT_SG.c(S.this.source)),
                Field('amountLeft', Integer, BILLING_ACCOUNT_SG.c(S.this.amount_left)),
                Field('units', Integer, BILLING_ACCOUNT_SG.c(S.this.units)),
                Field('unitsLeft', Integer, BILLING_ACCOUNT_SG.c(S.this.units_left)),
                Field('pricePerUser', Integer, BILLING_ACCOUNT_SG.c(S.this.price_per_user)),
                Field('dateCreated', String, BILLING_ACCOUNT_SG.c(S.this.date_created)),
                Field('dateExpired', String, BILLING_ACCOUNT_SG.c(S.this.date_expired)),
                Field('dateDeleted', String, BILLING_ACCOUNT_SG.c(S.this.date_deleted)),
                Link(
                    'billingTransactionsFrom',
                    Sequence[TypeRef['BillingTransaction']],
                    LINKS['billing_account']['billing_transaction_from'],
                    requires='id',
                ),
                Link(
                    'billingTransactionsTo',
                    Sequence[TypeRef['BillingTransaction']],
                    LINKS['billing_account']['billing_transaction_to'],
                    requires='id',
                ),
            ],
        ),
        Node(
            'BillingCompanyConfig',
            [
                Field(
                    name='maxArchiveDocumentsCount',
                    type_=Optional[Integer],
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.max_archive_documents_count),
                ),
                Field(
                    name='maxAdditionalFieldsCount',
                    type_=Optional[Integer],
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.max_additional_fields_count),
                ),
                Field(
                    name='maxEmployeesCount',
                    type_=Optional[Integer],
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.max_employees_count),
                ),
                Field(
                    name='maxDocumentsCount',
                    type_=Optional[Integer],
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.max_documents_count),
                ),
                Field(
                    name='maxTagsCount',
                    type_=Optional[Integer],
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.max_tags_count),
                ),
                Field(
                    name='maxTemplatesCount',
                    type_=Optional[Integer],
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.max_automation_count),
                ),
                Field(
                    name='maxRequiredFieldsCount',
                    type_=Optional[Integer],
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.max_required_fields_count),
                ),
                Field(
                    name='maxVersionsCount',
                    type_=Optional[Integer],
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.max_versions_count),
                ),
                Field(
                    name='apiEnabled',
                    type_=Boolean,
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.api_enabled),
                ),
                Field(
                    name='canEnforce2FA',
                    type_=Boolean,
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.can_enforce_2fa),
                ),
                Field(
                    name='externalCommentsEnabled',
                    type_=Boolean,
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.external_comments_enabled),
                ),
                Field(
                    name='internalCommentsEnabled',
                    type_=Boolean,
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.internal_comments_enabled),
                ),
                Field(
                    name='internalDocumentsEnabled',
                    type_=Boolean,
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.internal_document_enabled),
                ),
                Field(
                    name='canManageEmployeeAccess',
                    type_=Boolean,
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.can_manage_employee_access),
                ),
                Field(
                    name='reviewsEnabled',
                    type_=Boolean,
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.reviews_enabled),
                ),
                Field(
                    name='maxVisibleDocumentsCount',
                    type_=Optional[Integer],
                    func=BILLING_COMPANY_CONFIG_SG.c(S.this.max_visible_documents_count),
                ),
            ],
        ),
        Node(
            'Bill',
            [
                Field('id', String, BILL_SRC.c(S.this.id)),
                Field('seqnum', String, BILL_SRC.c(S.this.seqnum)),
                Field('email', String, BILL_SRC.c(S.this.email)),
                Field('source', String, BILL_SRC.c(S.this.source)),
                Field('documentId', String, BILL_SRC.c(S.this.document_id)),
                Field('companyId', String, BILL_SRC.c(S.this.company_id)),
                Field('statusId', String, BILL_SRC.c(S.this.status_id)),
                Field('dateCreated', String, BILL_SRC.c(S.this.date_created)),
                Field('paymentStatus', String, BILL_SRC.c(S.this.payment_status)),
                Field('number', Optional[String], billing_resolvers.resolve_bill_number),
                Field('amount', Optional[Integer], billing_resolvers.resolve_bill_amount),
                Field('_self', Any, resolvers.get_self_meta_fields),
                Field('servicesType', String, BILL_SRC.c(S.this.services_type)),
                Link(
                    name='services',
                    type_=Sequence[TypeRef['BillService']],
                    func=billing_resolvers.resolve_bill_services,
                    requires='_self',
                ),
                # Deprecated fields (use "services" field instead)
                Field('rate', String, billing_resolvers.get_bill_old_fields),
                Field('count_documents', Optional[String], billing_resolvers.get_bill_old_fields),
                Field(
                    'max_employees_count',
                    Optional[Integer],
                    billing_resolvers.get_bill_old_fields,
                ),
                # Renamed fields (use camelCase fields instead)
                Field('document_id', String, BILL_SRC.c(S.this.document_id)),
                Field('company_id', String, BILL_SRC.c(S.this.company_id)),
                Field('status_id', String, BILL_SRC.c(S.this.status_id)),
                Field('date_created', String, BILL_SRC.c(S.this.date_created)),
                Field('payment_status', String, BILL_SRC.c(S.this.payment_status)),
            ],
        ),
        Node(
            name='BillService',
            fields=[
                # Common fields for all services
                Field('type', String, BILL_SERVICE_SG.c(S.this.type)),
                Field('units', Integer, BILL_SERVICE_SG.c(S.this.units)),
                Field('unitPrice', Integer, BILL_SERVICE_SG.c(S.this.unit_price)),
                # Different fields for different services:
                #  - BillServiceRate: rate, date_from, limits_employees_count
                #  - BillServiceExtension: extension, date_from
                #  - BillServiceUnits: (no additional fields)
                Field('rate', Optional[String], BILL_SERVICE_SG.c(S.this.rate)),  # "rate" type
                Field('dateFrom', Optional[String], BILL_SERVICE_SG.c(S.this.date_from)),
                Field('extension', Optional[String], BILL_SERVICE_SG.c(S.this.extension)),
                Field(
                    'limitsEmployeesCount',
                    Optional[Integer],
                    BILL_SERVICE_SG.c(S.this.limits_employees_count),
                ),
            ],
        ),
        Node(
            'BillingTransaction',
            [
                Field('id', String, BILLING_TRANSACTION_SG.c(S.this.id)),
                Field('from', String, BILLING_TRANSACTION_SG.c(S.this.from_)),
                Field('to', String, BILLING_TRANSACTION_SG.c(S.this.to_)),
                Field('operatorId', String, BILLING_TRANSACTION_SG.c(S.this.operator_id)),
                Field('initiatorId', String, BILLING_TRANSACTION_SG.c(S.this.initiator_id)),
                Field('type', String, BILLING_TRANSACTION_SG.c(enum_value(S.this.type))),
                Field('amount', Integer, BILLING_TRANSACTION_SG.c(S.this.amount)),
                Field('units', Integer, BILLING_TRANSACTION_SG.c(S.this.units)),
                Field('comment', String, BILLING_TRANSACTION_SG.c(S.this.comment)),
                Field('dateCreated', String, BILLING_TRANSACTION_SG.c(S.this.date_created)),
            ],
        ),
        Node(
            'Bonus',
            [
                Field('id', String, BONUS_SG.c(S.this.id)),
                Field('createdBy', String, BONUS_SG.c(S.this.created_by)),
                Field('key', String, BONUS_SG.c(S.this.key)),
                Field('title', String, BONUS_SG.c(S.this.title)),
                Field('description', String, BONUS_SG.c(S.this.description)),
                Field('type', String, BONUS_SG.c(enum_value(S.this.type))),
                Field('units', Integer, BONUS_SG.c(S.this.units)),
                Field('period', Integer, BONUS_SG.c(S.this.period)),
                Field('dateCreated', String, BONUS_SG.c(S.this.date_created)),
                Field('dateExpired', String, BONUS_SG.c(S.this.date_expired)),
                Field('dateDeleted', String, BONUS_SG.c(S.this.date_deleted)),
            ],
        ),
        Node(
            'CompanyRate',
            [
                Field('id', String, COMPANY_RATE_SG.c(S.this.id)),
                Field('rate', String, COMPANY_RATE_SG.c(enum_value(S.this.rate))),
                Field('status', String, COMPANY_RATE_SG.c(enum_value(S.this.status))),
                Field('startDate', String, COMPANY_RATE_SG.c(S.this.activation_date)),
                Field('endDate', String, COMPANY_RATE_SG.c(S.this.date_expired)),
                Field('amount', Integer, COMPANY_RATE_SG.c(S.this.amount)),
                Field('source', String, COMPANY_RATE_SG.c(S.this.source)),
                Field(
                    name='config',
                    type_=Record,
                    func=billing_resolvers.resolve_company_rate_dynamic_fields,
                ),
                Field(
                    name='billNumber',
                    type_=Optional[String],
                    func=billing_resolvers.resolve_company_rate_dynamic_fields,
                ),
                Field(
                    name='units',
                    type_=Integer,
                    func=billing_resolvers.resolve_company_rate_dynamic_fields,
                ),
                Field(
                    name='unitsLeft',
                    type_=Integer,
                    func=billing_resolvers.resolve_company_rate_dynamic_fields,
                ),
                # Deprecated: frontend is already using new camelCase fields, but we need to keep
                # it in code for backward compatibility, until we are sure that it's not used.
                # Check warning logs from the resolver for the usage of these fields.
                Field(
                    name='bill_number',
                    type_=Optional[String],
                    func=billing_resolvers.resolve_company_rate_dynamic_fields,
                ),
                Field(
                    name='units_left',
                    type_=Integer,
                    func=billing_resolvers.resolve_company_rate_dynamic_fields,
                ),
            ],
        ),
        Node(
            'RateExtension',
            [
                Field('id', String, RATE_EXTENSION_SG.c(S.this.id)),
                Field('type', String, RATE_EXTENSION_SG.c(enum_value(S.this.type))),
                Field('status', String, RATE_EXTENSION_SG.c(enum_value(S.this.status))),
                Field('bill_id', String, RATE_EXTENSION_SG.c(S.this.bill_id)),
                Field('date_expiring', Optional[String], RATE_EXTENSION_SG.c(S.this.date_expiring)),
                Field(
                    'bill_document_id',
                    String,
                    app_resolvers.resolve_rate_extension_bill_document_id,
                ),
            ],
        ),
        # Documents models
        Node(
            'Document',
            [
                # Model fields
                Field('id', String, DOCUMENT_SG.c(S.this.id)),
                Field('seqnum', Integer, DOCUMENT_SG.c(S.this.seqnum)),
                Field('userId', String, DOCUMENT_SG.c(S.this.user_id)),
                Field('uploadedBy', String, DOCUMENT_SG.c(S.this.uploaded_by)),
                Field('edrpouOwner', String, DOCUMENT_SG.c(S.this.edrpou_owner)),
                Field(
                    'edrpouRecipient',
                    Optional[String],
                    DOCUMENT_SG.c(S.this.edrpou_recipient),
                ),
                # NOTE: email can be comma separated list of emails
                Field(
                    'emailRecipient',
                    Optional[String],
                    DOCUMENT_SG.c(S.this.email_recipient),
                ),
                Field(
                    'isRecipientEmailHidden',
                    Optional[Boolean],
                    DOCUMENT_SG.c(S.this.is_recipient_email_hidden),
                ),
                Field('title', String, DOCUMENT_SG.c(S.this.title)),
                Field(
                    'extension',
                    String,
                    DOCUMENT_SG.c(unarchive_document_extension(S.this.extension)),
                ),
                Field('archiveName', Optional[String], DOCUMENT_SG.c(S.this.archive_name)),
                Field('statusId', Integer, DOCUMENT_SG.c(S.this.status_id)),
                Field(
                    'dateDocument',
                    Optional[String],
                    DOCUMENT_SG.c(S.this.date_document),
                ),
                Field(
                    'dateFinished',
                    Optional[String],
                    DOCUMENT_SG.c(S.this.date_finished),
                ),
                Field('amount', Optional[Integer], DOCUMENT_SG.c(S.this.amount)),
                Field('type', Optional[String], DOCUMENT_SG.c(S.this.type)),
                Field('category', Optional[Integer], DOCUMENT_SG.c(S.this.category)),
                Field('number', Optional[String], DOCUMENT_SG.c(S.this.number)),
                Field('source', String, DOCUMENT_SG.c(enum_value(S.this.source))),
                Field(
                    'firstSignBy',
                    String,
                    DOCUMENT_SG.c(enum_value(S.this.first_sign_by)),
                ),
                Field('isInternal', Boolean, DOCUMENT_SG.c(S.this.is_internal)),
                Field('isMultilateral', Boolean, DOCUMENT_SG.c(S.this.is_multilateral)),
                Field(
                    'expectedOwnerSignatures',
                    Integer,
                    DOCUMENT_SG.c(S.this.expected_owner_signatures),
                ),
                Field(
                    'expectedRecipientSignatures',
                    Integer,
                    DOCUMENT_SG.c(S.this.expected_recipient_signatures),
                ),
                Field('isProtected', Boolean, DOCUMENT_SG.c(S.this.is_protected)),
                Field(
                    's3XmlToPdfKey',
                    Optional[String],
                    DOCUMENT_SG.c(S.this.s3_xml_to_pdf_key),
                ),
                Field('dateCreated', String, DOCUMENT_SG.c(S.this.date_created)),
                Field('dateUpdated', String, DOCUMENT_SG.c(S.this.date_updated)),
                Field(
                    'dateDelivered',
                    Optional[String],
                    DOCUMENT_SG.c(S.this.date_delivered),
                ),
                # Custom fields
                Field('dateListing', String, app_resolvers.resolve_date_listing),
                Field(
                    'displayCompanyEdrpou',
                    String,
                    DOCUMENT_SG.c(
                        document_display_company_edrpou(
                            S.this.current_company_edrpou,
                            S.this.current_user_email,
                            S.this.edrpou_owner,
                            S.this.edrpou_recipient,
                            S.this.email_recipient,
                        )
                    ),
                ),
                Field(
                    'displayCompanyEmail',
                    String,
                    DOCUMENT_SG.c(
                        document_display_company_email(
                            S.this.current_company_edrpou,
                            S.this.current_user_email,
                            S.this.edrpou_owner,
                            S.this.user.email,
                            S.this.edrpou_recipient,
                            S.this.email_recipient,
                        )
                    ),
                ),
                Field(
                    'displayCompanyName',
                    String,
                    DOCUMENT_SG.c(
                        document_display_company_name(
                            S.this.current_company_edrpou,
                            S.this.current_user_email,
                            S.this.edrpou_owner,
                            S.this.edrpou_recipient,
                            S.this.email_recipient,
                            S.this.company_owner,
                            S.this.company_recipient,
                            S.this.contact_recipient,
                            S.this.contact_owner,
                        )
                    ),
                ),
                Field(
                    'displayStatusText',
                    String,
                    DOCUMENT_SG.c(
                        document_status_text(
                            S.this.is_internal,
                            S.this.is_multilateral,
                            S.this.expected_owner_signatures,
                            S.this.expected_recipient_signatures,
                            S.this.edrpou_owner,
                            S.this.status_id,
                            S.this.first_sign_by,
                            S.this.current_company_edrpou,
                        )
                    ),
                ),
                Field(
                    'isImported',
                    Boolean,
                    DOCUMENT_SG.c(document_is_imported(S.this.source)),
                ),
                Field(
                    'isInput',
                    Boolean,
                    DOCUMENT_SG.c(
                        document_is_input(
                            S.this.current_company_edrpou,
                            S.this.current_user_email,
                            S.this.edrpou_owner,
                            S.this.edrpou_recipient,
                            S.this.email_recipient,
                        )
                    ),
                ),
                Field(
                    'isOneSign',
                    Boolean,
                    DOCUMENT_SG.c(
                        document_is_one_sign(
                            S.this.first_sign_by,
                            S.this.is_internal,
                            S.this.expected_owner_signatures,
                            S.this.expected_recipient_signatures,
                        )
                    ),
                ),
                Field(
                    'expectedSignatureFormat',
                    String,
                    DOCUMENT_SG.c(enum_value(S.this.signature_format)),
                ),
                Field(
                    'reviewStatus',
                    Optional[String],
                    app_resolvers.resolve_review_status,
                ),
                Field('isViewable', Boolean, documents_resolvers.resolve_is_viewable),
                Field('accessLevel', String, documents_resolvers.resolve_access_level),
                Field(
                    'hasEUSignatures',
                    Boolean,
                    documents_resolvers.resolve_has_eu_signatures,
                ),
                Field(
                    'isInvalidSigned',
                    Optional[Boolean],
                    DOCUMENT_SG.c(S.this.is_invalid_signed),
                ),
                Field(
                    'isDeleteLocked',
                    Boolean,
                    documents_resolvers.resolve_is_delete_locked,
                ),
                Field(
                    'isArchived',
                    Boolean,
                    documents_resolvers.resolve_is_archived,
                ),
                Field(
                    name='canDelete',
                    type_=Boolean,
                    func=documents_resolvers.can_delete_document,
                ),
                # Document actions
                Field(
                    name='canSign',
                    type_=Boolean,
                    func=app_resolvers.resolve_document_can_sign_field,
                ),
                # Sequences
                Link(
                    'comments',
                    Sequence[TypeRef['Comment']],
                    app_resolvers.resolve_comments_from_document_node,
                    requires='id',
                ),
                Link(
                    name='accesses',
                    type_=Sequence[TypeRef['DocumentAccess']],
                    func=documents_resolvers.resolve_document_accesses,
                    options=[
                        Option('source', Integer, default=None),
                    ],
                    requires='id',
                ),
                Link(
                    'signatures',
                    Sequence[TypeRef['Signature']],
                    LINKS['document']['signature'],
                    requires='id',
                ),
                Link(
                    'reviews',
                    Sequence[TypeRef['Review']],
                    app_resolvers.resolve_reviews,
                    options=[Option('add_is_last_condition', Optional[Boolean], default=False)],
                    requires='id',
                ),
                Link(
                    'reviewRequests',
                    Sequence[TypeRef['ReviewRequest']],
                    app_resolvers.resolve_review_requests,
                    options=[Option('is_all_requests', Optional[Boolean], default=False)],
                    requires='id',
                ),
                Link(
                    'signers',
                    Sequence[TypeRef['DocumentSigner']],
                    app_resolvers.resolve_signers,
                    requires='id',
                ),
                Link(
                    'children',
                    Sequence[TypeRef['DocumentLink']],
                    app_resolvers.resolve_doc_children,
                    requires='id',
                ),
                Link(
                    'flows',
                    Sequence[TypeRef['DocumentFlow']],
                    app_resolvers.resolve_doc_flows,
                    requires='id',
                ),
                Link(
                    'antivirusChecks',
                    Sequence[TypeRef['AntivirusCheck']],
                    resolve_antivirus_checks_for_document,
                    requires='id',
                ),
                Link(
                    'viewerGroups',
                    Sequence[TypeRef['GroupDocumentAccess']],
                    resolve_viewer_groups_for_documents,
                    requires='id',
                ),
                # Types (objects)
                Link(
                    'parent',
                    Optional[TypeRef['DocumentLink']],
                    app_resolvers.resolve_doc_parent,
                    requires='id',
                ),
                Link(
                    'reviewSetting',
                    Optional[TypeRef['ReviewSetting']],
                    app_resolvers.resolve_review_setting,
                    requires='id',
                ),
                Link(
                    'companyOwner',
                    Optional[TypeRef['CompanyDocumentOwner']],
                    app_resolvers.resolve_company_from_document_node,
                    requires='edrpouOwner',
                ),
                Link(
                    'companyRecipient',
                    Optional[TypeRef['CompanyDocumentRecipient']],
                    app_resolvers.resolve_company_from_document_node,
                    requires='edrpouRecipient',
                ),
                Link(
                    'contactRecipient',
                    Optional[TypeRef['Contact']],
                    app_resolvers.resolve_contact_from_document_node,
                    requires='edrpouRecipient',
                ),
                Link(
                    'contactPersonRecipient',
                    Optional[TypeRef['ContactPerson']],
                    app_resolvers.resolve_contact_person_from_document_node,
                    requires='emailRecipient',
                ),
                Link(
                    'deleteRequest',
                    Optional[TypeRef['DeleteRequest']],
                    app_resolvers.resolve_doc_delete_request,
                    requires='id',
                ),
                Link(
                    'recipients',
                    Sequence[TypeRef['DocumentRecipient']],
                    LINKS['document']['recipients'],
                    requires='id',
                ),
                Link(
                    'user',
                    TypeRef['DocumentUser'],
                    resolvers.direct_link,
                    requires='userId',
                ),
                Link(
                    'tags',
                    Sequence[TypeRef['Tag']],
                    app_resolvers.resolve_tags_by_documents,
                    requires='id',
                ),
                Link(
                    'parameters',
                    Sequence[TypeRef['DocumentParameter']],
                    documents_fields_resolvers.resolve_document_parameters,
                    requires='id',
                ),
                Link(
                    'versions',
                    Sequence[TypeRef['DocumentVersion']],
                    resolve_document_versions_for_document,
                    requires='id',
                ),
                Link(
                    'metadata',
                    Optional[TypeRef['DocumentMeta']],
                    direct_link,
                    requires='id',
                ),
                Link(
                    'drafts',
                    Sequence[TypeRef['Draft']],
                    resolve_drafts_for_document,
                    requires='id',
                ),
                Link(
                    'categoryDetails',
                    TypeRef['DocumentCategory'],
                    direct_link,
                    requires='category',
                ),
                Link(
                    'directory',
                    Optional[TypeRef['DocumentDirectory']],
                    resolve_documents_directories,
                    requires='id',
                ),
                Link(
                    'revoke',
                    Optional[TypeRef['DocumentRevoke']],
                    resolve_documents_revokes,
                    requires='id',
                ),
            ],
        ),
        Node(
            name='DocumentsList',
            fields=[
                Field(
                    name='count',
                    type_=Integer,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='document_ids',
                    type_=Sequence[String],
                    func=resolvers.get_attrib_fields,
                ),
                Link(
                    name='documents',
                    type_=Sequence[TypeRef['Document']],
                    func=direct_link,
                    requires='document_ids',
                ),
            ],
        ),
        Node(
            name='ArchiveList',
            fields=[
                Field(
                    name='count',
                    type_=Integer,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='document_ids',
                    type_=Sequence[String],
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='directory_ids',
                    type_=Sequence[String],
                    func=resolvers.get_attrib_fields,
                ),
                Link(
                    name='documents',
                    type_=Sequence[TypeRef['Document']],
                    func=direct_link,
                    requires='document_ids',
                ),
                Link(
                    name='directories',
                    type_=Sequence[TypeRef['DocumentDirectory']],
                    func=direct_link,
                    requires='directory_ids',
                ),
            ],
        ),
        Node(
            name='DirectoriesList',
            fields=[
                Field(
                    name='count',
                    type_=Integer,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='directory_ids',
                    type_=Sequence[Integer],
                    func=resolvers.get_attrib_fields,
                ),
                Link(
                    name='directories',
                    type_=Sequence[TypeRef['DocumentDirectory']],
                    func=direct_link,
                    requires='directory_ids',
                ),
            ],
        ),
        Node(
            name='DocumentCategoriesList',
            fields=[
                Field(
                    name='count',
                    type_=Integer,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='document_categories_ids',
                    type_=Sequence[String],
                    func=resolvers.get_attrib_fields,
                ),
                Link(
                    name='documentCategories',
                    type_=Sequence[TypeRef['DocumentCategory']],
                    func=direct_link,
                    requires='document_categories_ids',
                ),
            ],
        ),
        Node(
            name='SimpleDirectory',
            fields=[
                Field(name='id', type_=Integer, func=resolvers.get_attrib_fields),
                Field(name='name', type_=String, func=resolvers.get_attrib_fields),
            ],
        ),
        Node(
            'DocumentDirectory',
            [
                Field('id', Integer, DIRECTORY_SG.c(S.this.id)),
                Field('name', String, DIRECTORY_SG.c(S.this.name)),
                Field('parentId', Optional[Integer], DIRECTORY_SG.c(S.this.parent_id)),
                Field('dateCreated', String, DIRECTORY_SG.c(S.this.date_created)),
                Field('dateUpdated', String, DIRECTORY_SG.c(S.this.date_updated)),
                Link(
                    'path',
                    Sequence[TypeRef['SimpleDirectory']],
                    resolve_directory_parents_list,
                    requires='id',
                    description=(
                        'Do not use that field while getting list of directories. '
                        'Try to use it only for node "directory" from graph_root'
                    ),
                ),
            ],
        ),
        Node(
            'DocumentRevoke',
            [
                Field('id', String, DOCUMENT_REVOKE_SG.c(S.this.id)),
                Field('initiatorRoleId', String, DOCUMENT_REVOKE_SG.c(S.this.initiator_role_id)),
                Field(
                    'initiatorCompanyId', String, DOCUMENT_REVOKE_SG.c(S.this.initiator_company_id)
                ),
                Field('reason', String, DOCUMENT_REVOKE_SG.c(S.this.reason)),
                Field('status', String, DOCUMENT_REVOKE_SG.c(S.this.status)),
                Field('documentId', String, DOCUMENT_REVOKE_SG.c(S.this.document_id)),
                Field('signatureFormat', String, DOCUMENT_REVOKE_SG.c(S.this.signature_format)),
                Link(
                    'signatures',
                    Sequence[TypeRef['DocumentRevokeSignature']],
                    resolve_document_revoke_signatures,
                    requires='id',
                ),
                Link(
                    'initiatorCompany',
                    TypeRef['DocumentRevokeCompany'],
                    resolvers.direct_link,
                    requires='initiatorCompanyId',
                ),
                Link(
                    'initiatorRole',
                    TypeRef['RevokeUser'],
                    resolve_user_by_role_id,
                    requires='initiatorRoleId',
                ),
            ],
        ),
        Node(
            'DocumentRevokeSignature',
            [
                Field('id', String, DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.id)),
                Field('revokeId', String, DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.revoke_id)),
                Field('roleId', String, DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.role_id)),
                Field('userEmail', String, DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.user_email)),
                Field('isInternal', Boolean, DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.is_internal)),
                Field('keyAcsk', String, DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.key_acsk)),
                Field(
                    'keySerialNumber',
                    String,
                    DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.key_serial_number),
                ),
                Field('keyTimeMark', String, DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.key_timemark)),
                Field(
                    'keyCompanyFullName',
                    Optional[String],
                    DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.key_company_fullname),
                ),
                Field(
                    'keyOwnerEdrpou',
                    String,
                    DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.key_owner_edrpou),
                ),
                Field(
                    'keyOwnerFullName',
                    String,
                    DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.key_owner_fullname),
                ),
                Field(
                    'keyOwnerPosition',
                    Optional[String],
                    DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.key_owner_position),
                ),
                Field(
                    'stampAcsk', Optional[String], DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.stamp_acsk)
                ),
                Field(
                    'stampSerialNumber',
                    Optional[String],
                    DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.stamp_serial_number),
                ),
                Field(
                    'stampTimeMark',
                    Optional[String],
                    DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.stamp_timemark),
                ),
                Field(
                    'stampCompanyFullName',
                    Optional[String],
                    DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.stamp_company_fullname),
                ),
                Field(
                    'stampOwnerEdrpou',
                    Optional[String],
                    DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.stamp_owner_edrpou),
                ),
                Field(
                    'stampOwnerFullName',
                    Optional[String],
                    DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.stamp_owner_fullname),
                ),
                Field(
                    'stampOwnerPosition',
                    Optional[String],
                    DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.stamp_owner_position),
                ),
                Field('dateCreated', String, DOCUMENT_REVOKE_SIGNATURE_SG.c(S.this.date_created)),
                Link(
                    'user',
                    TypeRef['SignatureUser'],
                    resolve_user_by_role_id,
                    requires='roleId',
                ),
            ],
        ),
        Node(
            'DocumentLink',
            [
                Field('creatorEdrpou', String, app_resolvers.resolve_links),
                Field('documentId', String, app_resolvers.resolve_links),
                Link(
                    'document',
                    TypeRef['Document'],
                    resolvers.direct_link,
                    requires='documentId',
                ),
            ],
        ),
        Node(
            'Tag',
            [
                Field('id', String, TAG_SG.c(S.this.id)),
                Field('name', String, TAG_SG.c(S.this.name)),
                Field('dateCreated', String, TAG_SG.c(S.this.date_created)),
                Field('dateUpdated', String, TAG_SG.c(S.this.date_updated)),
                Field('companyId', String, TAG_SG.c(S.this.company_id)),
                Field('canAssign', Boolean, TAG_SG.c(S.this.can_assign)),
            ],
        ),
        Node(
            'DocumentSigner',
            [
                Field('id', String, DOCUMENT_SIGNER_SG.c(S.this.id)),
                Field('documentId', String, DOCUMENT_SIGNER_SG.c(S.this.document_id)),
                Field('companyId', String, DOCUMENT_SIGNER_SG.c(S.this.company_id)),
                Field('roleId', Optional[String], DOCUMENT_SIGNER_SG.c(S.this.role_id)),
                Field('groupId', Optional[String], DOCUMENT_SIGNER_SG.c(S.this.group_id)),
                Field(
                    'groupSignerId', Optional[String], DOCUMENT_SIGNER_SG.c(S.this.group_signer_id)
                ),
                Field('order', Optional[Integer], DOCUMENT_SIGNER_SG.c(S.this.order)),
                Field('dateCreated', String, DOCUMENT_SIGNER_SG.c(S.this.date_created)),
                Field('dateSigned', Optional[String], DOCUMENT_SIGNER_SG.c(S.this.date_signed)),
                Field('assignerId', Optional[String], DOCUMENT_SIGNER_SG.c(S.this.assigner)),
                Link(
                    'role',
                    TypeRef['SignerRole'],
                    resolvers.direct_link,
                    requires='roleId',
                ),
                Link(
                    'group',
                    TypeRef['Group'],
                    resolvers.direct_link,
                    requires='groupId',
                ),
                Link(
                    'groupSignedBy',
                    TypeRef['SignerRole'],
                    resolvers.direct_link,
                    requires='groupSignerId',
                ),
            ],
        ),
        Node(
            'DocumentAccess',
            [
                Field('id', String, DOCUMENT_ACCESS_SG.c(S.this.id)),
                Field('roleId', String, DOCUMENT_ACCESS_SG.c(S.this.role_id)),
                Field('dateCreated', String, DOCUMENT_ACCESS_SG.c(S.this.date_created)),
                Link(
                    name='role',
                    type_=TypeRef['DocumentAccessRole'],
                    func=direct_link,
                    requires='roleId',
                ),
            ],
        ),
        # Comments models
        Node(
            'Comment',
            [
                Field('id', String, COMMENT_SG.c(S.this.id)),
                Field('documentId', String, COMMENT_SG.c(S.this.document_id)),
                Field(
                    'documentVersionId',
                    String,
                    COMMENT_SG.c(S.this.document_version_id),
                ),
                Field('accessCompanyId', String, COMMENT_SG.c(S.this.access_company_id)),
                Field('roleId', String, COMMENT_SG.c(S.this.role_id)),
                Field('type', String, COMMENT_SG.c(enum_value(S.this.type))),
                Field('text', String, COMMENT_SG.c(S.this.text)),
                Field('dateCreated', String, COMMENT_SG.c(S.this.date_created)),
                Field('dateEdited', String, COMMENT_SG.c(S.this.date_edited)),
                Link(
                    'role',
                    TypeRef['CommentRole'],
                    resolvers.direct_link,
                    requires='roleId',
                ),
                Field(
                    'isRejection',
                    Boolean,
                    COMMENT_SG.c(comment_is_rejection(S.this.type)),
                ),
                Link(
                    'documentVersion',
                    TypeRef['DocumentVersion'],
                    resolvers.direct_link,
                    requires='documentVersionId',
                ),
                # TODO[ID]: To delete after `v9.0.0` deploy
                Field('userId', String, COMMENT_SG.c(S.this.user_id)),
                Field('statusId', Integer, COMMENT_SG.c(S.this.status_id)),
            ],
        ),
        # Signatures models
        Node(
            'Signature',
            [
                Field('id', String, SIGNATURE_SG.c(S.this.id)),
                Field('documentId', String, SIGNATURE_SG.c(S.this.document_id)),
                Field('userId', String, SIGNATURE_SG.c(S.this.user_id)),
                Field('roleId', String, SIGNATURE_SG.c(S.this.role_id)),
                Field('userEmail', String, SIGNATURE_SG.c(S.this.user_email)),
                Field('isInternal', Boolean, SIGNATURE_SG.c(S.this.is_internal)),
                Field('keyAcsk', String, SIGNATURE_SG.c(S.this.key_acsk)),
                Field('keySerialNumber', String, SIGNATURE_SG.c(S.this.key_serial_number)),
                Field('keyTimeMark', String, SIGNATURE_SG.c(S.this.key_timemark)),
                Field(
                    'keyCompanyFullName',
                    Optional[String],
                    SIGNATURE_SG.c(S.this.key_company_fullname),
                ),
                Field('keyOwnerEdrpou', String, SIGNATURE_SG.c(S.this.key_owner_edrpou)),
                Field(
                    'keyOwnerFullName',
                    String,
                    SIGNATURE_SG.c(S.this.key_owner_fullname),
                ),
                Field(
                    'keyOwnerPosition',
                    Optional[String],
                    SIGNATURE_SG.c(S.this.key_owner_position),
                ),
                Field('keyIsLegal', Boolean, SIGNATURE_SG.c(S.this.key_is_legal)),
                Field('stampAcsk', Optional[String], SIGNATURE_SG.c(S.this.stamp_acsk)),
                Field(
                    'stampSerialNumber',
                    Optional[String],
                    SIGNATURE_SG.c(S.this.stamp_serial_number),
                ),
                Field(
                    'stampTimeMark',
                    Optional[String],
                    SIGNATURE_SG.c(S.this.stamp_timemark),
                ),
                Field(
                    'stampCompanyFullName',
                    Optional[String],
                    SIGNATURE_SG.c(S.this.stamp_company_fullname),
                ),
                Field(
                    'stampOwnerEdrpou',
                    Optional[String],
                    SIGNATURE_SG.c(S.this.stamp_owner_edrpou),
                ),
                Field(
                    'stampOwnerFullName',
                    Optional[String],
                    SIGNATURE_SG.c(S.this.stamp_owner_fullname),
                ),
                Field(
                    'stampOwnerPosition',
                    Optional[String],
                    SIGNATURE_SG.c(S.this.stamp_owner_position),
                ),
                Field(
                    'stampIsLegal',
                    Optional[Boolean],
                    SIGNATURE_SG.c(S.this.stamp_is_legal),
                ),
                Field('dateCreated', String, SIGNATURE_SG.c(S.this.date_created)),
                Field('isValid', Boolean, SIGNATURE_SG.c(S.this.is_valid)),
                Link(
                    'user',
                    TypeRef['SignatureUser'],
                    resolvers.direct_link,
                    requires='userId',
                ),
            ],
        ),
        # Contacts models
        Node(
            'Contact',
            [
                Field('id', String, CONTACT_SG.c(S.this.id)),
                Field('companyId', String, CONTACT_SG.c(S.this.company_id)),
                Field('name', Optional[String], CONTACT_SG.c(S.this.name)),
                Field('shortName', Optional[String], CONTACT_SG.c(S.this.short_name)),
                Field('edrpou', String, CONTACT_SG.c(S.this.edrpou)),
                Field('isRegistered', Boolean, app_resolvers.resolve_is_contact_registered),
                Field('dateCreated', String, CONTACT_SG.c(S.this.date_created)),
                Link(
                    'persons',
                    Sequence[TypeRef['ContactPerson']],
                    LINKS['contact']['contact_person'],
                    requires='id',
                ),
                Link(
                    'tags',
                    Sequence[TypeRef['Tag']],
                    app_resolvers.resolve_tags_by_contacts,
                    requires='id',
                ),
            ],
        ),
        Node(
            name='ContactsList',
            fields=[
                Field(
                    name='count',
                    type_=Integer,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='contact_ids',
                    type_=Sequence[String],
                    func=resolvers.get_attrib_fields,
                ),
                Link(
                    name='contacts',
                    type_=Sequence[TypeRef['Contact']],
                    func=direct_link,
                    requires='contact_ids',
                ),
            ],
        ),
        Node(
            name='ContactPersonsList',
            fields=[
                Field(
                    name='count',
                    type_=Integer,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='contact_person_ids',
                    type_=Sequence[String],
                    func=resolvers.get_attrib_fields,
                ),
                Link(
                    name='contact_persons',
                    type_=Sequence[TypeRef['ContactPerson']],
                    func=direct_link,
                    requires='contact_person_ids',
                ),
            ],
        ),
        Node(
            'ContactPerson',
            [
                Field('id', String, CONTACT_PERSON_SG.c(S.this.id)),
                Field('contactId', String, CONTACT_PERSON_SG.c(S.this.contact_id)),
                Field('email', Optional[String], CONTACT_PERSON_SG.c(S.this.email)),
                Field('isEmailHidden', Boolean, CONTACT_PERSON_SG.c(S.this.is_email_hidden)),
                Field('mainRecipient', Boolean, CONTACT_PERSON_SG.c(S.this.main_recipient)),
                Field(
                    'firstName',
                    Optional[String],
                    CONTACT_PERSON_SG.c(S.this.first_name),
                ),
                Field(
                    'secondName',
                    Optional[String],
                    CONTACT_PERSON_SG.c(S.this.second_name),
                ),
                Field('lastName', Optional[String], CONTACT_PERSON_SG.c(S.this.last_name)),
                Link(
                    'contact',
                    Optional[TypeRef['Contact']],
                    resolvers.direct_link,
                    requires='contactId',
                ),
                Link(
                    'phones',
                    Sequence[TypeRef['ContactPersonPhone']],
                    LINKS['contact_person']['contact_person_phone'],
                    requires='id',
                ),
            ],
        ),
        Node(
            'ContactPersonPhone',
            [Field('phone', String, CONTACT_PERSON_PHONE_SG.c(S.this.phone))],
        ),
        # Sessions models
        Node(
            'SignSession',
            [
                Field('id', String, SIGN_SESSION_SG.c(S.this.id)),
                Field('documentId', String, SIGN_SESSION_SG.c(S.this.document_id)),
                Field('roleId', Optional[String], SIGN_SESSION_SG.c(S.this.role_id)),
                Field('edrpou', Optional[String], SIGN_SESSION_SG.c(S.this.edrpou)),
                Field('email', Optional[String], SIGN_SESSION_SG.c(S.this.email)),
                Field('isLegal', Optional[Boolean], SIGN_SESSION_SG.c(S.this.is_legal)),
                Field('type', String, SIGN_SESSION_SG.c(enum_value(S.this.type))),
                Field(
                    'source',
                    Optional[String],
                    SIGN_SESSION_SG.c(enum_value(S.this.source)),
                ),
                Field('status', String, SIGN_SESSION_SG.c(enum_value(S.this.status))),
                Field(
                    'documentStatus',
                    String,
                    SIGN_SESSION_SG.c(enum_value(S.this.document_status)),
                ),
                Field(
                    'finishUrl',
                    String,
                    SIGN_SESSION_SG.c(sign_session_url(S.this.id, 'finish')),
                ),
                Field(
                    'cancelUrl',
                    String,
                    SIGN_SESSION_SG.c(sign_session_url(S.this.id, 'cancel')),
                ),
                Field('signParameters', Record, SIGN_SESSION_SG.c(S.this.sign_parameters)),
                Link(
                    'role',
                    Optional[TypeRef['SignSessionRole']],
                    resolvers.optional_direct_link,
                    requires='roleId',
                ),
            ],
        ),
        Node(
            'DeleteRequest',
            [
                Field('id', String, DELETE_REQUEST_SG.c(S.this.id)),
                Field('documentId', String, DELETE_REQUEST_SG.c(S.this.document_id)),
                Field(
                    'initiatorRoleId',
                    String,
                    DELETE_REQUEST_SG.c(S.this.initiator_role_id),
                ),
                Field(
                    'recipientsEmails',
                    String,
                    DELETE_REQUEST_SG.c(S.this.recipients_emails),
                ),
                Field(
                    'receiverEdrpou',
                    String,
                    DELETE_REQUEST_SG.c(S.this.receiver_edrpou),
                ),
                Field('initiatorEdrpou', String, DELETE_REQUEST_SG.c(S.this.initiator_edrpou)),
                Field('status', String, DELETE_REQUEST_SG.c(enum_value(S.this.status))),
                Field('message', String, DELETE_REQUEST_SG.c(S.this.message)),
                Field('rejectMessage', String, DELETE_REQUEST_SG.c(S.this.reject_message)),
                Field(
                    'currentRoleEmail',
                    String,
                    DELETE_REQUEST_SG.c(S.this.current_role_email),
                ),
                Field(
                    'isReceiver',
                    Boolean,
                    DELETE_REQUEST_SG.c(
                        delete_request_is_input(
                            S.this.current_role_email,
                            S.this.recipients_emails,
                            S.this.current_edrpou,
                            S.this.receiver_edrpou,
                            S.this.is_admin_user,
                        )
                    ),
                ),
            ],
        ),
        Node(
            'DocumentFlow',
            [
                Field('id', String, DOCUMENTS_FLOW_SG.c(S.this.id)),
                Field('companyId', String, DOCUMENTS_FLOW_SG.c(S.this.company_id)),
                Field('edrpou', String, DOCUMENTS_FLOW_SG.c(S.this.edrpou)),
                Field(
                    name='displayCompanyName',
                    type_=Optional[String],
                    func=flows_resolvers.resolve_flow_company_name,
                ),
                Field(
                    'signaturesCount',
                    Integer,
                    DOCUMENTS_FLOW_SG.c(S.this.signatures_count),
                ),
                Field(
                    'pendingSignaturesCount',
                    Integer,
                    DOCUMENTS_FLOW_SG.c(S.this.pending_signatures_count),
                ),
                Field('meta', Record, DOCUMENTS_FLOW_SG.c(S.this.meta)),
                Field('order', Integer, DOCUMENTS_FLOW_SG.c(S.this.order)),
                Field('dateSent', String, DOCUMENTS_FLOW_SG.c(S.this.date_sent)),
                Field(
                    'isComplete',
                    Boolean,
                    DOCUMENTS_FLOW_SG.c(
                        is_flow_complete(S.this.pending_signatures_count, S.this.meta)
                    ),
                ),
                Field('recipientId', String, DOCUMENTS_FLOW_SG.c(S.this.receivers_id)),
                Link(
                    'recipient',
                    TypeRef['DocumentRecipient'],
                    resolvers.direct_link,
                    requires='receiversId',
                ),
                Field(
                    'canSign',
                    Boolean,
                    DOCUMENTS_FLOW_SG.c(
                        is_flow_can_be_signed(
                            S.this.meta,
                            S.this.pending_signatures_count,
                            S.this.edrpou,
                            S.this.current_role,
                            S.this.current_role_email,
                            S.this.current_edrpou,
                        )
                    ),
                ),
                # Deprecated fields, use "recipient" and "recipientId" instead
                Field('receiversId', String, DOCUMENTS_FLOW_SG.c(S.this.receivers_id)),
                Field(
                    'receivers',
                    Record,
                    DOCUMENTS_FLOW_SG.c(format_flow_receiver(S.this.receivers)),
                ),
            ],
        ),
        Node(
            'DocumentRecipient',
            [
                Field('id', String, DOCUMENT_RECIPIENT_SG.c(S.this.id)),
                Field('edrpou', String, DOCUMENT_RECIPIENT_SG.c(S.this.edrpou)),
                Field('document_id', String, DOCUMENT_RECIPIENT_SG.c(S.this.document_id)),
                Field('emails', Optional[Sequence[String]], DOCUMENT_RECIPIENT_SG.c(S.this.emails)),
                Field(
                    'isEmailsHidden',
                    Boolean,
                    DOCUMENT_RECIPIENT_SG.c(S.this.is_emails_hidden),
                ),
                Field('dateSent', Optional[String], DOCUMENT_RECIPIENT_SG.c(S.this.date_sent)),
                Field(
                    'dateReceived', Optional[String], DOCUMENT_RECIPIENT_SG.c(S.this.date_received)
                ),
                Field(
                    'dateDelivered',
                    Optional[String],
                    DOCUMENT_RECIPIENT_SG.c(S.this.date_delivered),
                ),
            ],
        ),
        Node(
            'DocumentAutomationTemplateEntity',
            [
                Field(
                    'id',
                    String,
                    resolvers.get_attrib_fields,
                ),
                Field(
                    'type',
                    String,
                    resolvers.get_attrib_fields,
                ),
                Link(
                    'group',
                    Optional[TypeRef['Group']],
                    resolvers.direct_link,
                    requires='id',
                ),
                Link(
                    'role',
                    Optional[TypeRef['DocumentAutomationTemplateRole']],
                    resolvers.direct_link,
                    requires='id',
                ),
            ],
        ),
        Node(
            'DocumentAutomationTemplate',
            [
                Field('id', String, DOCUMENT_AUTOMATION_TEMPLATE_SG.c(S.this.id)),
                Field('name', String, DOCUMENT_AUTOMATION_TEMPLATE_SG.c(S.this.name)),
                Field('isActive', Boolean, DOCUMENT_AUTOMATION_TEMPLATE_SG.c(S.this.is_active)),
                Field(
                    'reviewSettings',
                    Record,
                    DOCUMENT_AUTOMATION_TEMPLATE_SG.c(S.this.review_settings),
                ),
                Field(
                    'signersSettings',
                    Record,
                    DOCUMENT_AUTOMATION_TEMPLATE_SG.c(S.this.signers_settings),
                ),
                Field(
                    'viewersSettings',
                    Record,
                    DOCUMENT_AUTOMATION_TEMPLATE_SG.c(S.this.viewers_settings),
                ),
                Field(
                    'fieldsSettings',
                    Record,
                    DOCUMENT_AUTOMATION_TEMPLATE_SG.c(S.this.fields_settings),
                ),
                # TODO[DOC-4378]: should be removed after DOC-4378 is implemented
                Field(
                    'tagsSettings',
                    Record,
                    DOCUMENT_AUTOMATION_TEMPLATE_SG.c(S.this.tags_settings),
                ),
                Field(
                    'assignedToId',
                    Optional[String],
                    DOCUMENT_AUTOMATION_TEMPLATE_SG.c(S.this.assigned_to),
                ),
                Link(
                    'assignedTo',
                    Optional[TypeRef['CoworkerRole']],
                    resolvers.optional_direct_link,
                    requires='assignedToId',
                ),
                Link(
                    'tags',
                    Sequence[TypeRef['Tag']],
                    app_resolvers.resolve_tags_by_document_template,
                    requires='id',
                ),
                Link(
                    'reviewers',
                    Sequence[TypeRef['DocumentAutomationTemplateEntity']],
                    document_automation_resolvers.resolve_reviewers,
                    requires='reviewSettings',
                ),
                Link(
                    'signers',
                    Sequence[TypeRef['DocumentAutomationTemplateEntity']],
                    document_automation_resolvers.resolve_signers,
                    requires='signersSettings',
                ),
                Link(
                    'viewerRoles',
                    Sequence[TypeRef['DocumentAutomationTemplateRole']],
                    document_automation_resolvers.resolve_viewers_roles,
                    requires='viewersSettings',
                ),
                Link(
                    'viewerGroups',
                    Sequence[TypeRef['Group']],
                    document_automation_resolvers.resolve_viewer_groups,
                    requires='viewersSettings',
                ),
                Link(
                    'automation',
                    Sequence[TypeRef['DocumentAutomationCondition']],
                    document_automation_resolvers.resolve_automation,
                    requires='id',
                ),
            ],
        ),
        Node(
            'DocumentAutomationCondition',
            [
                Field('id', String, DOCUMENT_AUTOMATION_CONDITION_SG.c(S.this.id)),
                Field('conditions', Record, DOCUMENT_AUTOMATION_CONDITION_SG.c(S.this.conditions)),
                Field(
                    'status',
                    String,
                    DOCUMENT_AUTOMATION_CONDITION_SG.c(enum_value(S.this.status)),
                ),
                Link(
                    'involvedCompanies',
                    Sequence[TypeRef['AutomationConditionCompany']],
                    resolve_automation_condition_involved_companies,
                    requires='conditions',
                ),
            ],
        ),
        Node(
            'DocumentsField',
            [
                Field('id', String, DOCUMENTS_FIELD_SG.c(S.this.id)),
                Field('name', String, DOCUMENTS_FIELD_SG.c(S.this.name)),
                Field('type', String, DOCUMENTS_FIELD_SG.c(enum_value(S.this.type))),
                Field('isRequired', Boolean, DOCUMENTS_FIELD_SG.c(S.this.is_required)),
                Field('order', Optional[Integer], DOCUMENTS_FIELD_SG.c(S.this.order)),
                Field(
                    'canEdit',
                    Boolean,
                    documents_fields_resolvers.resolve_fields_can_edit,
                ),
                Field(
                    'enumOptions',
                    Sequence[String],
                    DOCUMENTS_FIELD_SG.c(S.this.enum_options),
                ),
                Link(
                    'roles',
                    Sequence[TypeRef['CoworkerRole']],
                    documents_fields_resolvers.resolve_roles_by_fields,
                    requires='id',
                ),
            ],
        ),
        Node(
            name='DocumentCategory',
            fields=[
                Field('id', String, DOCUMENT_CATEGORY_SG.c(S.this.id)),
                Field('companyId', Optional[String], DOCUMENT_CATEGORY_SG.c(S.this.company_id)),
                Field('title', String, DOCUMENT_CATEGORY_SG.c(S.this.title)),
                Field('dateCreated', String, DOCUMENT_CATEGORY_SG.c(S.this.date_created)),
                Field('dateUpdated', String, DOCUMENT_CATEGORY_SG.c(S.this.date_updated)),
                Field('dateDeleted', Optional[String], DOCUMENT_CATEGORY_SG.c(S.this.date_deleted)),
            ],
        ),
        Node(
            'DocumentParameter',
            [
                Field('id', String, DOCUMENTS_PARAMETER_SG.c(S.this.id)),
                Field('fieldId', String, DOCUMENTS_PARAMETER_SG.c(S.this.field_id)),
                Field('value', String, DOCUMENTS_PARAMETER_SG.c(S.this.value)),
                Field('isRequired', Boolean, DOCUMENTS_PARAMETER_SG.c(S.this.is_required)),
            ],
        ),
        Node(
            'TriggerNotification',
            [
                Field('id', String, TRIGGER_NOTIFICATION_SG.c(S.this.id)),
                Field(
                    name='title',
                    type_=String,
                    func=TRIGGER_NOTIFICATION_SG.c(
                        trigger_notifications.get_trigger_notification_title(
                            S.this.type,
                            S.this.context,
                        )
                    ),
                ),
                Field(
                    name='description',
                    type_=String,
                    func=TRIGGER_NOTIFICATION_SG.c(
                        trigger_notifications.get_trigger_notification_description(
                            S.this.type,
                            S.this.context,
                        )
                    ),
                ),
                Field(
                    name='url',
                    type_=Optional[String],
                    func=TRIGGER_NOTIFICATION_SG.c(
                        trigger_notifications.get_trigger_notification_url(
                            S.this.url,
                            S.this.type,
                            S.this.context,
                        )
                    ),
                ),
                Field('type', String, TRIGGER_NOTIFICATION_SG.c(enum_value(S.this.type))),
                Field(
                    'status',
                    String,
                    TRIGGER_NOTIFICATION_SG.c(enum_value(S.this.status)),
                ),
                Field(
                    'displayDate',
                    String,
                    TRIGGER_NOTIFICATION_SG.c(S.this.display_date),
                ),
                Field(
                    'context',
                    Record,
                    TRIGGER_NOTIFICATION_SG.c(S.this.context),
                ),
            ],
        ),
        Node(
            'Banner',
            [
                Field('id', String, BANNER_SG.c(S.this.id)),
                Field('dateFrom', String, BANNER_SG.c(S.this.start_date)),
                Field('dateTo', String, BANNER_SG.c(S.this.end_date)),
                Field('color', String, BANNER_SG.c(enum_value(S.this.color))),
                Field('status', String, BANNER_SG.c(enum_value(S.this.status))),
                Field(
                    'positions',
                    Optional[Sequence[String]],
                    BANNER_SG.c(enum_value(S.this.positions)),
                ),
                Field(
                    'rates',
                    Optional[Sequence[String]],
                    BANNER_SG.c(enum_value(S.this.rates)),
                ),
                Field(
                    'outgoingDocumentsCount',
                    Optional[Sequence[String]],
                    BANNER_SG.c(S.this.outgoing_documents_count),
                ),
                Field(
                    'incomingDocumentsSignCount',
                    Optional[Sequence[String]],
                    BANNER_SG.c(S.this.incoming_documents_sign_count),
                ),
                Field(
                    'activityPeriod',
                    Optional[String],
                    BANNER_SG.c(S.this.activity_period),
                ),
                Field(
                    'employeesCount',
                    Optional[Sequence[String]],
                    BANNER_SG.c(S.this.employees_count),
                ),
                Field(
                    'audienceType', Optional[String], BANNER_SG.c(enum_value(S.this.audience_type))
                ),
                Field(
                    'daysBeforeSignatureExpires',
                    Optional[Integer],
                    BANNER_SG.c(S.this.days_before_signature_expires),
                ),
                Field(
                    'analyticsCategory',
                    String,
                    BANNER_SG.c(enum_value(S.this.analytics_category)),
                ),
                Field(
                    'text',
                    String,
                    BANNER_SG.c(get_banner_content_field(S.this.content, 'uk', 'text')),
                ),
                Field(
                    'textEn',
                    String,
                    BANNER_SG.c(get_banner_content_field(S.this.content, 'en', 'text')),
                ),
                Field(
                    'linkText',
                    String,
                    BANNER_SG.c(get_banner_content_field(S.this.content, 'uk', 'link_text')),
                ),
                Field(
                    'linkTextEn',
                    String,
                    BANNER_SG.c(get_banner_content_field(S.this.content, 'en', 'link_text')),
                ),
                Field(
                    'link',
                    String,
                    BANNER_SG.c(get_banner_content_field(S.this.content, 'uk', 'link_url')),
                ),
                Field(
                    'linkEn',
                    String,
                    BANNER_SG.c(get_banner_content_field(S.this.content, 'en', 'link_url')),
                ),
            ],
        ),
        Node(
            'DocumentVersion',
            [
                Field('id', String, DOCUMENT_VERSION_SG.c(S.this.id)),
                Field('name', String, DOCUMENT_VERSION_SG.c(S.this.name)),
                Field('type', String, DOCUMENT_VERSION_SG.c(enum_value(S.this.type))),
                Field('roleId', String, DOCUMENT_VERSION_SG.c(S.this.role_id)),
                Field('isSent', Boolean, DOCUMENT_VERSION_SG.c(S.this.is_sent)),
                Field('extension', String, DOCUMENT_VERSION_SG.c(S.this.extension)),
                Field('contentHash', String, DOCUMENT_VERSION_SG.c(S.this.content_hash)),
                Field(
                    'contentLength',
                    Integer,
                    DOCUMENT_VERSION_SG.c(S.this.content_length),
                ),
                Field('dateCreated', String, DOCUMENT_VERSION_SG.c(S.this.date_created)),
                Link(
                    'antivirusChecks',
                    Sequence[TypeRef['AntivirusCheck']],
                    resolve_antivirus_checks_for_document_version,
                    requires='id',
                ),
                Link(
                    'role',
                    TypeRef['VersionRole'],
                    resolvers.direct_link,
                    requires='roleId',
                ),
                Field(
                    'reviewStatus',
                    Optional[String],
                    app_resolvers.resolve_review_status_for_version,
                ),
            ],
        ),
        Node(
            'DocumentRequiredField',
            [
                Field('id', String, DOCUMENT_REQUIRED_FIELD_SG.c(S.this.id)),
                Field(
                    'documentCategory',
                    String,
                    DOCUMENT_REQUIRED_FIELD_SG.c(S.this.document_category),
                ),
                Field(
                    'isNameRequired',
                    Boolean,
                    DOCUMENT_REQUIRED_FIELD_SG.c(S.this.is_name_required),
                ),
                Field(
                    'isTypeRequired',
                    Boolean,
                    DOCUMENT_REQUIRED_FIELD_SG.c(S.this.is_type_required),
                ),
                Field(
                    'isNumberRequired',
                    Boolean,
                    DOCUMENT_REQUIRED_FIELD_SG.c(S.this.is_number_required),
                ),
                Field(
                    'isDateRequired',
                    Boolean,
                    DOCUMENT_REQUIRED_FIELD_SG.c(S.this.is_date_required),
                ),
                Field(
                    'isAmountRequired',
                    Boolean,
                    DOCUMENT_REQUIRED_FIELD_SG.c(S.this.is_amount_required),
                ),
                Field(
                    'companyId',
                    String,
                    DOCUMENT_REQUIRED_FIELD_SG.c(S.this.company_id),
                ),
                Link(
                    'company',
                    TypeRef['DocumentRequiredFieldCompany'],
                    resolvers.direct_link,
                    requires='companyId',
                ),
            ],
        ),
        Node(
            'AntivirusCheck',
            [
                Field('id', String, ANTIVIRUS_CHECK_SG.c(S.this.id)),
                Field('provider', String, ANTIVIRUS_CHECK_SG.c(S.this.provider)),
                Field('status', String, ANTIVIRUS_CHECK_SG.c(S.this.status)),
                Field('dateCreated', String, ANTIVIRUS_CHECK_SG.c(S.this.date_created)),
                Field('dateUpdated', String, ANTIVIRUS_CHECK_SG.c(S.this.date_updated)),
                Field(
                    'documentVersionId',
                    String,
                    ANTIVIRUS_CHECK_SG.c(S.this.document_version_id),
                ),
            ],
        ),
        Node(
            'DraftAntivirusCheck',
            [
                Field('draftId', String, DRAFT_ANTIVIRUS_CHECK_SG.c(S.this.draft_id)),
                Field('provider', String, DRAFT_ANTIVIRUS_CHECK_SG.c(S.this.provider)),
                Field('status', String, DRAFT_ANTIVIRUS_CHECK_SG.c(S.this.status)),
                Field('dateCreated', String, DRAFT_ANTIVIRUS_CHECK_SG.c(S.this.date_created)),
                Field('dateUpdated', String, DRAFT_ANTIVIRUS_CHECK_SG.c(S.this.date_updated)),
            ],
        ),
        Node(
            'CloudSigner',
            [
                Field(
                    'operationId',
                    String,
                    CLOUD_SIGNER_SG.c(S.this.operation_id),
                ),
                Field(
                    'documentId',
                    String,
                    CLOUD_SIGNER_SG.c(S.this.document_id),
                ),
            ],
        ),
        Node(
            'DocumentMeta',
            [
                Field(
                    'contentHash',
                    String,
                    DOCUMENT_META_SG.c(S.this.content_hash),
                ),
                Field(
                    'contentLength',
                    Integer,
                    DOCUMENT_META_SG.c(S.this.content_length),
                ),
            ],
        ),
        Node(
            'Group',
            [
                Field('id', String, GROUP_SG.c(S.this.id)),
                Field('name', String, GROUP_SG.c(S.this.name)),
                Field('dateCreated', String, GROUP_SG.c(S.this.date_created)),
                Field('createdBy', String, GROUP_SG.c(S.this.created_by)),
                Link(
                    'members',
                    Sequence[TypeRef['GroupMember']],
                    resolve_group_members_by_group,
                    requires='id',
                ),
                Link(
                    'documentSigners',
                    Sequence[TypeRef['Document']],
                    resolve_documents_pending_group_signer,
                    requires='id',
                ),
                Link(
                    'documentReviewers',
                    Sequence[TypeRef['Document']],
                    resolve_documents_pending_group_reviewer,
                    requires='id',
                ),
                Link(
                    'documentAutomationTemplates',
                    Sequence[TypeRef['DocumentAutomationTemplate']],
                    resolve_template_with_group,
                    requires='id',
                ),
            ],
        ),
        Node(
            name='GroupsList',
            fields=[
                Field(
                    name='count',
                    type_=Integer,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='group_ids',
                    type_=Sequence[String],
                    func=resolvers.get_attrib_fields,
                ),
                Link(
                    name='groups',
                    type_=Sequence[TypeRef['Group']],
                    func=direct_link,
                    requires='group_ids',
                ),
            ],
        ),
        Node(
            'GroupMember',
            [
                Field('id', String, GROUP_MEMBER_SG.c(S.this.id)),
                Field('groupId', String, GROUP_MEMBER_SG.c(S.this.group_id)),
                Field('roleId', String, GROUP_MEMBER_SG.c(S.this.role_id)),
                Field('dateCreated', String, GROUP_MEMBER_SG.c(S.this.date_created)),
                Field('createdBy', String, GROUP_MEMBER_SG.c(S.this.created_by)),
                Link(
                    'createdByRole',
                    TypeRef['CoworkerRole'],
                    resolvers.direct_link,
                    requires='createdBy',
                ),
                Link(
                    'role',
                    TypeRef['CoworkerRole'],
                    resolvers.direct_link,
                    requires='roleId',
                ),
            ],
        ),
        Node(
            'GroupDocumentAccess',
            [
                Field('id', String, GROUP_DOCUMENT_ACCESS_SG.c(S.this.id)),
                Field('groupId', String, GROUP_DOCUMENT_ACCESS_SG.c(S.this.group_id)),
                Field('documentId', String, GROUP_DOCUMENT_ACCESS_SG.c(S.this.document_id)),
                Field('createdBy', String, GROUP_DOCUMENT_ACCESS_SG.c(S.this.created_by)),
                Field('dateCreated', String, GROUP_DOCUMENT_ACCESS_SG.c(S.this.date_created)),
                Link(
                    'group',
                    TypeRef['Group'],
                    resolvers.direct_link,
                    requires='groupId',
                ),
                Link(
                    'createdByRole',
                    TypeRef['CoworkerRole'],
                    resolvers.direct_link,
                    requires='createdBy',
                ),
            ],
        ),
        Node(
            'ContactRecipient',
            [
                Field('edrpou', String, CONTACT_RECIPIENT_SG.c(S.this.edrpou)),
                Field('name', Optional[String], CONTACT_RECIPIENT_SG.c(S.this.name)),
                Field('email', Optional[String], CONTACT_RECIPIENT_SG.c(S.this.email)),
                Field('userName', Optional[String], CONTACT_RECIPIENT_SG.c(S.this.user_name)),
                Field('isMainRecipient', Boolean, CONTACT_RECIPIENT_SG.c(S.this.main_recipient)),
            ],
        ),
        Node(
            'Draft',
            [
                Field('id', String, DRAFT_SG.c(S.this.id)),
                Field('type', String, DRAFT_SG.c(S.this.type)),
                Field('dateCreated', String, DRAFT_SG.c(S.this.date_created)),
                Field('dateUpdated', Optional[String], DRAFT_SG.c(S.this.date_updated)),
                Field('companyId', String, DRAFT_SG.c(S.this.company_id)),
                Field('creatorRoleId', String, DRAFT_SG.c(S.this.creator_role_id)),
                Field('documentId', Optional[String], DRAFT_SG.c(S.this.document_id)),
                Field(
                    'documentVersionId', Optional[String], DRAFT_SG.c(S.this.document_version_id)
                ),
                Field('templateId', Optional[String], DRAFT_SG.c(S.this.template_id)),
                Field(
                    'dateScheduledDeletion',
                    Optional[String],
                    DRAFT_SG.c(draft_date_scheduled_deletion(S.this.date_updated)),
                ),
                Link(
                    'creatorRole',
                    TypeRef['DraftRole'],
                    resolvers.direct_link,
                    requires='creatorRoleId',
                ),
                Link(
                    'antivirusCheck',
                    Optional[TypeRef['DraftAntivirusCheck']],
                    resolvers.direct_link,
                    requires='id',
                ),
                Link(
                    'template',
                    Optional[TypeRef['Template']],
                    resolvers.direct_link,
                    requires='templateId',
                ),
            ],
        ),
        Node(
            name='DraftsList',
            fields=[
                Field(
                    name='count',
                    type_=Integer,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='draft_ids',
                    type_=Sequence[String],
                    func=resolvers.get_attrib_fields,
                ),
                Link(
                    name='drafts',
                    type_=Sequence[TypeRef['Draft']],
                    func=direct_link,
                    requires='draft_ids',
                ),
            ],
        ),
        Node(
            'Template',
            [
                Field('id', String, TEMPLATE_SG.c(S.this.id)),
                Field('title', String, TEMPLATE_SG.c(S.this.title)),
                Field('extension', String, TEMPLATE_SG.c(S.this.extension)),
                Field('dateCreated', String, TEMPLATE_SG.c(S.this.date_created)),
                Field('dateUpdated', String, TEMPLATE_SG.c(S.this.date_updated)),
                Field('companyId', Optional[String], TEMPLATE_SG.c(S.this.company_id)),
                Field(
                    'creatorRoleId',
                    Optional[String],
                    TEMPLATE_SG.c(
                        template_creator_role_id(
                            S.this.created_by,
                            S.this.company_id,
                        )
                    ),
                ),
                Field('category', Optional[String], TEMPLATE_SG.c(S.this.category)),
                Link(
                    'creatorRole',
                    TypeRef['TemplateRole'],
                    resolvers.direct_link,
                    requires='creatorRoleId',
                ),
                Field(
                    'isFavorite',
                    Boolean,
                    func=resolve_is_favorite,
                ),
                Field(
                    'previewImgUrl',
                    String,
                    func=resolve_preview_url,
                ),
            ],
        ),
        Node(
            name='TemplatesList',
            fields=[
                Field(
                    name='count',
                    type_=Integer,
                    func=resolvers.get_attrib_fields,
                ),
                Field(
                    name='template_ids',
                    type_=Sequence[String],
                    func=resolvers.get_attrib_fields,
                ),
                Link(
                    name='templates',
                    type_=Sequence[TypeRef['Template']],
                    func=direct_link,
                    requires='template_ids',
                ),
            ],
        ),
        ROOT,
    ]
)


class AsyncGraphQLTypeNameField(GraphTransformer):
    """
    Add __typename field to each node in the schema.

    For some reason the __typename field is not added to the schema by default.
    It's implemented inside GraphQLIntrospection.
    But for end user we don't want to expose entire schema introspection.

    From https://graphql.org/learn/schema/
     The __typename field is a special meta-field that automatically exists on
     every Object type and resolves to the name of that type, providing a way to
     differentiate between data types on the client.

    Mobile application client heavily relies on __typename field. Without it
    client won't work.

    Reference of implementation: hiku.introspection.graphql.GraphQLIntrospection
    """

    @staticmethod
    def __type_name__(node_name: str) -> Field:
        return Field('__typename', String, _async_wrapper(partial(type_name_field_func, node_name)))

    def visit_node(self, obj: Node) -> Node:
        node = super().visit_node(obj)
        node.fields.append(self.__type_name__(obj.name))
        return node

    def visit_root(self, obj: Root) -> Root:
        root = super().visit_root(obj)
        root.fields.append(self.__type_name__(QUERY_ROOT_NAME))
        return root
