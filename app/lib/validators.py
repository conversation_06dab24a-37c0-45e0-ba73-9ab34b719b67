import base64
import binascii
import copy
import datetime
import enum
import functools
import io
import json
import logging
import re
import traceback
from collections.abc import Callable
from decimal import ROUND_HALF_UP, Decimal
from functools import partial
from string import punctuation
from typing import Any, TypeVar, cast

import phonenumbers
import pydantic
import trafaret as t
import ujson
import xmltodict
from aiohttp import web
from aiohttp.http_exceptions import InvalidHeader
from aiohttp.web_exceptions import HTTPRequestEntityTooLarge
from aiohttp.web_request import FileField
from lxml import etree
from multidict import CIMultiDict, CIMultiDictProxy
from trafaret_validator import TrafaretValidator
from yarl import URL

from api.errors import Code, Error, InvalidRequest
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import enums, regexp
from app.lib.datetime_utils import parse_raw_datetime, soft_to_local_datetime
from app.lib.enums import enum_items, enum_values
from app.lib.helpers import (
    empty_string,
    ensure_ascii,
    split_comma_separated_emails,
    string_to_bool,
    truncate_and_strip,
)
from app.lib.types import (
    AnyDict,
    DataDict,
    DataMapping,
    MultipartFormDataProxy,
)
from app.services import services

logger = logging.getLogger(__name__)

T = TypeVar('T')

PydanticT = TypeVar('PydanticT', bound='pydantic.BaseModel')

#: Custom validators for Vchasno
CompanyName = partial(t.String, max_length=1024)
ONLY_EDRPOU = partial(t.Regexp, regexp.edrpou_re)
IPN = partial(t.Regexp, regexp.ipn_re)
# currently used to validate old passwords that were set
# before new security policies
InsecurePassword = partial(t.String, min_length=7, max_length=255)
Token = partial(t.String, allow_blank=False)
UserName = partial(t.String, allow_blank=True, max_length=64)
PassportCode = partial(t.Regexp, regexp.passport_re)

MULTIPART_CONTENT_TYPES = (
    '',
    'multipart/form-data',
    'application/x-www-form-urlencoded',
)


# define patterns to identify malicious payloads
SQL_INJECTION_PATTERNS = [
    re.compile(r'(?i)(union select|select.*from|insert into|update.*set|delete from)'),
    re.compile(r'(?i)(;--|;#)'),
    re.compile(r"(?i)(or 1=1|and 1=1|or 'a'='a|and 'a'='a)"),
    re.compile(r'(?i)(drop table|alter table|create table|truncate table)'),
    re.compile(r'(?i)(exec xp_cmdshell|exec sp_executesql|exec sp_addsrvrolemember)'),
]

XSS_PATTERNS = [
    re.compile(r'(?i)<script.*?>.*?</script>'),
    re.compile(r'(?i)javascript:'),
    re.compile(r"(?i)on\w+=['\"]?[^'\">]+['\"]?"),
    re.compile(r'(?i)document\.cookie'),
    re.compile(r'(?i)window\.location'),
    re.compile(r"(?i)<img\s+src=['\"][^'\"]*['\"]\s+onerror=['\"][^'\"]*['\"]"),
]

RCE_PATTERNS = [
    re.compile(r'(?i)(import os|import subprocess|os\.system|subprocess\.Popen)'),
    re.compile(r'(?i)(eval\(|exec\(|pickle\.loads|marshal\.loads)'),
    re.compile(r'(?i)(wget\s+|curl\s+|/bin/sh|/bin/bash)'),
    re.compile(r'(?i)(nc\s+|netcat\s+|socat\s+)'),
]


class EDRPOU(t.Regexp):
    message: str = 'Is not valid EDRPOU'

    def __init__(self) -> None:
        super().__init__(regexp.company_code_re)

    def check_and_return(self, value: Any) -> str:
        if not isinstance(value, str):
            self._failure('EDRPOU is not a string', value=value)

        if regexp.company_auto_code_re.match(value):
            return value

        if self.regexp.match(value) and not regexp.zero_company_code_re.match(value):
            return value

        self._failure(self.message, value=value)
        # The previous line will raise an error, but the method self._failure
        # doesn't provide type hint `NoReturn` that should inform mypy about that.
        raise


class Date(t.Call):
    def __init__(self) -> None:
        super().__init__(validate_date)


class DateTime(t.Call):
    def __init__(self) -> None:
        super().__init__(validate_datetime)


class LeftDateTime(t.Call):
    def __init__(self) -> None:
        super().__init__(validate_left_datetime)


class Price(t.Call):
    def __init__(self) -> None:
        super().__init__(validate_price)


class Amount(t.Call):
    def __init__(self, *, lte: int | None = None, gte: int | None = None) -> None:
        func = functools.partial(validate_amount, lte=lte, gte=gte)
        super().__init__(func)


class RightDateTime(t.Call):
    def __init__(self) -> None:
        super().__init__(validate_right_datetime)


class Enum(t.Enum):
    """
    Validate string by Enum values. It returns str. If you need Enum object use EnumType.
    """

    def __init__(self, *variants: type[enum.Enum]) -> None:
        all_variants = (item.value for variant in variants for item in enum_items(variant))
        super().__init__(*all_variants)


class EnumType(t.Trafaret):
    """
    Validate string by Enum values. It returns Enum object
    """

    def __init__(self, enum_type: type[enum.Enum]) -> None:
        self.enum_type = enum_type
        self.trafaret = t.Enum(*enum_values(enum_type))
        super().__init__()

    def check_and_return(self, value: str, context: Any | None = None) -> enum.Enum:
        self.trafaret.check(value, context)
        return self.enum_type(value)


class IP(t.Call):
    def __init__(self) -> None:
        super().__init__(validate_ip)


class UserRole(t.Enum):
    def __init__(self) -> None:
        super().__init__(*(item.value for item in enums.UserRole))


class UUID(t.Regexp):
    def __init__(self) -> None:
        super().__init__(regexp.uuid_re)


class Base64Data(t.Trafaret):
    INVALID_TYPE_ERROR = _('Значення повинно бути рядком')
    CANT_DECODE_ERROR = _('Неможливо отримати значення рядка у форматі base64')

    def check_and_return(self, value: str) -> bytes:
        if not isinstance(value, str):
            raise t.DataError(self.INVALID_TYPE_ERROR, value=value, trafaret=self)

        try:
            return base64.b64decode(value)
        except binascii.Error:
            raise t.DataError(self.CANT_DECODE_ERROR, value=value, trafaret=self)


class FileUpload(t.Trafaret):
    def check_and_return(self, value: bytes | str | FileField) -> FileField:
        if isinstance(value, FileField):
            return value
        if isinstance(value, bytes):
            # Log this case, because situation is strange
            logger.warning('File upload is bytes', extra={'file_upload': value})
            return FileField(
                name='',
                filename='',
                file=io.BufferedReader(io.BytesIO(value)),  # type: ignore
                content_type='application/octet-stream',
                headers=CIMultiDictProxy(CIMultiDict()),
            )

        logger.warning('Wrong type for FileUpload', extra={'file_upload': value})
        raise t.DataError(error='Value must have FileField type', value=value, trafaret=self)


class Language(EnumType):
    def __init__(self) -> None:
        super().__init__(enum_type=enums.Language)


class DocumentUUIDTrafaret(TrafaretValidator):
    document_id = UUID()


def password() -> t.Trafaret:
    return t.String(max_length=255, allow_blank=True) >> validate_password


def validate_password(value: str) -> str | None:
    """
    Ensure that password is valid and secure enough according to DOC-4049.

    Password must be alphanumerical and contain at least one
    digit and special character.
    """
    err = _('Пароль має містити щонайменше 8 символів, хоча б одну цифру і один символ')
    if not value:
        raise t.DataError(_('Пароль не може бути пустим'), value=value)
    if len(value) < 8:
        raise t.DataError(err, value=value)

    has_letters = False
    has_digits = False
    has_special_characters = False
    for char in value:
        if char.isalpha():
            has_letters = True
            continue
        if char.isdigit():
            has_digits = True
            continue
        if char in punctuation:
            has_special_characters = True
            continue

    if not (has_letters and has_digits and has_special_characters):
        raise t.DataError(err, value=value)

    return value


def boolean(*, nullable: bool = False) -> t.Trafaret:
    # allow value to be a boolean string:
    #  - "True", "true", "1" -> True
    #  - otherwise -> False
    # TODO: add explicit values for "False" branch
    trafaret = t.String(allow_blank=True) >> truncate_and_strip >> string_to_bool

    # allow value to be a python "bool"
    trafaret = trafaret | t.Bool()

    # allow value to be a None
    if nullable:
        trafaret = trafaret | t.Null()

    return trafaret


def _join_emails_list(value: list[str]) -> str | None:
    return ', '.join(value) or None


def comma_separated_email(*, nullable: bool = False) -> t.Trafaret:
    trafaret = t.String() >> truncate_and_strip >> split_comma_separated_emails >> t.List(email())
    if nullable:
        trafaret = trafaret | t.Null()

    return trafaret


def emails_list(nullable: bool = True) -> t.Trafaret:
    """Validate list of emails. Always return list even if value is None"""
    trafaret = t.List(email())
    if nullable:
        trafaret = trafaret | t.Null()
    return trafaret >> default_value([], deep_copy=True)


def default_value(value: Any, deep_copy: bool = False) -> Callable[[Any], Any]:
    """If value from validator is None replace it with given default value."""

    def default(validator_value: Any) -> Any:
        default_value_object = copy.deepcopy(value) if deep_copy else value
        return default_value_object if validator_value is None else validator_value

    return default


def _is_email_chars_valid(value: str) -> bool:
    """
    Special checks about emails characters that we want to enforce.
    """

    # aiosmtplib requirement, not RFC6532
    if ensure_ascii(value) is None:
        return False

    # SMTPServer requirement, not RFC6532
    if value.startswith('-'):
        return False

    return True


def _validate_email(value: str) -> str | None:
    if not value:
        return value

    if not _is_email_chars_valid(value):
        raise t.DataError('Email contains invalid characters', value=value)

    return value


def email(*, nullable: bool = False) -> t.Trafaret:
    """Helper function to validate emails."""
    trafaret = t.String(allow_blank=False) >> truncate_and_strip >> t.Email
    if nullable:
        trafaret = trafaret | t.Null()
    return trafaret >> _validate_email


def is_valid_email(value: str) -> bool:
    """
    Lightweight alternative to "email" trafaret that doesn't raise an exception and doesn't
    have stripping logic to avoid accepting emails that have whitespace at the beginning or end.
    """

    # this regexp is used by t.Email trafaret internally
    if not t.internet.EMAIL_REGEXP.match(value):
        return False

    if not _is_email_chars_valid(value):
        return False

    return True


def ensure_details_dict(data: DataDict | str) -> DataDict:
    if isinstance(data, dict):
        return data
    return {'trafaret': data}


def int_or_none(value: Any) -> int | None:
    """Ensure cast value to int if it is not null."""
    try:
        return int(value)
    except (ValueError, TypeError):
        return None


def ipn() -> t.Trafaret:
    """Helper function to validate IPN."""
    trafaret = t.String(allow_blank=True, max_length=32) >> truncate_and_strip

    return trafaret >> validate_ipn


def is_legal() -> t.Trafaret:
    """Helper function to validate is legal boolean flags."""
    return (t.Bool() | t.Null()) >> default_value(True)


def phone(*, allow_blank: bool = True) -> t.Trafaret:
    """Helper function to validate phone numbers."""
    trafaret = t.String(allow_blank=allow_blank, max_length=32) >> truncate_and_strip

    if allow_blank:
        trafaret = (trafaret | t.Null()) >> empty_string
    return trafaret >> validate_phone


def trafaret_error_as_dict_filter_python_details(error_text: str) -> str:
    if error_text == 'value should be None':
        return 'value should be null'

    if error_text == 'value is not a list':
        return 'value is not an array'

    if error_text == 'value is not a dict':
        return 'value is not an object'

    if error_text == 'value should be True or False':
        return 'value is not a boolean'

    if (
        error_text == "value can't be converted to int"
        or error_text == "value can't be converted to float"
        or error_text == 'value is not int'
    ):
        return 'value is not a number'

    return error_text


def trafaret_dict_error_as_dict(error: DataDict | str) -> DataDict | str:
    if isinstance(error, str):
        return trafaret_error_as_dict_filter_python_details(error)

    return {k: trafaret_dict_error_as_dict(v) for k, v in error.items()}


def validate(validator_class: type[TrafaretValidator], data: DataMapping) -> AnyDict:
    """
    Helper function to validate given data with validator and return valid data
    as result.

    In total this is just a wrapper on top of ``TrafaretValidator`` for Vchasno
    project. You can do the validation by ``TrafaretValidator`` manually, but
    this function takes care about proper exception to raise.
    """

    validator = validator_class(**data)

    if not validator.validate():
        details = trafaret_dict_error_as_dict(validator.errors)
        logger.info(
            'Trafaret validation error',
            extra={
                'validator': validator_class.__name__,
                'details': details,
                'data': data,
                'validator_errors': validator.errors,
            },
        )
        raise InvalidRequest(details=details)
    return validator.data


def validate_date(value: str) -> datetime.date:
    """Check whether given value is a valid date in ISO8601 format."""
    return validate_datetime(value).date()


def validate_datetime(value: str) -> datetime.datetime:
    """Check whether given value is a valid datetime in ISO8601 format."""
    # Empty value
    if not value:
        raise t.DataError('Empty date value', value=value)

    try:
        result = parse_raw_datetime(value)
    except ValueError:
        raise t.DataError('Invalid date value', value=value)

    return result


def validate_edrpou(value: str) -> str | None:
    """Ensure that given value is a valid EDRPOU.

    This is not an actual validator as it does not raise an exception, but
    suppress it. Going forward it will might be changed.
    """
    try:
        return EDRPOU().check(value)
    except t.DataError:
        return None


def validate_email(value: str) -> str | None:
    try:
        return email().check(value)
    except t.DataError:
        return None


def validate_enum(value: str, enum_class: type[enum.Enum]) -> str | None:
    """Check whether given value exists in given enum class.

    This is not an actual validator as it does not raise an exception.
    """
    try:
        return Enum(enum_class).check(value)
    except t.DataError:
        return None


def validate_ip(value: str) -> str:
    """Check whether given value is valid IP address or IP range."""
    # Empty value
    if not value:
        raise t.DataError('Empty value as IP address', value=value)

    # Do not allow complete wildcard IPs
    if value in {'*', '*.*', '*.*.*', '*.*.*.*'}:
        raise t.DataError('Invalid IP address. We do not allow wildcard IPs', value=value)

    parts = value.split('.')
    # IP address contains wrong number of dots
    if len(parts) > 4:
        raise t.DataError('Invalid format for IP address', value=value)

    # try to extract mask
    subparts = parts[-1].split('/')
    if len(subparts) == 2:
        if subparts[-1].isdigit():
            int_value = int(subparts[-1])
            if int_value < 0 or int_value > 32:
                raise t.DataError('Invalid mask', value=value)
            if '*' in parts:
                raise t.DataError(
                    'Invalid IP address. We do not allow wildcard IPs with mask',
                    value=value,
                )
        else:
            raise t.DataError('Invalid mask', value=value)
        parts[-1] = subparts[0]

    # Exact match, like 127.0.0.1 or 192.168.1.*
    if all(validate_ip_part(part) for part in parts):
        return value
    raise t.DataError('Invalid IP address', value=value)


def validate_ip_part(value: str) -> str | None:
    """Validate IP part value."""
    if value == '*':
        return value

    if value.isdigit():
        int_value = int(value)
        return value if 0 <= int_value <= 255 else None

    counter = value.count('*')
    if counter != 1:
        return None

    return value


def validate_ipn(value: str) -> str | None:
    """Ensure that given value is a valid IPN."""
    if not value:
        return None

    try:
        return IPN().check(value)
    except t.DataError:
        raise t.DataError('Invalid ipn code', value=value)


async def validate_json_xml_request(request: web.Request, *, allow_blank: bool = False) -> Any:
    content_type = request.headers.get('content-type', '').lower()
    if content_type in ('application/xml', 'text/xml'):
        return await validate_xml_request(request, allow_blank=allow_blank)
    return await validate_json_request(request, allow_blank=allow_blank)


async def validate_json_post_request(request: web.Request, *, allow_blank: bool = False) -> Any:
    if request.content_type.endswith('/json'):
        return await validate_json_request(request, allow_blank=allow_blank)
    return await validate_post_request(request)


def validate_json_string(
    value: str | bytes,
    *,
    allow_blank: bool = True,
) -> DataDict:
    """
    Check that string or bytes are valid JSON objects.
    If parameter 'allow_blank' is True, then empty JSON string will be converted
    to empty dict.
    """

    # for unknown reason "AnyStr" isn't equal to "Union[str, bytes]" for mypy
    # see: https://github.com/python/mypy/issues/1533
    _value = cast(Any, value)
    try:
        return ujson.loads(_value)
    except ValueError:
        if allow_blank and not value:
            return {}

        logger.warning(
            msg='Invalid JSON request',
            exc_info=True,
            extra={'value': value},
        )
        raise Error(Code.invalid_json_request)


def validate_json_query_param(value: str) -> DataDict:
    """
    Validate query parameter that is encoded to JSON
    """
    if not isinstance(value, str):
        raise InvalidRequest(
            reason=_('Значення параметра не є рядком'),
            details={'value': value},
        )

    try:
        return json.loads(value)
    except ValueError:
        raise InvalidRequest(
            reason=_('Значення є невалідним JSON'),
            details={'value': value},
        )


async def validate_json_request(
    request: web.Request, *, allow_blank: bool = False, dict_only: bool = True
) -> Any:
    """Extract valid JSON data from `request` or raise an error.

    Used to not raise a server error if a given request does not contain valid
    JSON content, but raise an HTTP 400 Bad Request error instead.

    Args:
        request: A request from which to try and extract data.
        allow_blank (optional, keyword-only): Allow blank requests or not. If
            `True`, blank requests return an empty dict.
        dict_only (optional, keyword-only): Allow requests with non-dict based body.
            If `True`, list requests will raise an error.

    Returns:
        Parsed JSON data on success.

    Raises:
        api.errors.Error: If the request is not valid JSON data.
    """
    try:
        result = await request.json(loads=ujson.loads)

        if dict_only and not isinstance(result, dict):
            if get_flag(FeatureFlags.JSON_IS_DICT_VALIDATION):
                raise ValueError
            logger.warning(f'Expected dict, got {result} instead')

        return result
    except ValueError:
        if allow_blank and not await request.read():
            return {}

        logger.warning(
            'Invalid JSON request. If the error persists, please ask the '
            'frontend engineers or external clients to fix their request '
            'to the backend',
            exc_info=True,
            extra={'url': str(request.rel_url)},
        )
        raise Error(Code.invalid_json_request)


def validate_deserialized_json_type(json_data: T, expected_type: type) -> T:
    """Return the deserialized JSON response if it is of expected type.

    Raise an error if it's not.

    If you want to check against `None`, pass `type(None)` to `expected_type`,
    since `NoneType` is not normally available inside Python code.

    Args:
        json_data: JSON data to be validated.
        expected_type: The expected type to validate against.

    Raises:
        api.errors.Error: If the JSON data does not match the type.
    """
    if not isinstance(json_data, expected_type):
        logger.warning(
            f'Invalid request: got JSON payload of type {type(json_data)} '
            f'while expecting {type(expected_type)}. If the error persists, '
            'please ask the frontend engineers or external clients to fix '
            'their request to the backend.',
        )
        raise Error(Code.invalid_json_request)

    # Since MyPy fails to recognize that the value of the same type is being
    # returned, cast the type explicitly
    return cast(T, json_data)


async def validate_raw_xml_request(request: web.Request) -> etree.ElementTree:
    """
    Do not raise server error if given request does not contain valid JSON
    content. Raise 400 error instead.
    """
    parser = etree.XMLParser(remove_blank_text=True)
    content = await request.read()
    try:
        return etree.parse(io.BytesIO(content), parser=parser)
    except etree.XMLSyntaxError:
        raise Error(Code.invalid_xml)


def validate_left_datetime(value: str) -> datetime.datetime | None:
    dt = validate_datetime(value)
    return soft_to_local_datetime(dt)


def validate_price(data: float) -> Decimal:
    value = Decimal(t.Float(gt=0).check(data))
    return value.quantize(Decimal('.01'), rounding=ROUND_HALF_UP)


def validate_amount(data: float, lte: int | None = None, gte: int | None = None) -> Decimal:
    value = Decimal(t.Float(lte=lte, gte=gte).check(data))
    return value.quantize(Decimal('.01'), rounding=ROUND_HALF_UP)


def validate_right_datetime(value: str) -> datetime.datetime | None:
    dt = validate_datetime(value)
    if value and 'T' not in value:
        dt = dt.replace(hour=23, minute=59, second=59, microsecond=999_999)
    return soft_to_local_datetime(dt)


async def _post_exception_data(request: web.Request) -> DataDict:
    try:
        post_string = str(await request.post())
        read_bytes = str(await request.read())
    except Exception:
        post_string = ''
        read_bytes = ''
    return {
        'request_url': str(request.rel_url),
        'request_post_start': post_string[:150],
        'request_post_end': post_string[-150:],
        'can_read_body': request.body_exists,
        'request_read_bytes_start': read_bytes[:150],
        'request_read_bytes_end': read_bytes[-150:],
        'request_method': request.method,
        'request_content_type': request.content_type,
        'request_content_length': request.content_length,
        'request_stream_cursor': str(getattr(request.content, '_cursor', None)),
        'request_stream_size': str(getattr(request.content, '_size', None)),
        'request_stream_buffer': str(getattr(request.content, '_buffer', None)),
    }


def _is_field_name_problem(exception: Exception) -> bool:
    """
    Determines if an exception was caused by a field name issue.

    This function checks the traceback of the provided exception to identify if
    a specific assertion error related to the `field.name` being `None` occurred
    during processing of a POST request by aiohttp (await request.post())
    """
    tb = traceback.format_tb(exception.__traceback__)
    return any('assert field.name is not None' in item for item in tb)


async def validate_post_request(request: web.Request) -> MultipartFormDataProxy:
    # Validate content type
    content_type: str = request.content_type.lower()
    if content_type not in MULTIPART_CONTENT_TYPES:
        raise InvalidRequest(
            reason=_('Використовуйте заголовок content-type: multipart/form-data'),
            details={'current_content_type': request.content_type},
        )

    try:
        return await request.post()  # type: ignore
    except HTTPRequestEntityTooLarge:
        client_max_size = services.config.app.client_max_size
        raise Error(Code.max_total_size, details={'max_total_size': client_max_size}, status=413)
    except InvalidHeader as exception:
        data: DataDict = {
            **await _post_exception_data(request),
            'headers': exception.headers,
        }
        logger.info('Multipart form exception: invalid header', extra=data)
        raise InvalidRequest(
            reason=_(
                'Невалідний заголовок запиту або тіла у форматі multipart/form-data. '
                'Перші рядки тіла зпиту повинні бути валідними заголовками '
                'multipart/form-data'
            ),
            details=data,
        )

    except binascii.Error as exception:
        data = {**await _post_exception_data(request), 'exception': str(exception)}
        logger.info('Multipart form exception: invalid base64', extra=data)
        raise InvalidRequest(reason=_('Неможливо розкодувати дані у base64'), details=data)

    except AssertionError as exception:
        message = str(exception)
        data = {**await _post_exception_data(request), 'exception': str(exception)}

        if 'Reading after EOF' in message:
            logger.info('Multipart form exception: reading after EOF', extra=data)
            raise InvalidRequest(
                reason=_(
                    'Неможливо визначити кінець тіла запиту. '
                    'Ймовірні причини помилки: неправильне використання boundary або '
                    "заголовку запиту Content-Length (загловок не обов'язковий)"
                ),
                details=data,
            )
        if _is_field_name_problem(exception):
            logger.info('Multipart form exception: malformed field name', extra=data)
            raise InvalidRequest(
                reason=_(
                    'Ймовірна помилка у заголовку Content-Disposition в тілі запиту. '
                    'Перевірте формування тіла запиту, додайте екранування '
                    'спеціальних символів (;) при формуванні заголовків'
                ),
                details=data,
            )
        logger.info('Multipart form exception: Unhandled', extra=data)
        raise exception

    except ValueError as exception:
        message = str(exception)
        data = {**await _post_exception_data(request), 'exception': str(exception)}
        # Dummy detect reason
        if 'boundary' in message:
            logger.info('Multipart form exception: boundary problem', extra=data)
            raise InvalidRequest(reason=_('Помилка з міткою boundary у тілі запиті'), details=data)

        logger.info('Multipart form exception: Unhandled', extra=data)
        raise exception

    except TypeError as exception:
        message = str(exception)
        data = {**await _post_exception_data(request), 'exception': str(exception)}
        if 'MultiDict keys should be' in message:
            logger.info('Multipart form exception: body headers', extra=data)
            raise InvalidRequest(
                reason=_('Проблема з заголовками multipart-form/data'), details=data
            )

        logger.info('Multipart form exception: Unhandled', extra=data)
        raise exception

    except UnicodeDecodeError as exception:
        message = str(exception)
        data = {**await _post_exception_data(request), 'exception': message}
        logger.info('Multipart form exception: decoding error', extra=data)
        raise InvalidRequest(reason=_('Проблема із кодуванням multipart-form/data'), details=data)

    except Exception as exception:
        data = {**await _post_exception_data(request), 'exception': str(exception)}
        logger.exception('Multipart form exception: Unhandled', exc_info=True, extra=data)
        raise InvalidRequest(reason=_('Невідома проблема під час розбору multipart-form/data'))


def validate_phone(value: str) -> str:
    """Validate phone number via phonenumbers library."""
    if not value:
        return value

    try:
        number: phonenumbers.PhoneNumber = phonenumbers.parse(value)
    except phonenumbers.NumberParseException:
        raise t.DataError('Invalid phone number', value=value)

    if not phonenumbers.is_valid_number(number):
        raise t.DataError('Please supply valid ukrainian phone number', value=value)

    return f'+{number.country_code}{number.national_number}'


def validate_trafaret(trafaret: t.Trafaret, data: Any) -> Any:
    """Validate plain trafaret instead of validator class."""
    try:
        return trafaret.check(data)
    except t.DataError as err:
        raise InvalidRequest(
            details=ensure_details_dict(trafaret_dict_error_as_dict(err.as_dict()))
        )


def _get_error_message(error_type: str, error_dict: DataDict) -> str:  # noqa: C901
    """Get appropriate error message for error type."""
    match error_type:
        case 'string_type' | 'uuid_type':
            return 'value is not a string'
        case 'model_type' | 'dict_type':
            return 'value is not an object'
        case 'list_type' | 'tuple_type' | 'set_type':
            return 'value is not an array'
        case 'missing':
            return 'is required'
        case 'value_error.email':
            return 'value is not a valid email address'
        case 'string_too_long':
            ctx = error_dict.get('ctx', {})
            max_length = ctx.get('max_length')
            if max_length is not None:
                return f'String is longer than {max_length} characters'
            return 'String is too long'
        case 'int_parsing' | 'float_parsing' | 'int_type' | 'float_type' | 'int_from_float':
            return 'value is not a number'
        case 'greater_than_equal':
            return 'value is less than 0'
        case 'greater_than':
            return 'value should be greater than 0'
        case 'less_than_equal':
            ctx = error_dict.get('ctx', {})
            limit = ctx.get('le')
            if limit is not None:
                return f'value is greater than {limit}'
            return 'value is greater than maximum allowed'
        case 'bool_parsing' | 'bool_type':
            return 'value is not a boolean'
        case 'union_tag_invalid' | 'union_tag_not_found':
            return 'value is not valid'
        case 'literal_error' | 'enum' | 'none_required':
            return "value doesn't match any variant"
        case 'uuid_parsing':
            return (
                'does not match pattern '
                '^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$'
            )
        case 'value_error':
            value_error = error_dict.get('ctx', {}).get('error')
            error_message = error_dict.get('msg')
            if value_error and isinstance(value_error, ValueError):
                return str(value_error)
            if error_message:
                # By default, pydantic adds "Value Error," prefix to the message of the all value
                # error. We try to not use this message because this prefix is not translated and
                # hidden deep inside pydantic internals
                return error_message
            return 'value error'
        case _:
            return error_dict.get('msg', 'unknown error')


def convert_pydantic_to_trafaret_error(
    error: pydantic.ValidationError,
) -> DataDict | dict[int, Any]:
    """
    Convert pydantic ValidationError to trafaret-like error dictionary.

    This function transforms pydantic validation errors into a nested dictionary structure
    similar to trafaret's error format. It provides a simplified conversion that handles
    basic error structures without trying to replicate trafaret's complex union behavior.

    The conversion process:
    1. Build a raw error tree from the flat list of pydantic errors
    2. Recursively process the tree to handle union types and nested structures
    3. Apply trafaret-style error message formatting

    Examples:
        Simple field error:
            Pydantic: [{'type': 'string_type', 'loc': ('name',), 'msg': '...'}]
            Result: {'name': 'value is not a string'}

        Nested object error:
            Pydantic: [{'type': 'missing', 'loc': ('user', 'email'), 'msg': '...'}]
            Result: {'user': {'email': 'is required'}}

        List item error:
            Pydantic: [{'type': 'string_type', 'loc': ('items', 0, 'name'), 'msg': '...'}]
            Result: {'items': {0: {'name': 'value is not a string'}}}

        Union type error (int | str):
            Pydantic: [
                {'type': 'int_parsing', 'loc': ('value', 'int'), 'msg': '...'},
                {'type': 'string_type', 'loc': ('value', 'str'), 'msg': '...'}
            ]
            Result: {'value': {0: 'value is not a number', 1: 'value is not a string'}}
    """
    errors = cast(list[DataDict], error.errors())
    error_tree = _build_error_tree(errors)
    result = _process_error_tree(error_tree)
    if isinstance(result, str):
        return {'__root__': result}
    return result


def _build_error_tree(errors: list[DataDict]) -> DataDict:
    """
    Build a nested error tree from flat list of pydantic error details.

    This function takes the flat list of error dictionaries from pydantic and
    constructs a nested tree structure based on the location paths. It also
    detects potential union scenarios where a field with None as an alternative
    has errors deep within the non-None path.

    Example:
        Input: [
            {'type': 'string_type', 'loc': ('user', 'name'), 'msg': '...'},
            {'type': 'missing', 'loc': ('user', 'email'), 'msg': '...'}
        ]
        Output: {
            'user': {
                'name': {'_error_type': 'string_type', '_error_dict': {...}},
                'email': {'_error_type': 'missing', '_error_dict': {...}}
            }
        }
    """
    tree: DataDict = {}

    for error_detail in errors:
        location_path = error_detail['loc']

        # Navigate to the correct position in the tree
        current_node = tree
        for path_element in location_path:
            current_node = current_node.setdefault(path_element, {})

        # Store the error information at the leaf node
        current_node['_error_type'] = error_detail['type']
        current_node['_error_dict'] = error_detail

    return tree


def _process_error_tree(tree: DataDict) -> DataDict | dict[int, Any] | str:
    """
    Recursively process the error tree to handle unions and convert to trafaret format.

    This function traverses the error tree built by _build_error_tree and:
    1. Identifies union type patterns in the tree structure
    2. Converts union errors to indexed format (0, 1, 2, ...)
    3. Converts leaf nodes to trafaret-style error messages
    4. Handles nested structures recursively
    """
    if not tree:
        return {}

    # Check if this is a leaf node (contains error information)
    if '_error_type' in tree:
        error_type = tree['_error_type']
        error_dict = cast(DataDict, tree['_error_dict'])
        return _get_error_message(error_type, error_dict)

    # Check if this node represents a union type
    union_result = _process_union_node(tree)
    if union_result is not None:
        return union_result

    # Regular nested structure - process each child recursively
    result: DataDict = {}
    for key, subtree in tree.items():
        if isinstance(key, str) and key.startswith('_'):
            continue
        result[key] = _process_error_tree(cast(DataDict, subtree))

    return result


def _process_union_node(tree: DataDict) -> dict[int, Any] | None:
    """
    Process a tree node that represents a union type.

    Union types in pydantic error paths are identified by having child nodes
    that are type indicators (like 'str', 'int', model names, etc.).

    Example:
        Input tree: {
            'str': {'_error_type': 'string_type', '_error_dict': {...}},
            'int': {'_error_type': 'int_parsing', '_error_dict': {...}}
        }
        Output: {0: 'value is not a string', 1: 'value is not a number'}
    """
    child_keys = [key for key in tree if not str(key).startswith('_')]

    if not child_keys:
        return None

    if not all(_is_union_type_indicator(key) for key in child_keys):
        return None

    # Process union alternatives and assign indices
    # Preserve the order from the original pydantic errors instead of sorting
    union_result: dict[int, Any] = {}
    for index, type_key in enumerate(child_keys):
        subtree = cast(DataDict, tree[type_key])
        union_result[index] = _process_error_tree(subtree)

    return union_result


def _is_union_type_indicator(element: Any) -> bool:
    """
    Determine if a path element indicates a union type alternative.

    Union type indicators in pydantic location paths include:
    - Built-in type names: 'str', 'int', 'float', 'bool', 'list', 'dict'
    - Generic types: 'list[...]', 'dict[...]'
    - Special types: 'NoneType', 'type'
    - Model class names (typically CapWords)
    - Module-qualified names: 'typing.Union', 'datetime.datetime'

    Examples:
        _is_union_type_indicator('str') -> True
        _is_union_type_indicator('UserModel') -> True
        _is_union_type_indicator('list[str]') -> True
        _is_union_type_indicator('user_name') -> False  # Regular field name
    """
    if not isinstance(element, str):
        return False

    # Built-in Python types commonly used in unions
    builtin_types = {'str', 'int', 'float', 'bool', 'list', 'dict', 'tuple', 'set'}
    if element in builtin_types:
        return True

    # Special typing-related types
    special_types = {'NoneType', 'type', 'object', 'Any'}
    if element in special_types:
        return True

    # Generic types with brackets (list[str], dict[str, int], etc.)
    if '[' in element and element.endswith(']'):
        return True

    # Module-qualified names (typing.Union, datetime.datetime, etc.)
    if '.' in element:
        return True

    # Model class names typically start with uppercase (PEP8 convention)
    # This heuristic assumes field names follow snake_case
    if element and element[0].isupper():
        return True

    return False


def validate_pydantic(schema: type[PydanticT], data: Any) -> PydanticT:
    """Validate pydantic model instead of validator class."""
    try:
        return schema.model_validate(data)
    except pydantic.ValidationError as err:
        details = convert_pydantic_to_trafaret_error(err)
        raise InvalidRequest(details=details)


def validate_pydantic_adapter(
    adapter: pydantic.TypeAdapter[T],
    value: Any,
    *,
    context: DataDict | None = None,
) -> T:
    """
    Validate scalar types using pydantic TypeAdapter.

    Example:
    >>> from app.lib import validators_pydantic as pv
    >>>
    >>> raw_document_id = '123e4567-e89b-12d3-a456-************'
    >>> document_id = validate_pydantic_adapter(pv.UUIDAdapter, value=raw_document_id)
    """
    try:
        return adapter.validate_python(value, context=context)
    except pydantic.ValidationError as err:
        details = convert_pydantic_to_trafaret_error(err)
        raise InvalidRequest(details=details)


def validate_url(value: str) -> URL | None:
    try:
        return URL(t.URL(value))
    except t.DataError:
        return None


def validate_uuid(value: str) -> str | None:
    """Validate that a string value is in fact a valid UUID.

    This is not an actual validator as it does not raise an exception.
    """
    try:
        return UUID().check(value)
    except t.DataError:
        return None


async def validate_xml_request(request: web.Request, *, allow_blank: bool = False) -> Any:
    text = await request.read()
    try:
        data = xmltodict.parse(text, xml_attribs=False)
        if len(data) != 1:
            raise Error(
                code=Code.invalid_xml_request,
                reason=_('XML should contain single root tag.'),
            )
        return list(data.values())[0]
    except Exception:
        if allow_blank and not text:
            return {}
        logger.warning('Invalid XML request.', exc_info=True, extra={'url': str(request.rel_url)})

        raise Error(Code.invalid_xml_request)


def is_malicious(text: str) -> bool:
    """
    Check if the given text is malicious.
    Check blacklisted patterns for SQL injection, XSS, and RCE.
    """
    for pattern in SQL_INJECTION_PATTERNS:
        if pattern.search(text):
            return True

    for pattern in XSS_PATTERNS:
        if pattern.search(text):
            return True

    return any(pattern.search(text) for pattern in RCE_PATTERNS)
