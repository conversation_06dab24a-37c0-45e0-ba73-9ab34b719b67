from app.csat.enums import CsatSurvey
from app.csat.tables import csat_survey_table
from app.lib.database import DBConnection
from app.lib.types import DataDict
from app.models import select_all


async def insert_csat_survey(conn: DBConnection, data: DataDict) -> None:
    await conn.execute(csat_survey_table.insert().values(data))


async def select_csat_surveys(
    conn: DBConnection, user_id: str, limit: int | None = None
) -> list[CsatSurvey]:
    query = csat_survey_table.select().where(csat_survey_table.c.user_id == user_id)

    if limit:
        query = query.order_by(csat_survey_table.c.date_created.desc()).limit(limit)

    rows = await select_all(conn, query)
    return [CsatSurvey.from_row(row) for row in rows]
