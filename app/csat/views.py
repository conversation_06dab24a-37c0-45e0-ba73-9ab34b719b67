import logging
from http import HTTPStatus

from aiohttp import web

from app.auth.decorators import login_required
from app.auth.types import User
from app.csat.db import insert_csat_survey
from app.csat.validators import CsatRequestSchema
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib.validators import validate_json_request, validate_pydantic
from app.openapi.decorators import openapi_docs
from app.services import services

logger = logging.getLogger(__name__)


@openapi_docs(
    summary='Add CSAT feedback',
    request_json=CsatRequestSchema,
)
@login_required()
async def add_csat_feedback(request: web.Request, user: User) -> web.Response:
    if not get_flag(FeatureFlags.ENABLE_CSAT_FEEDBACK):
        logger.info('CSAT feedback is unavailable.')
        return web.json_response(
            data={'status': 'unavailable', 'info': 'CSAT disable by featureflag'},
            status=HTTPStatus.FORBIDDEN,
        )

    raw_data = await validate_json_request(request)

    valid_data = validate_pydantic(CsatRequestSchema, raw_data)

    async with services.db.acquire() as conn:
        await insert_csat_survey(
            conn,
            {
                'user_id': user.id,
                'company_id': user.company_id,
                'type': valid_data.type,
                'estimate': valid_data.estimate,
                'feedback': valid_data.feedback,
            },
        )

    return web.json_response(data={'status': 'ok'}, status=HTTPStatus.CREATED)
