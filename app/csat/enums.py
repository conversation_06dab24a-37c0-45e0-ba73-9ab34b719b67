import datetime
from dataclasses import dataclass
from enum import auto, unique

from app.lib.database import DBRow
from app.lib.enums import NamedEnum


@unique
class CsatActionType(NamedEnum):
    upload_doc_without_signing = auto()
    upload_doc_with_signing = auto()


@dataclass(frozen=True)
class CsatSurvey:
    id: str
    user_id: str
    company_id: str
    type: CsatActionType
    date_created: datetime.datetime
    estimate: int | None = None
    feedback: str | None = None

    @staticmethod
    def from_row(row: DBRow) -> 'CsatSurvey':
        return CsatSurvey(**row)
