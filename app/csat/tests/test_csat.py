from http import HTTPStatus

from app.csat.db import select_csat_surveys
from app.csat.enums import CsatActionType
from app.flags import FeatureFlags
from app.tests.common import fetch_graphql, prepare_auth_headers, prepare_client


async def test_csat_add_feedback_disable_flag(aiohttp_client, test_flags):
    test_flags[FeatureFlags.ENABLE_CSAT_FEEDBACK.name] = False
    app, client, user = await prepare_client(aiohttp_client)

    body = {
        'type': CsatActionType.upload_doc_with_signing.value,
        'estimate': 4,
        'feedback': 'Test feedback',
    }

    resp = await client.post(
        path='/internal-api/csat',
        json=body,
        headers=prepare_auth_headers(user),
    )

    assert resp.status == HTTPStatus.FORBIDDEN
    body = await resp.json()
    assert body == {'status': 'unavailable', 'info': 'CSAT disable by featureflag'}

    resp = await client.post(
        path='/internal-api/csat',
        json=body,
    )

    assert resp.status == HTTPStatus.BAD_REQUEST


async def test_csat_add_feedback(aiohttp_client, test_flags):
    test_flags[FeatureFlags.ENABLE_CSAT_FEEDBACK.name] = True
    app, client, user = await prepare_client(aiohttp_client)

    data = await fetch_graphql(
        client, '{ currentUser { id latestCsatSurveyDate dateCreated} }', prepare_auth_headers(user)
    )

    assert data['currentUser']['id'] == user.id
    assert data['currentUser']['latestCsatSurveyDate'] is None

    body = {
        'type': CsatActionType.upload_doc_with_signing.value,
        'estimate': 4,
        'feedback': 'Test feedback',
    }
    resp = await client.post(
        path='/internal-api/csat',
        json=body,
        headers=prepare_auth_headers(user),
    )

    # Assert
    assert resp.status == HTTPStatus.CREATED
    body = await resp.json()
    assert body == {'status': 'ok'}

    async with app['db'].acquire() as conn:
        result = await select_csat_surveys(conn, user.id)
    assert len(result) == 1
    assert result[0].type == CsatActionType.upload_doc_with_signing.value
    assert result[0].estimate == 4
    assert result[0].feedback == 'Test feedback'
    assert result[0].user_id == user.id
    assert result[0].company_id == user.company_id
    assert result[0].date_created

    data = await fetch_graphql(
        client, '{ currentUser { id latestCsatSurveyDate dateCreated} }', prepare_auth_headers(user)
    )

    assert data['currentUser']['id'] == user.id
    assert data['currentUser']['latestCsatSurveyDate'] == result[0].date_created.isoformat()
