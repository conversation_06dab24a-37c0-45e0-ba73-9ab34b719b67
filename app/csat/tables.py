import sqlalchemy as sa

from app.csat.enums import CsatActionType
from app.models import columns, metadata

csat_survey_table = sa.Table(
    'csat_surveys',
    metadata,
    columns.UUID(),
    columns.ForeignKey('user_id', 'users.id', nullable=False, ondelete='NO ACTION', index=True),
    columns.ForeignKey(
        'company_id',
        'companies.id',
        nullable=False,
        ondelete='CASCADE',
    ),
    columns.SoftEnum('type', CsatActionType, nullable=False),
    columns.DateCreated(),
    # 'estimate' and 'feedback' can be nullable: if a survey is shown to a user
    # and they skip it (e.g., by closing the popup),
    # we store this action to prevent showing the survey again to the same user for 30 days.
    sa.Column('estimate', sa.SmallInteger(), nullable=True),
    columns.Text('feedback', nullable=True),
)

sa.Index(
    'index_csat_user_id',
    csat_survey_table.c.user_id,
    csat_survey_table.c.date_created.desc(),
)
