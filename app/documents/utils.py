from __future__ import annotations

import asyncio
import copy
import logging
import uuid
from collections import defaultdict
from contextlib import suppress
from datetime import datetime
from enum import Enum
from typing import (
    Any,
    Literal,
    assert_never,
    cast,
)
from urllib.parse import urlencode

import sqlalchemy as sa
from aiohttp import (
    BasicAuth,
    ClientResponse,
    web,
)
from aiohttp.client_exceptions import ClientError
from elasticmagic import SearchQuery
from sqlalchemy.sql.elements import BinaryExpression

import app.reviews.utils as reviews_utils
from api.errors import (
    AccessDenied,
    Code,
    Error,
    InvalidRequest,
    ServerError,
)
from api.uploads.utils import unarchive_file_name
from app.actions.utils import get_source
from app.auth import utils as auth
from app.auth.db import (
    count_user_roles,
    increment_company_upload_documents_left,
    select_default_recipients,
    select_recipient_email_from_company,
    select_registered_companies_edrpous,
)
from app.auth.schemas import CompanyConfig
from app.auth.types import (
    Auth<PERSON>ser,
    Company,
    User,
)
from app.auth.utils import (
    can_user_view_all_company_documents,
    get_company_config,
    has_permission,
    update_company_tags_count,
)
from app.billing.api import charge_document
from app.billing.constants import RATES_NAME_MAP
from app.billing.db import select_active_company_rates
from app.billing.enums import CompanyLimit
from app.billing.types import CompanyRate
from app.billing.utils import get_billing_company_config, get_rate_config_limits
from app.comments.db import delete_comments
from app.comments.enums import CommentType
from app.comments.types import Comment
from app.comments.utils import send_comments_for_indexation
from app.contacts.db import (
    select_contact_email_by_edrpou,
    update_main_recipient,
)
from app.contacts.types import ContactDetails
from app.document_automation.utils import start_document_automation
from app.document_versions import utils as document_versions_utils
from app.document_versions.db import (
    select_latest_document_versions,
)
from app.document_versions.types import UpdateDocumentVersionSettingsOutput
from app.document_versions.utils import (
    get_document_version_key,
    get_version_count,
    is_versioned_document,
    send_document_version,
    update_document_version_settings,
)
from app.documents import (
    db,
    emailing,
)
from app.documents.constants import TTL_HIDDEN_EMAIL
from app.documents.db import (
    add_document_child,
    add_documents_links,
    add_documents_meta,
    build_private_document_access_filter,
    change_documents_for_public_api,
    create_document_access_for_recipient,
    delete_document_child,
    delete_documents_from_db,
    delete_documents_links,
    delete_empty_listing,
    delete_listing_source,
    delete_recipients_access,
    exists_listing,
    filter_visible_document_ids_for_user,
    get_documents_pending_delete_requests_mapping,
    insert_delete_requests,
    insert_listings,
    insert_recipients,
    select_bilateral_document_recipient,
    select_delete_document_settings,
    select_document_by_id,
    select_document_for_recipient_email,
    select_document_listings_with_tag_source,
    select_document_meta,
    select_document_recipients,
    select_documents_by_ids,
    select_documents_by_ids_with_company_info,
    select_listings_by_source,
    update_document,
    update_document_date_delivered_none,
)
from app.documents.emailing import (
    send_document_delete_request_accepted_emails,
    send_document_delete_request_notifications,
    send_reject_delete_request_emails,
)
from app.documents.enums import (
    AccessSource,
    DeleteRequestStatus,
    DocumentAccessLevel,
    FirstSignBy,
    UpdateTagsAction,
    UpdateViewersAction,
)
from app.documents.notifications import (
    DocumentFinishedNotification,
    DocumentInboxInviteRoleNotification,
    DocumentInboxNotification,
)
from app.documents.tables import (
    company_listing_table,
    delete_request_table,
    listing_table,
)
from app.documents.types import (
    AcceptDeleteReqData,
    AddDocumentChildCtx,
    AddDocumentChildrenCtx,
    BillingDocumentsLimitCtx,
    ChangeBilateralRecipientCtx,
    ChangeBilateralRecipientResult,
    ConvertOfficeDocumentToPDFCtx,
    DeleteDocumentChildCtx,
    DeleteDocumentSettings,
    DeleteListingsCtx,
    DeleteRecipientsResources,
    Document,
    DocumentBilateralSide,
    DocumentRecipient,
    DocumentRecipientStateItem,
    DocumentRecipientWithFlow,
    DocumentSignaturesCounter,
    DocumentWithUploader,
    ListingDataAggregator,
    RecipientAggregatedData,
    RecipientEmailsAggregator,
    RecipientsEmailsOptions,
    SendDocumentOptions,
    SendVersionedDocumentCtx,
    UpdateDocumentAccessSettingsCtx,
    UpdateDocumentDict,
    UpdateDocumentOptions,
    UpdateRecipientsExtendedCtx,
    UpdateTagsSettings,
    UpdateVersionedCtx,
    UpdateViewersData,
    UpsertDocumentRecipientDict,
)
from app.documents_fields.utils import update_document_parameters
from app.drafts.db import select_drafts
from app.drafts.utils import get_draft_s3_key
from app.es.models.document import Document as ESDocument
from app.esputnik.enums import Event
from app.events import document_actions
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.flow.types import AddFlowOptions, CreateFlowCtx, FlowItem
from app.groups.db import (
    delete_group_document_accesses,
    insert_group_document_access,
    select_group_document_accesses,
)
from app.groups.utils import get_group_members_by_group_ids
from app.i18n import _
from app.i18n.types import LazyI18nString
from app.lib import (
    edi,
    eusign_utils,
    s3_utils,
    tracking,
)
from app.lib.brokers import KafkaClient
from app.lib.chunks import gather_chunks
from app.lib.constants import (
    DOC_FOLDER_TO_TITLE_MAP,
    EDI_CHANGES_STATUSES,
    EDI_COMDOC006_DOC_TYPE,
    EDI_COMDOC_DOC_TYPE,
    EDI_DELNOT_DOC_TYPE,
    EDI_FINISH_STATUS_DOC_TYPES,
    EDI_RETCOMDOC_DOC_TYPE,
    STATUS_TO_TITLE_MAP,
)
from app.lib.database import DBConnection, DBRow, ensure_connection_open
from app.lib.datetime_utils import utc_now
from app.lib.edi import (
    EDIPayloadBase,
    EDIPayloadDeleteDocumentRequestByEdo,
    EDIPayloadFinishDealByComdoc,
    EDIPayloadFinishDealByInvoice,
    EDIPayloadFinishDealByRetcomdoc,
    EDIPayloadHandleChangeStatusDocument,
)
from app.lib.enums import (
    DocumentFolder,
    DocumentStatus,
    SignersSource,
    Source,
    enum_items,
)
from app.lib.gotenberg import GotenbergClient, is_landscaped
from app.lib.helpers import (
    clear_duplicates,
    find_remove_in_list,
    get_url2pdf_base_url,
    split_comma_separated_emails,
    to_json,
)
from app.lib.locks import redis_lock
from app.lib.logging import truncate_logging_extra
from app.lib.result import GenericResult
from app.lib.types import (
    DataDict,
    Redis,
)
from app.lib.urls import build_url
from app.lib.validators import is_valid_email
from app.models import select_all
from app.profile.utils import generate_esputnik_documents_sent_event
from app.registration.utils import is_valid_email_domains
from app.reviews import utils as reviews
from app.services import services
from app.sign_sessions import utils as sign_sessions
from app.sign_sessions.enums import SignSessionVendor
from app.signatures.db import (
    delete_document_signers,
    exists_signature_by_document_id,
    select_document_signers_extended,
    select_signatures,
)
from app.signatures.types import DocumentSignerWithEdrpou, Signature
from app.tags.db import select_document_tags_ids
from worker import topics

APPROVED = DocumentStatus.approved
FINISHED = DocumentStatus.finished
SENT = DocumentStatus.sent
SIGNED = DocumentStatus.signed
SIGNED_AND_SENT = DocumentStatus.signed_and_sent
READY_TO_BE_SIGNED = DocumentStatus.ready_to_be_signed

logger = logging.getLogger(__name__)

get_document = db.select_document
get_document_recipients = db.select_document_recipients
get_document_recipient = db.select_document_recipient

RESTRICTED_RECEIVING_DOCUMENTS_EMAILS = ('<EMAIL>',)


async def get_expected_document(
    conn: DBConnection,
    *,
    document_id: str,
) -> Document:
    document = await get_document(conn, document_id=document_id)
    if not document:
        raise InvalidRequest(reason=_('Документ не знайдено'))

    return document


async def schedule_remove_company_listings_es(company_listing_ids: list[str]) -> None:
    """
    Deletes docs from ES for companies that lost their access
    """
    if not company_listing_ids:
        return

    await services.kafka.send_record(
        topic=topics.REMOVE_DOCUMENTS_FROM_INDEX,
        value={'company_listing_ids': company_listing_ids},
    )


def combine_recipients_with_flows(
    flows: list[FlowItem],
    recipients: list[DocumentRecipient],
) -> list[DocumentRecipientWithFlow]:
    """
    In this function, we attach flow to each recipient. There is 1-to-1 mapping between
    recipients and flows for multilateral documents, for other types of documents flow
    can be None.
    """

    flows_mapping = {flow.recipient_id: flow for flow in flows}

    new_recipients: list[DocumentRecipientWithFlow] = []
    for recipient in recipients:
        flow = flows_mapping.get(recipient.id)
        new_recipient = DocumentRecipientWithFlow.from_objects(recipient=recipient, flow=flow)
        new_recipients.append(new_recipient)

    return new_recipients


async def change_bilateral_document_recipient(
    conn: DBConnection,
    user: User,
    raw_data: DataDict,
    request_source: Source,
) -> Document:
    """
    Change bilateral document recipient
    """
    from app.documents.validators import validate_change_bilateral_recipient

    options = await validate_change_bilateral_recipient(conn=conn, raw_data=raw_data, user=user)

    update = DocumentUpdate(document=options.document)
    await update.perform_update(
        conn=conn,
        options=options,
        source=request_source,
    )
    return update.document


async def _get_change_bilateral_recipient_ctx(
    conn: DBConnection,
    *,
    document_id: str,
    document_edrpou_owner: str,
    new_recipient_edrpou: str,
    new_recipient_emails: list[str],
    new_first_sign_by: FirstSignBy,
    previous_is_bilateral: bool,
    previous_first_sign_by: FirstSignBy | None,
    previous_recipients: list[DocumentRecipient],
) -> ChangeBilateralRecipientCtx | None:
    """
    Prepare context for changing bilateral recipient for a document
    """
    other_recipients = [r for r in previous_recipients if r.edrpou != document_edrpou_owner]

    # List of all previous recipients, except the owner
    all_previous_recipients_edrpous = list({recipient.edrpou for recipient in other_recipients})

    # List of all previous recipients that was removed from the list of recipients.
    # In most of the cases, everyone except the owner and the new recipient should lose access and
    # objects related to the document
    removed_recipients_edrpous = list(
        {
            recipient.edrpou
            for recipient in other_recipients
            if recipient.edrpou != new_recipient_edrpou
        }
    )

    # In case when we are changing from multilateral to a bilateral document, everyone except the
    # owner and new recipient should lose access to the document. Also, we don't send the document
    # to the new recipient automatically.
    if not previous_is_bilateral:
        return ChangeBilateralRecipientCtx(
            edrpous_to_unsend=removed_recipients_edrpous,
            should_send_to_recipient=False,
            emails_to_remove_access=[],
        )

    # 1 owner + 1 recipient = 2 in total -> bilateral document
    previous_bilateral_recipient = other_recipients[0] if len(other_recipients) == 1 else None

    # This case can occur when the document is bilateral, but the recipients are not set yet.
    # In this case, no additional sending is required.
    if not previous_bilateral_recipient:
        return ChangeBilateralRecipientCtx(
            edrpous_to_unsend=removed_recipients_edrpous,
            should_send_to_recipient=False,
            emails_to_remove_access=[],
        )

    # Detect requests where nothing was changed for bilateral documents and skip such requests
    if previous_first_sign_by == new_first_sign_by and previous_bilateral_recipient.is_equal_by(
        edrpou=new_recipient_edrpou,
        emails=new_recipient_emails,
    ):
        return None

    # ===> This is the beginning of the case when we are changing recipient for a bilateral
    # document and EDRPOU or emails are different for the new recipient. In this case, we should
    # detect if you need to resend the document to the new recipient or unsend it from the previous
    # bilateral recipient.

    had_recipient_access = await exists_listing(
        conn=conn,
        document_id=document_id,
        edrpou=previous_bilateral_recipient.edrpou,
    )

    # There may be a case where the previous recipient had access to the document,
    # but after changing the signing order from "recipient signs first" to "owner signs
    # first" we should remove access to the document for this recipient. It's because in
    # the typical situation, when a document is on the owner side and an owner is signing
    # first, the recipient should receive the document **only** after the owner signs and
    # sends it.
    # It's also important to note that this scenario can only occur when the document
    # hasn't been signed yet, as we don't allow order changes in other cases.
    if new_first_sign_by == FirstSignBy.owner and previous_first_sign_by == FirstSignBy.recipient:
        return ChangeBilateralRecipientCtx(
            edrpous_to_unsend=all_previous_recipients_edrpous,
            should_send_to_recipient=False,
            emails_to_remove_access=[],
        )

    # In other cases, open new access for a new recipient (light version of the
    # document sending) only when the previous bilateral recipient had access to the
    # document. Possible cases:
    #  - from "first sign by owner" to "first sign by owner"
    #  - from "first sign by recipient" to "first sign by recipient"
    #  - from "first sign by owner" to "first sign by recipient"
    should_send_to_recipient = had_recipient_access

    # After reverting sending the document to the previous recipients, we can still have a
    # situation where only email was changed for the recipient, but EDRPOU is the same. In
    # this case, we need to delete default access ("recipient" access) for the previous recipient
    # email and create a new one for the new recipient
    emails_to_remove_access: list[str] = []
    if previous_bilateral_recipient.edrpou == new_recipient_edrpou:
        previous_emails = {email.lower() for email in previous_bilateral_recipient.emails}
        new_emails = {email.lower() for email in new_recipient_emails}
        emails_to_remove_access = list(previous_emails - new_emails)

    return ChangeBilateralRecipientCtx(
        edrpous_to_unsend=removed_recipients_edrpous,
        should_send_to_recipient=should_send_to_recipient,
        emails_to_remove_access=emails_to_remove_access,
    )


async def change_bilateral_recipient_base(
    conn: DBConnection,
    *,
    document_id: str,
    document_edrpou_owner: str,
    document_title: str,
    new_recipient_edrpou: str,
    new_recipient_emails: list[str],
    new_recipient_emails_hidden: bool,
    new_first_sign_by: FirstSignBy,
    previous_is_bilateral: bool,
    previous_first_sign_by: FirstSignBy | None,
    previous_recipients: list[DocumentRecipient],
    previous_status: DocumentStatus,
    current_user: User,
) -> ChangeBilateralRecipientResult:
    """
    Change recipient for a bilateral document.

    This function can be called when a type of documents is changing from multilateral to bilateral,
    so the argument "all_previous_recipients" can contain more than 2 companies and you should
    handle that case properly.

    Not all "new_*" arguments are updated in the database, sometimes they needed to detect
    changes between new and previous settings.
    """
    from api.public.utils import update_processed_status

    update = ChangeBilateralRecipientResult()

    ctx = await _get_change_bilateral_recipient_ctx(
        conn=conn,
        document_id=document_id,
        document_edrpou_owner=document_edrpou_owner,
        new_recipient_edrpou=new_recipient_edrpou,
        new_recipient_emails=new_recipient_emails,
        new_first_sign_by=new_first_sign_by,
        previous_is_bilateral=previous_is_bilateral,
        previous_first_sign_by=previous_first_sign_by,
        previous_recipients=previous_recipients,
    )

    if not ctx:
        return update

    # We have already checked that the recipient is changing,
    # so that property equals to 'should_open_recipient_access'
    update.resend_to_recipient = ctx.should_send_to_recipient

    async with conn.begin():
        # The next function doesn't delete recipients because the goal of this function is to move
        # to the state when recipients are set, but a document is not sent to them yet. After this,
        # you can more safely replace and send the document to the new recipients.
        # The process of changing recipients can be represented as: → unsend → replace → send
        deleted_resources = await unsend_document_to_recipients(
            conn=conn,
            document_id=document_id,
            companies_edrpous=list(ctx.edrpous_to_unsend),
        )
        update.deleted = deleted_resources

        # Recipient might be updated in unsend_document_to_recipients function, so we need to
        # fetch a fresh list of recipients to properly recreate them.
        previous_recipients = await db.select_document_recipients(conn, document_id=document_id)

        await update_bilateral_recipient(
            conn=conn,
            document_id=document_id,
            document_owner_edrpou=current_user.company_edrpou,
            new_recipient_edrpou=new_recipient_edrpou,
            new_recipient_emails=new_recipient_emails,
            new_recipient_emails_hidden=new_recipient_emails_hidden,
            prev_recipients=previous_recipients,
        )

        if ctx.should_send_to_recipient:
            await send_bilateral_document_to_recipient(
                conn=conn,
                document_id=document_id,
                recipient_edrpou=new_recipient_edrpou,
                recipient_emails=new_recipient_emails,
            )
            # NOTE: this function returns a DeleteListingsCtx object with deleted company listing,
            # but we don't need to use it in this case because the list "emails_to_remove_access"
            # and "new_recipient_emails" should not intersect. Which means that new recipient
            # EDRPOU always had access to the document in this case.
            #
            # Also pay attention that we delete recipient access for the previous recipient emails
            # after creating new access for the new recipient to avoid deleting and then creating
            # company listing for the same company at the same time.
            await delete_default_access(
                conn=conn,
                document_id=document_id,
                company_edrpou=new_recipient_edrpou,
                user_emails=ctx.emails_to_remove_access,
            )

        await update_processed_status(
            conn=conn,
            document_id=document_id,
            status_id=previous_status.value,
            processed=False,
            user_company_id=current_user.company_id,
        )

        if update.resend_to_recipient:
            await update_main_contacts_on_document_update(
                conn=conn,
                company_id=current_user.company_id,
                recipient_edrpou=new_recipient_edrpou,
                recipient_emails=new_recipient_emails,
                is_emails_hidden=new_recipient_emails_hidden,
            )

    await document_actions.add_document_action(
        document_action=document_actions.DocumentAction(
            action=document_actions.Action.document_change_recipient,
            document_id=document_id,
            document_edrpou_owner=document_edrpou_owner,
            document_title=document_title,
            company_id=current_user.company_id,
            company_edrpou=current_user.company_edrpou,
            email=current_user.email,
            role_id=current_user.role_id,
        )
    )

    # NOTE: remember to call `send_change_bilateral_document_recipient_jobs` outside
    #   transaction context in which current function is called.
    return update


async def send_change_bilateral_document_recipient_jobs(
    conn: DBConnection,
    document_id: str,
    recipient_edrpou: str,
    recipient_emails: list[str],
    user: User,
    request_source: Source,
) -> None:
    """
    Send asynchronous jobs after bilateral document recipient was changed.

    Use this function outside of transaction context and in combination with
    "change_bilateral_recipient_base" function.
    """
    for recipient_email in recipient_emails:
        await share_document(
            conn=conn,
            document_id=document_id,
            recipient_edrpou=recipient_edrpou,
            recipient_email=recipient_email,
            sender=user,
        )

    await connect_document_with_tags_by_contact(
        edrpou=recipient_edrpou,
        contact_edrpou=user.company_edrpou,
        documents_ids=[document_id],
    )
    # Find and apply template for document in recipient company
    await start_document_automation(
        document_id=document_id,
        company_edrpou=recipient_edrpou,
        source=request_source,
    )


async def get_send_versioned_document_ctx(
    conn: DBConnection,
    document_id: str,
) -> SendVersionedDocumentCtx:
    """
    Get some additional context about versioned document on document sending
    """

    is_versioned = await is_versioned_document(conn, document_id=document_id)
    versions_count = await get_version_count(
        conn=conn,
        document_id=document_id,
        is_sent=True,
    )
    has_signatures = await exists_signature_by_document_id(conn, document_id)
    return SendVersionedDocumentCtx(
        is_versioned=is_versioned,
        versions_count=versions_count,
        has_signatures=has_signatures,
    )


def _check_restricted_email_for_sending(options: SendDocumentOptions) -> None:
    """
    From Jira DOC-6236:

    [crutch] Не даємо відправляти документи на загальну пошту Оранти

    Звернувся клієнт Оранта (ЄДРПОУ 00034186) з проханням прибрати можливість відправляти
    відправляти компаніям документи на їх публічну пошту <EMAIL>, бо у них в публічній
    інформації прописано, що з моменту надходження інфи на цю адресу їм треба протягом певного
    часу опрацювати такий документ. Це загальна пошта і вона не зареєстрована у Вчасно,
    того інформування про нові документи йде на сапорт, який не має доступу до документів. І такі
    документу губляться, бо адміни на Вчасно також не опрацьовують ці документи. Ну і наймати
    окрему людину, щоб розгрібала ці документи також не хочуть Просять поки вони не налаштувати
    інтеграцію із їх внутрішнім документообігом, то заборонити відправку документів на цю пошту.
    Обіцяють зробити в 3 кварталі цього року (2024).

    Можемо виводити помилку "Даний емейл не зареєстрований на Вчасно, вкажіть іншу адресу"
    """

    # DOC-6236 - temporary fix for 00034186
    if options.recipient and any(
        restricted_email in options.recipient.emails
        for restricted_email in RESTRICTED_RECEIVING_DOCUMENTS_EMAILS
    ):
        raise InvalidRequest(
            reason=_('Даний емейл не зареєстрований на Вчасно, вкажіть іншу адресу')
        )


async def send_document(
    conn: DBConnection,
    user: User | None,
    company_edrpou: str,
    raw_data: DataDict,
    request_source: Source,
) -> SendDocumentOptions | None:
    """
    Send document or new version of the document to the recipient or coworkers.

    NOTE: this function should not be called during internal process of signing (document signers).
    Logic of sending document to the next signer is implemented in the "add_signature" function.
    """
    from app.documents.validators import (
        validate_document_exists,
        validate_send_document,
    )
    from app.es.utils import send_to_indexator
    from app.flow import utils as flow_utils
    from app.flow import validators as flow_validators

    redis = services.redis
    document_id = raw_data['document_id']

    async with redis_lock(f'lock_send_document_{document_id}'):
        document = await validate_document_exists(conn, {'document_id': document_id})

        versioned_ctx = await get_send_versioned_document_ctx(conn, document_id=document.id)
        # If it's not first version of the versioned document we use simpler path to send
        # the new version of the document to the recipient. In this case we consider that
        # document is already sent to the recipient, and we don't need to send it again.
        if versioned_ctx.should_sent_next_version:
            await send_document_version(
                conn,
                document_id=document.id,
                sender_edrpou=company_edrpou,
                user=user,
                is_first_version=False,
            )
            return None

        # Multilateral document has its own logic of sending document to the recipient
        # See comments in the "send_multilateral_document" function for more details.
        if document.is_multilateral:
            flows_state = await flow_utils.get_flows_state(conn, document_id=document.id)
            await flow_validators.validate_first_send_multilateral_document(
                conn=conn,
                document=document,
                current_company_edrpou=company_edrpou,
                state=flows_state,
            )
            await flow_utils.send_multilateral_document(
                conn=conn,
                user=user,
                company_edrpou=company_edrpou,
                document=document,
                state=flows_state,
                request_source=request_source,
            )
            return None

        options = await validate_send_document(
            conn=conn,
            redis=redis,
            data=raw_data,
            document=document,
            user=user,
            company_edrpou=company_edrpou,
        )

        # In some cases such as when user sign multilateral document or user add
        # not last expected signature, we must ignore this requests from fronted.
        # TODO: fix sending requests from fronted and remove this condition
        if not options:
            logging.info(
                'Request is ignored',
                extra={'raw_data': truncate_logging_extra(raw_data)},
            )
            return None

        _check_restricted_email_for_sending(options)

        logging.info(
            msg='Send document',
            extra={
                'next_status': options.next_status,
                'uploader_company_id': options.uploader_company_id,
                'charge_context': options.charge_context,
                'recipient': options.recipient and options.recipient.as_dict(),
                'is_owner_sending': options.is_owner_sending,
                'is_one_sign': options.is_one_sign,
                'is_3p': options.is_3p,
                'is_sending_to_recipient': options.is_sending_to_recipient,
                'company_edrpou': options.company_edrpou,
                'user_email': user and user.email,
                'user_edrpou': user and user.company_edrpou,
                'user_role_id': user and user.role_id,
                'document': dict(options.document.to_log_extra()),
            },
        )

        async with conn.begin():
            # Sending first version of the document should be used in combination with
            # the whole logic of sending the document to the recipient, in order to
            # perform all necessary actions such as open access for the recipient.
            if versioned_ctx.should_sent_first_version:
                await send_document_version(
                    conn,
                    document_id=document.id,
                    sender_edrpou=company_edrpou,
                    user=user,
                    is_first_version=True,
                )

            # There are two cases when user request this function:
            #  - when document must be sent from one company to another.
            #  - when document must be sent from coworker to coworker
            if options.is_sending_to_recipient:
                await send_bilateral_document_to_company(
                    conn=conn,
                    options=options,
                    user=user,
                    request_source=request_source,
                )
            else:
                # Case when document sent from coworker to coworker. For example, when document
                # has more signatures expected from this company.
                # NOTE: we don't send document between document signers (during internal process
                # of signing), because adding signature by signer has logic of sending document
                # to the next signer.
                await send_document_to_coworker(conn, options)

            document = options.document

        if user:
            await document_actions.add_document_action(
                document_action=document_actions.DocumentAction(
                    action=document_actions.Action.document_send,
                    document_id=document.id,
                    document_edrpou_owner=document.edrpou_owner,
                    document_title=document.title,
                    company_id=user.company_id,
                    company_edrpou=user.company_edrpou,
                    email=user.email,
                    role_id=user.role_id,
                )
            )

        await send_document_status_callback_job(
            document_id=document.id,
            uploaded_by_edrpou=document.uploaded_by_edrpou,
        )

        if options.next_status == DocumentStatus.finished:
            await schedule_jobs_about_finished_document(document=document)

        await generate_esputnik_documents_sent_event(conn, user, document)

        await send_to_indexator(document_ids=[document.id], to_slow_queue=False)
        await services.kafka.send_record(
            topic=topics.SEND_COMMENTS_TO_INDEX,
            value={'document_ids': [document.id]},
        )

        return options


async def convert_xml_to_pdf(
    user: AuthUser | User,
    document_id: str,
    s3_xml_to_pdf_key: str | None,
    *,
    render_signatures_table: bool = False,
    render_signatures_visualisation: bool = True,
) -> s3_utils.UploadFile:
    """Convert XML document to PDF via external URL2PDF service."""
    config = services.config.url2pdf
    request_timeout = config.timeout

    from app.sign_sessions.utils import get_sign_session_vendor

    async with services.db.acquire() as conn:
        vendor = await get_sign_session_vendor(conn, user.company_edrpou)
        if vendor is None:
            logger.warning(
                'Fallback to Vchasno Sign Session vendor to create view session for the document',
                extra={
                    'company_edrpou': user.company_edrpou,
                    'company_id': user.company_id,
                    'document_id': document_id,
                    'role_id': user.role_id,
                    'user_email': user.email,
                    'user_id': user.id,
                },
            )
            vendor = SignSessionVendor.vchasno

        # Prepare view session for given user
        view_session_id = await sign_sessions.create_view_session_for_xml2pdf(
            conn=conn,
            user=user,
            document_id=document_id,
            vendor=vendor,
        )
        logger.info('View session is ready for given document')
        document = await get_expected_document(conn, document_id=document_id)

    # Convert given document from XML to PDF
    url = get_url2pdf_url(
        document_id=document_id,
        view_session_id=view_session_id,
        render_signatures_table=render_signatures_table,
        render_signatures_visualisation=render_signatures_visualisation,
        save_to_file=True,
        render_revoke_visualisation=document.status.is_revoked,
    )

    logger.info(
        'Request to URL2PDF service is ready',
        extra={
            'document_id': document_id,
            'url': url,
            'view_session_id': view_session_id,
        },
    )

    # Actual call to url2pdf service
    async with asyncio.timeout(request_timeout):
        try:
            async with services.http_client.get(url) as response:
                response.raise_for_status()
                content = await response.read()
                content_length = len(content)
        except ClientError:
            logger.warning(
                'Unable to finish request to URL2PDF service',
                exc_info=True,
                extra={'timeout': request_timeout, 'url': url},
            )
            raise ServerError(Code.invalid_url2pdf_response)

    logger.info(
        'Got response from URL2PDF service',
        extra={
            'content_length': content_length,
            'document_id': document_id,
            'url': url,
        },
    )

    # Store content to S3
    extra_ext = '.xz' if config.use_xz else ''
    s3_xml_to_pdf_key = s3_xml_to_pdf_key or f'{str(uuid.uuid4())}.pdf{extra_ext}'

    upload_file = s3_utils.UploadFile(
        key=get_xml_to_pdf_key(document_id, s3_xml_to_pdf_key),
        body=content,
    )
    await s3_utils.upload(upload_file)

    logger.info(
        'PDF document stored to S3',
        extra={
            'content_length': content_length,
            'document_id': document_id,
            's3_key': upload_file.key,
            's3_xml_to_pdf_key': s3_xml_to_pdf_key,
        },
    )

    # Finally update document table in database if it still presents
    async with services.db.acquire() as conn:
        with suppress(AttributeError):
            await update_document(
                conn,
                {
                    'document_id': document_id,
                    's3_xml_to_pdf_key': s3_xml_to_pdf_key,
                },
            )
            logger.info('Document instance updated in database')

    return upload_file


class DocumentsDelete:
    """
    Class to perform delete list of documents in one transaction.

    The main ideas of this class:
      1. gather all functions that related only to documents delete process in one place
      2. ability to run DB transaction and async_jobs separately with correct state

    Use it carefully:
      1. c = DocumentsDelete(...params)
      2. await c.perform_main_transaction() - run inside DB transaction
      3. await c.perform_async_actions() - run outside DB transaction
    """

    def __init__(
        self,
        documents: list[Document],
        delete_from_edi: bool = True,
    ) -> None:
        self._document_ids = [doc.id for doc in documents]
        self._documents = documents
        self._delete_from_edi = delete_from_edi

        # Artifacts generated during main transaction
        self._s3_keys_old: list[str] = []
        self._s3_keys_new: list[str] = []
        self._db_deleted_comment_ids: list[str] = []
        self._tags_company_ids: set[str] = set()

    async def perform_main_transaction(self, conn: DBConnection) -> None:
        """
        Runs DB queries, should be used inside DB transaction
        """
        from app.tags.db import select_tags_for_deletion

        logger.info('Delete documents', extra={'document_ids': self._document_ids})

        # Setup list of documents to delete from S3
        # Delete original file and all internally signed files as well
        self._s3_keys_old, self._s3_keys_new = await self._collect_delete_document_s3_keys(conn)

        tags = await select_tags_for_deletion(conn, self._document_ids)
        tags_ids = [row.tag_id for row in tags]
        self._tags_company_ids = {row.company_id for row in tags}

        with tracking.delete_documents_from_db.time():
            ids = await delete_documents_from_db(
                conn=conn, document_ids=self._document_ids, tags_ids=tags_ids
            )
            self._db_deleted_comment_ids = ids.comment_ids

    async def perform_async_actions(self) -> None:
        """
        Runs async jobs, should be used outside DB transaction
        """
        from app.comments.utils import schedule_remove_comments_from_index

        await services.kafka.send_record(
            topic=topics.REMOVE_DOCUMENTS_FROM_INDEX,
            value={'document_ids': self._document_ids},
        )
        await schedule_remove_comments_from_index(self._db_deleted_comment_ids)

        for cid in self._tags_company_ids:
            await update_company_tags_count(cid)

        try:
            await s3_utils.delete_batch(self._s3_keys_old)
        except Exception:
            logger.exception(
                'Unhandled exception on deleting document from S3',
                extra={'keys': self._s3_keys_old},
            )

        try:
            await s3_utils.delete_batch(self._s3_keys_new)
        except Exception:
            logger.exception(
                'Unhandled exception on deleting document from new S3',
                extra={'keys': self._s3_keys_new},
            )

        if not self._delete_from_edi:
            return

        # perform deletion from EDI
        edi_doc_ids = []
        for _document in self._documents:
            if _document.source and _document.source.is_from_edi:
                edi_doc_ids.append(_document.id)
        if edi_doc_ids:
            await gather_chunks(func=delete_document_from_edi, items=edi_doc_ids, chunk_size=10)

    async def _collect_delete_document_s3_keys(
        self,
        conn: DBConnection,
    ) -> tuple[list[str], list[str]]:
        """
        Collect S3 keys for document and related objects that should be deleted
        when document is deleted from database.

        NOTE: those keys should be deleted after database transaction is committed, to
        avoid situation when files are deleted from S3, but document is not deleted from
        database.
        """

        from app.signatures.db import select_signatures

        keys_new: list[str] = []
        keys_old: list[str] = [
            # We are collection only keys of the original file, versions are handled below
            _get_document_original_s3_key(document_id=document_id)
            for document_id in self._document_ids
        ]

        # Get keys for all document versions
        versions = await select_latest_document_versions(conn, document_ids=self._document_ids)
        for version in versions:
            keys_old.append(
                get_document_s3_key(document_id=version.document_id, version_id=version.id)
            )

        # drafts
        drafts = await select_drafts(
            conn=conn,
            document_ids=self._document_ids,
        )
        for draft in drafts:
            keys_old.append(get_draft_s3_key(draft.id))

        # Get keys for all signatures
        signatures = await select_signatures(conn, document_ids=self._document_ids)
        for signature in signatures:
            if signature.is_internal:
                key = get_internal_signature_s3_key(
                    document_id=signature.document_id,
                    signature_id=signature.id,
                )
                keys_old.append(key)

            else:
                if signature.key_exists:
                    key = get_external_key_signature_s3_key(
                        document_id=signature.document_id,
                        signature_id=signature.id,
                    )
                    keys_new.append(key)
                if signature.stamp_exists:
                    key = get_external_stamp_signature_s3_key(
                        document_id=signature.document_id,
                        signature_id=signature.id,
                    )
                    keys_new.append(key)

        for _document in self._documents:
            # If S3 XML to PDF key exists - delete it as well
            if _document.s3_xml_to_pdf_key:
                keys_old.append(get_xml_to_pdf_key(_document.id, _document.s3_xml_to_pdf_key))

        return keys_old, keys_new


async def delete_es_documents(
    documents_ids: list[str],
    company_listing_ids: list[str] | None = None,
) -> None:
    """Bulk delete documents IDs from ES"""

    if not documents_ids and not company_listing_ids:
        return

    query = None
    if documents_ids:
        query = SearchQuery(ESDocument.document_id.in_(documents_ids))
    elif company_listing_ids:
        query = SearchQuery(ESDocument._id.in_(company_listing_ids))

    if query is not None:
        await services.es.documents.delete_by_query(query, refresh=True, conflicts='proceed')


def get_signatures_count(
    signatures: list[Signature],
    target_edrpou: str,
    signers: list[DocumentSignerWithEdrpou],
) -> int:
    """
    Get count of signatures for the document from the target company. It counts only unique roles
    that signed the document. It should work for any kind of document: bilateral, multilateral,
    internal.
    """

    # current company document signers
    signers = [signer for signer in signers if signer.company_edrpou == target_edrpou]

    # current company signatures
    signatures = [
        signature
        for signature in signatures
        if signature.key_owner_edrpou == target_edrpou
        or signature.stamp_owner_edrpou == target_edrpou
    ]

    # If the document does not have document signers, then
    # each signature should be counted as a separate sign
    if not signers:
        return len(signatures)

    # Count unique roles from signatures
    signatures_roles = {signature.role_id for signature in signatures}
    signatures_roles_count = len(signatures_roles)

    # Count document signers that already signed the document
    signers_signed = [signer for signer in signers if signer.date_signed]
    signers_signed_count = len(signers_signed)

    # Might be situation when there are fewer signatures than signers but still may be "ready"
    # because one role can "fill" multiple signers (as a role and as a member of a group).
    # In this case, we should return the maximum value of completed signers or unique roles.
    return max(signatures_roles_count, signers_signed_count)


def get_document_signatures_counter(
    edrpou_owner: str,
    edrpou_recipient: str | None,
    expected_owner_signatures: int,
    expected_recipient_signatures: int,
    first_sign_by: FirstSignBy,
    signatures: list[Signature],
    document_signers: list[DocumentSignerWithEdrpou],
) -> DocumentSignaturesCounter:
    """
    Count signatures by owner and recipient companies for internal and bilateral documents.

    For multilateral documents, you can use "signatures_count" and "pending_signatures" flow fields
    to find out how many signatures are already added to the document.
    """
    is_same_edrpou = edrpou_owner == edrpou_recipient

    owner_count = get_signatures_count(
        signatures=signatures,
        target_edrpou=edrpou_owner,
        signers=document_signers,
    )

    # There are valid cases, where we don't have the recipient's EDRPOU or don't care about it,
    # like first sending of the document from owner to recipient.
    recipient_count = 0
    if edrpou_recipient:
        recipient_count = get_signatures_count(
            signatures=signatures,
            target_edrpou=edrpou_recipient,
            signers=document_signers,
        )

    # Special case when a document is sent from one company to itself (bilateral).
    # In this case, we should start counting from the side that sign the document first.
    if is_same_edrpou:
        # Such as a document is sent from one company to itself, owner and recipient
        # signatures count should be the same and equal to the total signatures count.
        total_count = owner_count
        if first_sign_by == FirstSignBy.owner:
            owner_count = min(expected_owner_signatures, owner_count)
            recipient_count = total_count - owner_count
        else:
            recipient_count = min(expected_recipient_signatures, owner_count)
            owner_count = total_count - recipient_count

    return DocumentSignaturesCounter(
        owner_count=owner_count,
        recipient_count=recipient_count,
    )


def get_document_next_status_sending(
    *,
    document: Document,
    signatures: list[Signature],
    document_signers: list[DocumentSignerWithEdrpou],
) -> DocumentStatus:
    """
    Returns next document status on document sending.
    """

    edrpou_owner = document.edrpou_owner
    edrpou_recipient = document.edrpou_recipient
    expected_owner_signatures = document.expected_owner_signatures
    expected_recipient_signatures = document.expected_recipient_signatures
    first_sign_by = document.first_sign_by
    status_id = document.status_id
    is_one_sign = is_one_sign_document(document)

    counter = get_document_signatures_counter(
        edrpou_owner=edrpou_owner,
        edrpou_recipient=edrpou_recipient,
        expected_owner_signatures=expected_owner_signatures,
        expected_recipient_signatures=expected_recipient_signatures,
        first_sign_by=first_sign_by,
        signatures=signatures,
        document_signers=document_signers,
    )
    recipient_signatures_count = counter.recipient_count
    owner_signatures_count = counter.owner_count

    logger.info(
        msg='Next sending status',
        extra={
            'status_id': status_id,
            'is_one_sign': is_one_sign,
            'expected_owner_signatures': expected_owner_signatures,
            'expected_recipient_signatures': expected_recipient_signatures,
            'owner_signatures_count': owner_signatures_count,
            'recipient_signatures_count': recipient_signatures_count,
        },
    )

    is_enough_owner_signatures = (
        expected_owner_signatures and owner_signatures_count >= expected_owner_signatures
    )
    is_enough_recipient_signatures = (
        expected_recipient_signatures
        and recipient_signatures_count >= expected_recipient_signatures
    )

    if status_id == SIGNED.value:
        # Enough owner signatures -> signed and sent
        # Enough recipient signatures -> signed and sent
        if is_enough_owner_signatures or is_enough_recipient_signatures:
            return SIGNED_AND_SENT

        # Any other case -> remains signed
        return SIGNED

    if status_id == APPROVED.value:
        # One Sign + Enough owner signatures -> finished
        # One Sign + Enough recipient signatures -> finished
        if is_one_sign:
            if is_enough_owner_signatures or is_enough_recipient_signatures:
                return FINISHED
        # Enough owner + enough recipient signatures -> finished
        elif is_enough_owner_signatures and is_enough_recipient_signatures:
            return FINISHED

        # Any other case -> remains approved
        return APPROVED

    # Any other case -> sent
    return SENT


def get_internal_document_next_status_signing(
    user: User | AuthUser | None,
    signers: list[DocumentSignerWithEdrpou],
    current_status: DocumentStatus,
    signatures_expected: int,
    signatures_count: int,
    group_members: defaultdict[str, list[str]],
) -> DocumentStatus:
    """
    Returns next internal document status after signing.

    NOTE: "signatures" and "singers" don't contain the information about the new signature that
    will be added later in this request
    """
    # TODO: investigate why we are returning FINISHED status in this case
    if not user:
        logger.warning(
            msg='User is not provided for internal document signing',
            extra={
                'signatures_expected': signatures_expected,
                'signatures_count': signatures_count,
                'current_status': current_status,
            },
        )
        return FINISHED

    if not signers:
        return FINISHED if signatures_expected <= signatures_count + 1 else SIGNED
    # For signing, we need to count the signatures of the owner like he/she is already
    # signed the document. So we use +1 here.
    # INFO: The user can provide "expected_owner_signatures" by API, or we set it to 1 by default
    # when there are no document signers.
    # Get all pending signers
    pending_signers = [signer for signer in signers if not signer.is_signed]

    # Check if user is direct signer
    user_is_direct_signer = any(user.role_id == s.role_id for s in pending_signers if s.role_id)

    # Check if user is group member
    user_is_group_member = any(
        user.role_id in group_members[s.group_id] for s in pending_signers if s.group_id
    )

    is_pending_signer = user_is_direct_signer or user_is_group_member

    # If user is not a pending signer, return current status
    if not is_pending_signer:
        return current_status

    signatures_to_satisfy = (
        0  # Count how many signatures we need to satisfy for getting the FINISHED status
    )

    for signer in pending_signers:
        if signer.role_id and signer.role_id == user.role_id:
            continue

        if signer.group_id and user.role_id in group_members[signer.group_id]:
            continue

        signatures_to_satisfy += 1

    # Check remaining signatures needed
    remaining_signatures_needed = max(0, signatures_expected - signatures_count - 1)

    # If current signature is the last one needed
    if signatures_to_satisfy == 0 or remaining_signatures_needed <= 0:
        return FINISHED

    # Or if we don't have enough signatures to satisfy
    return SIGNED


def get_document_next_status_signing(
    user: AuthUser | User | None,
    document: Document,
    signatures: list[Signature],
    document_signers: list[DocumentSignerWithEdrpou],
    group_members: defaultdict[str, list[str]],
) -> DocumentStatus:
    """
    Returns next document status after signing.
    """
    is_expects_owner_signature = is_wait_owner_signature(document=document)
    counter = get_document_signatures_counter(
        edrpou_owner=document.edrpou_owner,
        edrpou_recipient=document.edrpou_recipient,
        expected_owner_signatures=document.expected_owner_signatures,
        expected_recipient_signatures=document.expected_recipient_signatures,
        first_sign_by=document.first_sign_by,
        signatures=signatures,
        document_signers=document_signers,
    )
    is_3p = document.first_sign_by == FirstSignBy.recipient
    is_one_sign = is_one_sign_document(document)
    owner_signatures_count = counter.owner_count

    logger.info(
        msg='Next signing status',
        extra={
            'is_expects_owner_signature': is_expects_owner_signature,
            'expected_owner_signatures': document.expected_owner_signatures,
            'owner_signatures_count': owner_signatures_count,
            'is_3p': is_3p,
            'is_one_sign': is_one_sign,
        },
    )
    if is_expects_owner_signature:
        if document.is_internal:
            return get_internal_document_next_status_signing(
                user=user,
                signers=document_signers,
                current_status=document.status,
                # for internal documents, we use only "owner" signatures for counting
                signatures_expected=document.expected_owner_signatures,
                signatures_count=owner_signatures_count,
                group_members=group_members,
            )

        # 3P document + not last owner signature -> approved
        if is_3p:
            return APPROVED

        # Not last owner signature + only by owner -> approved
        # Not last owner signature + not only by owner -> signed
        return APPROVED if is_one_sign else SIGNED

    # 3P document + not last signature + only by recipient -> approved
    # 3P document + not last signature + not only by recipient -> signed
    if is_3p:
        return APPROVED if is_one_sign else SIGNED

    # Not last recipient signature -> approved
    return APPROVED


def get_internal_signature_s3_key(document_id: str, signature_id: str) -> str:
    return f'{document_id}_{signature_id}'


def get_external_key_signature_s3_key(document_id: str, signature_id: str) -> str:
    return f'{document_id}_{signature_id}_key'


def get_external_stamp_signature_s3_key(document_id: str, signature_id: str) -> str:
    return f'{document_id}_{signature_id}_stamp'


def get_url2pdf_url(
    document_id: str,
    view_session_id: str,
    *,
    save_to_file: bool = False,
    render_signatures_table: bool = False,
    render_signatures_visualisation: bool = False,
    render_revoke_visualisation: bool = False,
) -> str:
    download_url = '{base}?{params}'.format(
        base=build_url(
            route='downloads.signed_file',
            document_id=document_id,
            absolute=False,
        ),
        params=urlencode({'ssid': view_session_id}),
    )

    params = {'file': download_url, 'expand': 1}
    if save_to_file:
        params['saveToFile'] = 1
    if render_signatures_visualisation:
        params['renderSignatureInInterface'] = 1
    if render_signatures_table:
        params['renderSignaturesTable'] = 1
    if render_revoke_visualisation:
        params['renderAnnulmentMarkDocument'] = 1

    viewer_url = f'{build_url("xml_viewer")}?{urlencode(params)}'

    config = services.config.url2pdf
    query = {'url': viewer_url}
    if config.use_xz:
        query['xz'] = '1'

    base_url = get_url2pdf_base_url()

    return f'{base_url}?{urlencode(query)}'


def get_xml_to_pdf_key(document_id: str, s3_xml_to_pdf_key: str) -> str:
    return f'xml-to-pdf/{document_id}/{s3_xml_to_pdf_key}'


def is_first_sign_by_recipient_document(document: DBRow | Document) -> bool:
    """Check whether given document expects to be first signed by recipient."""
    return document.first_sign_by == FirstSignBy.recipient


def is_bilateral_3p_not_sent_document(document: Document) -> bool:
    """
    Is a document bilateral and first sign by recipient and not sent to recipient yet.
    """
    return (
        document.is_bilateral
        and is_first_sign_by_recipient_document(document)
        and document.status
        in (
            DocumentStatus.uploaded,
            DocumentStatus.ready_to_be_signed,
        )
    )


def is_one_sign_document(document: DBRow | Document) -> bool:
    """
    Check if only one side of the bilateral document is expected to sign the document.
    """
    first_sign_by: FirstSignBy = document.first_sign_by

    if first_sign_by == FirstSignBy.recipient:
        return document.expected_owner_signatures == 0

    if first_sign_by == FirstSignBy.owner:
        return document.expected_recipient_signatures == 0

    assert_never(first_sign_by)


def is_wait_owner_signature(document: DBRow | Document) -> bool:
    """
    This functions checks whether the document is on the owner side of the signing process for
    internal and bilateral documents.

    See diagram here for more details:
     - https://tabula-rasa.atlassian.net/wiki/spaces/vchasno/pages/4952001
    """
    if document.is_internal:
        return True

    # For multilateral documents, you can use "documents_flows.pending_signatures" for this purpose
    assert document.is_multilateral is False, 'Not allowed for multilateral documents'

    document_status = DocumentStatus(document.status_id)
    first_sign_by: FirstSignBy = document.first_sign_by

    if first_sign_by == FirstSignBy.owner:
        # only owner should sign the document
        if document.expected_recipient_signatures == 0:
            return document_status in (
                DocumentStatus.uploaded,
                DocumentStatus.ready_to_be_signed,
                DocumentStatus.approved,
            )

        # owner and recipient should sign the document
        return document_status in (
            DocumentStatus.uploaded,
            DocumentStatus.ready_to_be_signed,
            DocumentStatus.signed,
        )

    if first_sign_by == FirstSignBy.recipient:
        # only recipient should sign the document
        if document.expected_owner_signatures == 0:
            return False

        # owner and recipient should sign the document
        return document_status in (
            DocumentStatus.signed_and_sent,
            DocumentStatus.approved,
        )

    assert_never(first_sign_by)


def is_pdf_document(document: DBRow) -> bool:
    return unarchive_file_name(document.extension) == '.pdf'


def is_txt_document(document: DBRow) -> bool:
    return unarchive_file_name(document.extension) == '.txt'


def is_xml_document(document: DBRow) -> bool:
    return unarchive_file_name(document.extension) == '.xml'


async def send_document_status_callback_job(document_id: str, uploaded_by_edrpou: str) -> None:
    """
    Schedule a job that will notify our API clients that document status has been changed.
    """
    data = {
        'document_id': document_id,
        'uploaded_by_edrpou': uploaded_by_edrpou,
    }
    await services.kafka.send_record(topics.SEND_DOCUMENT_STATUS_CALLBACK, data)


async def send_documents_status_callback_jobs(data: list[DataDict]) -> None:
    if not data:
        return

    values = []
    for item in data:
        values.append(
            {
                'document_id': item['document_id'],
                'uploaded_by_edrpou': item['uploaded_by_edrpou'],
            }
        )
    await services.kafka.send_records(topics.SEND_DOCUMENT_STATUS_CALLBACK, values=values)


async def send_document_status_to_edi(document: DBRow) -> None:
    document_status = DocumentStatus(document.status_id)
    if document_status not in EDI_CHANGES_STATUSES:
        return

    client = edi.Client()
    await client.request(
        EDIPayloadHandleChangeStatusDocument(
            document_id=document.id,
            vendor_id=document.vendor_id,
            status=document_status,
        )
    )


async def delete_document_from_edi(document_id: str) -> None:
    client = edi.Client()
    try:
        await client.request(EDIPayloadDeleteDocumentRequestByEdo(vchasno_id=document_id))
    except Exception:
        logger.error(
            'Delete document from edi failed',
            extra={'document_id': document_id},
            exc_info=True,
        )


async def send_document_status_callback(
    document_id: str,
    uploaded_by_edrpou: str,
) -> ClientResponse | None:
    """Send request to callback API of our clients (ex: Delivery, Lifecell)"""
    log_extra = {
        'company_edrpou': uploaded_by_edrpou,
        'document_id': document_id,
    }
    logger.info('Sending document status', extra=log_extra)

    # Select updated document data
    async with services.db.acquire() as conn:
        document = await select_document_by_id(conn, document_id)
        if not document:
            logger.warning(
                'Send document status failed, document was not found',
                extra=log_extra,
            )
            return None

        company_config = await get_company_config(conn, company_edrpou=uploaded_by_edrpou)

    if document.source and document.source.is_from_edi:
        await send_document_status_to_edi(document)
        logger.info('Sent document status to edi', extra=log_extra)
        return None

    if services.config.app.debug:
        logger.error('Send document status failed. Debug mode', extra=log_extra)
        return None

    # Check company config settings
    if (
        not company_config.allow_send_document_status
        and not company_config.allow_send_document_finish_status
    ):
        logger.info('Send document status skipped. No config', extra=log_extra)
        return None

    # Lifecell needs only finished documents notifications
    if (
        company_config.allow_send_document_finish_status
        and document.status_id != DocumentStatus.finished.value
    ):
        logger.info(
            'Send document status skipped, document is not finished',
            extra={
                'document_status_id': document.status_id,
                **log_extra,
            },
        )
        return None

    api_config = company_config.api
    if not api_config:
        logger.error('Send document status failed, API config is missing', extra=log_extra)
        return None

    # Send document status
    data = {
        'id': document_id,
        'vendor_id': document.vendor_id,
        'status': document.status_id,
    }
    request_timeout = api_config['request_timeout']
    url = api_config['send_document_status_url']
    config_auth = api_config.get('basic_auth')
    auth = BasicAuth(**config_auth) if config_auth else None

    try:
        async with asyncio.timeout(request_timeout):
            response = await services.http_client.post(
                url,
                data=to_json(data),
                auth=auth,
                headers={
                    'Content-Type': 'application/json',
                    **_send_document_status_prepare_extra_headers(uploaded_by_edrpou),
                },
            )
            return response
    except TimeoutError:
        logger.info('Vendor API failed with timeout', extra=dict(log_extra, url=url))
        return None


def _send_document_status_prepare_extra_headers(edrpou: str) -> DataDict:
    # Lifecell need an extra header to request
    if edrpou == '22859846':
        return {
            'Accept': 'application/json',
        }
    return {}


async def send_sms_to_receiver(
    kafka: KafkaClient,
    company_edrpou: str,
    recipient: RecipientsEmailsOptions | None,
) -> None:
    if not recipient:
        return
    data = {
        'recipient_emails': recipient.emails,
        'sender_edrpou': company_edrpou,
    }
    await kafka.send_record(topics.SEND_SMS_TO_DOCUMENT_RECEIVER, data)


async def schedule_jobs_about_finished_document(document: DocumentWithUploader) -> None:
    """
    Schedule async jobs that should be executed after the document becomes finished.
    """
    await send_document_finished_to_edi_job(document=document)

    notification = DocumentFinishedNotification(document=document)
    await notification.send()


# TODO: Remove this after EDI-1399 done
async def send_document_finished_to_edi_job(document: Document) -> None:
    """Schedule job that sends request to EDI about finished documents"""

    if document.type not in EDI_FINISH_STATUS_DOC_TYPES:
        return

    payload: EDIPayloadBase
    if document.type in (EDI_DELNOT_DOC_TYPE, EDI_COMDOC006_DOC_TYPE):
        payload = EDIPayloadFinishDealByInvoice(
            invoice_id=document.id,
        )
    elif document.type == EDI_RETCOMDOC_DOC_TYPE:
        payload = EDIPayloadFinishDealByRetcomdoc(
            retcomdoc_id=document.id,
        )
    elif document.type == EDI_COMDOC_DOC_TYPE:
        payload = EDIPayloadFinishDealByComdoc(
            comdoc_id=document.id,
        )
    else:
        logger.error(
            'Send document finished to EDI failed, unsupported document type',
            extra={'document_id': document.id, 'document_type': document.type},
        )
        return

    await services.kafka.send_record(
        topic=topics.EDI_SEND_EDI_REQUEST,
        value=payload.to_dict(),
    )


def _is_valid_recipient_email_domain(
    email: str,
    company: Company | None,
) -> bool:
    """Check if recipient email has valid company domain for recipient company"""

    email_domains: list[str] | None
    email_domains = company.email_domains if company else None

    _is_valid_email = is_valid_email_domains(
        email=email,
        domains=email_domains,
    )
    return _is_valid_email


async def share_inbox_document_notification_for_new_user(
    conn: DBConnection,
    *,
    document_id: str,
    recipient_email: str,
    recipient_edrpou: str,
    recipient_company: Company | None,
    sender: User,
    sender_config: CompanyConfig,
    recipient_config: CompanyConfig,
) -> None:
    """
    Send notification about inbox document to not registered user, with invitation
    to register and sign document.
    """

    email_domains: list[str] | None
    email_domains = recipient_company.email_domains if recipient_company else None

    _is_valid_email = _is_valid_recipient_email_domain(
        email=recipient_email,
        company=recipient_company,
    )

    # We are sending invite email to new user, only if email domain
    # is  valid for recipient company.
    if _is_valid_email:
        await emailing.send_document_user_invite_email(
            conn=conn,
            document_id=document_id,
            recipient_email=recipient_email,
            recipient_edrpou=recipient_edrpou,
            email_domains=email_domains,
            sender=sender,
            sender_config=sender_config,
            recipient_config=recipient_config,
        )
    else:
        logger.warning(
            msg='Share document to user with invalid email domain',
            extra={'domain': email_domains, 'email': recipient_email},
        )

    # Send notification to admins because the invited user may not
    # be a member of this company
    await notify_users_about_incoming_documents(
        edrpou=recipient_edrpou,
        document_id=document_id,
        sender_role_id=sender.role_id,
    )


async def share_inbox_document_notification_for_pending_role(
    conn: DBConnection,
    *,
    document_id: str,
    recipient_email: str,
    recipient_edrpou: str,
    recipient: User | None,
    recipient_company: Company | None,
    sender: User,
    sender_config: CompanyConfig,
    recipient_config: CompanyConfig,
) -> None:
    """
    Send inbox email about new document to pending role, with invitation to add role
    to user profile and sign the document.
    """

    document = await select_document_for_recipient_email(conn, document_id)
    if not document:
        logger.warning('No document for email')
        return

    # Check if recipient email is OK for recipient company
    if not _is_valid_recipient_email_domain(
        email=recipient_email,
        company=recipient_company,
    ):
        await notify_users_about_incoming_documents(
            edrpou=recipient_edrpou,
            document_id=document_id,
            sender_role_id=sender.role_id,
        )
        return

    # If recipient exists, but doesn't have pending role,
    # then just send notification to company admins
    if recipient and not recipient.is_pending:
        await notify_users_about_incoming_documents(
            edrpou=recipient_edrpou,
            document_id=document_id,
            sender_role_id=sender.role_id,
        )
        return

    # if there is no role associated with given email
    # - notify company admins
    # - notify user about document
    if not recipient:
        await notify_users_about_incoming_documents(
            edrpou=recipient_edrpou,
            document_id=document_id,
            sender_role_id=sender.role_id,
        )

    notification = DocumentInboxInviteRoleNotification(
        conn=conn,
        document=document,
        recipient=recipient,
        recipient_email=recipient_email,
        recipient_edrpou=recipient_edrpou,
        recipient_company=recipient_company,
        sender=sender,
        recipient_config=recipient_config,
        sender_config=sender_config,
    )
    await notification.send()


async def share_inbox_document_notification_for_active_user(
    conn: DBConnection,
    document_id: str,
    recipient: User,
    sender: User,
    sender_config: CompanyConfig,
    recipient_config: CompanyConfig,
) -> None:
    """
    Send inbox document notification for active user with invitation to sign document
    """
    from app.flow import utils as flow_utils

    document = await select_document_for_recipient_email(conn, document_id)
    if not document:
        logger.warning('Document not found for sending email')
        return

    count_roles = await count_user_roles(conn, user_id=recipient.id)

    flows_state = await flow_utils.get_flows_state(conn, document_id=document_id)

    notification = DocumentInboxNotification(
        conn=conn,
        document=document,
        recipient=recipient,
        recipient_has_multiple_roles=count_roles > 1,
        sender=sender,
        sender_config=sender_config,
        recipient_config=recipient_config,
        flows_state=flows_state,
    )
    await notification.send()


async def share_document(
    conn: DBConnection,
    recipient_edrpou: str,
    recipient_email: str,
    document_id: str,
    sender: User,
) -> None:
    """
    Notify existing role or invite recipient to add (activate) new role.

    - "Pending" notification
    - Inbox email for unregistered user, with invitation to register and sign document
    - Inbox email for active role, with invitation to sign document
    - Inbox email for pending role, with invitation to add company to profile and sign document
    """

    if not is_valid_email(recipient_email):
        # We have quite a few cases when we have invalid email is set as recipient email
        logger.warning(
            msg='Share document to invalid email',
            extra={'email': recipient_email},
        )
        return

    # Company config for current user
    sender_config = await auth.get_company_config(conn, company_id=sender.company_id)

    # Select basic information about recipient
    recipient_base = await auth.get_base_user(conn, email=recipient_email)
    recipient_company = await auth.get_company(conn, edrpou=recipient_edrpou)
    recipient_config = await auth.get_company_config(
        conn=conn,
        company_edrpou=recipient_edrpou,
    )

    # Send inbox notification for unregistered user
    if not recipient_base:
        await share_inbox_document_notification_for_new_user(
            conn=conn,
            document_id=document_id,
            recipient_email=recipient_email,
            recipient_edrpou=recipient_edrpou,
            recipient_company=recipient_company,
            sender=sender,
            sender_config=sender_config,
            recipient_config=recipient_config,
        )
        return

    # Select recipient role
    recipient = await auth.get_user(
        conn=conn,
        company_edrpou=recipient_edrpou,
        email=recipient_email,
        is_legal=True,
        use_active_filter=False,
    )

    # Send inbox notification for active role
    # NOTE: it's the most common use case
    if recipient and recipient.is_active:
        await share_inbox_document_notification_for_active_user(
            conn=conn,
            document_id=document_id,
            recipient=recipient,
            sender=sender,
            sender_config=sender_config,
            recipient_config=recipient_config,
        )
        return

    # Otherwise send inbox email for pending role with invitation to
    # add that company to profile
    await share_inbox_document_notification_for_pending_role(
        conn=conn,
        document_id=document_id,
        recipient=recipient,
        recipient_company=recipient_company,
        recipient_edrpou=recipient_edrpou,
        recipient_email=recipient_email,
        sender=sender,
        recipient_config=recipient_config,
        sender_config=sender_config,
    )


async def notify_users_about_incoming_documents(
    edrpou: str,
    document_id: str,
    sender_role_id: str,
) -> None:
    """
    Notify admins and users with can_view_document about incoming documents
    when the user doesn't registered.
    """
    await services.kafka.send_record(
        topics.INITIATE_FALLBACK_INBOX_DOCUMENT_NOTIFICATIONS,
        value={
            'recipient_edrpou': edrpou,
            'document_id': document_id,
            'sender_role_id': sender_role_id,
        },
    )


async def remove_listings_source(
    conn: DBConnection, listings_ids: list[str], source: AccessSource
) -> DeleteListingsCtx:
    """
    IMPORTANT! When calling this function, you need manually process its result:
      1) send document to indexation
      2) remove documents from ES_Index by DeleteListingsCtx.company_listing_ids, like this
         await schedule_remove_company_listings_es(DeleteListingsCtx.company_listing_ids)
    """
    if not listings_ids:
        return DeleteListingsCtx()

    async with conn.begin():
        await delete_listing_source(conn, listings_ids, source=source)
        removed_listings_ctx = await delete_empty_listing(conn, listings_ids=listings_ids)

    return removed_listings_ctx


def get_documents_access_filters(user: AuthUser | User) -> BinaryExpression:
    """
    Filter to find documents that user have access to

    WARNING: this filter requires "listing_table" to be present in FROM clause of the main query
    """

    filters = [listing_table.c.access_edrpou == user.company_edrpou]

    # In cases with sign sessions, user can have special scope limited to given sets of
    # documents. Usually in sign session user has can_view_documents access, so most of the
    # time it will be combined with "can_view_document" filter
    if isinstance(user, AuthUser) and user.documents_ids is not None:
        filters.append(listing_table.c.document_id.in_(user.documents_ids))

    # Such as user can view all documents in the company, we don't need any additional filters
    # outside just checking that company has access to the document
    if can_user_view_all_company_documents(user):
        return sa.and_(*filters)

    role_filter = listing_table.c.role_id == user.role_id
    if user.can_view_private_document:
        private_filter = build_private_document_access_filter(user)
        filters.append(sa.or_(role_filter, private_filter))
    elif user.can_view_document:
        extended_filter = ~build_private_document_access_filter(user)
        filters.append(sa.or_(role_filter, extended_filter))
    else:
        # both permissions are False, then user can only view documents that they have access to
        filters.append(role_filter)

    return sa.and_(*filters)


def convert_gen_arch_params_to_text_dict(data: dict[Any, Any]) -> dict[str, str]:
    def attr_by_value(_enum: type[Enum], val: str) -> Any | None:
        for item in enum_items(_enum):
            if item.value == int(val):
                return item
        return None

    if 'date_from' in data:
        data['date_from'] = datetime.strptime(data['date_from'], '%Y-%m-%d').strftime('%d.%m.%Y')
    if 'date_to' in data:
        data['date_to'] = datetime.strptime(data['date_to'], '%Y-%m-%d').strftime('%d.%m.%Y')
    if 'folder_id' in data:
        attr = attr_by_value(DocumentFolder, data['folder_id'])
        if attr:
            data['folder_title'] = DOC_FOLDER_TO_TITLE_MAP[attr]
    if 'status_ids' in data:
        attr = [attr_by_value(DocumentStatus, item) for item in data['status_ids']]
        if attr:
            data['status_title'] = [STATUS_TO_TITLE_MAP[item] for item in attr]
    return data


async def create_delete_doc_requests(
    conn: DBConnection,
    user: User,
    documents: list[DBRow],
    message: str,
) -> tuple[list[DBRow], list[Comment]]:
    from app.comments import utils as comments

    assert documents
    delete_requests: list[DataDict] = []
    created_comments = []

    document_ids = [doc.id for doc in documents]

    involved_companies = await select_all(
        conn,
        (
            sa.select(
                [
                    company_listing_table.c.edrpou.label('edrpou'),
                    company_listing_table.c.document_id,
                ]
            )
            .select_from(company_listing_table)
            .where(
                sa.and_(
                    company_listing_table.c.edrpou != user.company_edrpou,
                    company_listing_table.c.document_id.in_(document_ids),
                )
            )
        ),
    )

    # This can be if there are no non-deleted users in target companies
    if not involved_companies:
        raise InvalidRequest(
            reason=_('Документи, до яких створюється запит на видалення, повинні мати отримувачів')
        )

    doc_receivers: defaultdict[str, set[str]] = defaultdict(set)
    for company in involved_companies:
        doc_receivers[company.document_id].add(company.edrpou)

    for doc in documents:
        # Create one del request per non-initiator company
        delete_requests += [
            {
                'initiator_role_id': user.role_id,
                'initiator_edrpou': user.company_edrpou,
                'document_id': doc.id,
                'receiver_edrpou': edrpou,
                'message': message,
            }
            for edrpou in doc_receivers[doc.id]
        ]

    async with conn.begin():
        delete_requests_rows = await insert_delete_requests(conn, delete_requests)
        if message:
            created_comments = await comments.create_comments_for_documents(
                conn=conn,
                documents_ids=document_ids,
                role_id=user.role_id,
                type_=CommentType.delete_request,
                text=message,
                access_company_id=None,
            )

    return delete_requests_rows, created_comments


async def accept_delete_requests(conn: DBConnection, document_ids: list[str]) -> None:
    """
    Accept delete request (update status and date_accepted)
    """

    if not document_ids:
        return

    data = {
        'status': DeleteRequestStatus.accepted,
        'date_accepted': sa.text('now()'),
    }

    await conn.execute(
        (
            delete_request_table.update()
            .values(data)
            .where(
                sa.and_(
                    delete_request_table.c.document_id.in_(document_ids),
                    delete_request_table.c.status.in_(
                        [DeleteRequestStatus.new, DeleteRequestStatus.rejected]
                    ),
                )
            )
        ),
    )


async def reject_delete_requests(
    conn: DBConnection,
    rejecter: User,
    document_ids: set[str],
    reject_message: str | None = None,
) -> list[DBRow]:
    from app.comments import utils as comments

    async with conn.begin():
        data = {
            'status': DeleteRequestStatus.rejected,
            'date_rejected': sa.text('now()'),
            'reject_message': reject_message,
        }

        updated_delete_requests = await select_all(
            conn,
            (
                delete_request_table.update()
                .values(data)
                .where(
                    sa.and_(
                        delete_request_table.c.document_id.in_(document_ids),
                        delete_request_table.c.status == DeleteRequestStatus.new,
                    )
                )
                .returning(delete_request_table)
            ),
        )
        if reject_message:
            await comments.create_comments_for_documents(
                conn=conn,
                documents_ids=list(document_ids),
                role_id=rejecter.role_id,
                type_=CommentType.delete_request_rejection,
                text=reject_message,
                access_company_id=None,
            )

    return updated_delete_requests


async def send_notification_about_document_access(
    user: User | None,
    document_id: str,
    document_title: str,
    roles_ids: list[str],
    source: Source | None,
    comment: str | None = None,
) -> None:
    if not user:
        logger.warning(
            'Send notification about document access skipped, user is None',
            extra={'document_id': document_id, 'document_title': document_title},
        )
        return

    await services.kafka.send_records(
        topics.SEND_NOTIFICATION_ABOUT_DOCUMENT_ACCESS,
        values=[
            {
                'document_id': document_id,
                'document_title': document_title,
                'coworker_email': user.email,
                'coworker_role_id': user.role_id,
                'role_id': role_id,
                'comment': comment,
                'source': source,
            }
            for role_id in roles_ids
        ],
    )


async def find_recipient_emails(
    conn: DBConnection,
    recipients_edrpous: list[str],
    user: User,
) -> RecipientEmailsAggregator:
    """
    Try to find emails for given edrpous from a database to automatically fill recipient emails
    field. Any emails that are not found in contacts will be hidden
    """
    aggregator = RecipientEmailsAggregator()

    # For the current company, we suggest email of the current user to simplify
    if user.company_edrpou in recipients_edrpous:
        aggregator.add(
            edrpou=user.company_edrpou,
            email=user.email,
            is_hidden=False,
        )

    # Try to find emails in contacts of the current company. It's the safest option, because
    # usually we can return email that was already used for sending documents or was added to
    # the contact list manually.
    missing_edrpous = list(set(recipients_edrpous) - aggregator.edrpous)
    contacts = await select_contact_email_by_edrpou(
        conn=conn,
        edrpous=missing_edrpous,
        company_id=user.company_id,
    )
    for contact in contacts:
        aggregator.add(
            edrpou=contact.edrpou,
            email=contact.email,
            # When a user sends a document to hidden email (suggested by this function),
            # we save this email in contacts as hidden. That why contact emails might
            # be hidden as well.
            is_hidden=contact.is_email_hidden,
        )

    # ==> From this point we are looking for emails that should always be hidden
    # to prevent leaking recipient emails

    # Try to find emails in default recipients. On the recipient side, company might set up
    # the default recipient that should receive all documents when the sender doesn't know email.
    # In large companies, it helps to avoid sending documents to the wrong person.
    missing_edrpous = list(set(missing_edrpous) - aggregator.edrpous)
    default_recipients = await select_default_recipients(conn, missing_edrpous)
    for default_recipient in default_recipients:
        aggregator.add(
            edrpou=default_recipient.edrpou,
            email=default_recipient.email,
            is_hidden=True,
        )

    # Try to find emails in company users. As the last resort, we can select some person from
    # the company and send the document to them. The document might be sent to the wrong person,
    # but at least it will be sent to the company
    missing_edrpous = list(set(missing_edrpous) - aggregator.edrpous)
    company_users = await select_recipient_email_from_company(conn=conn, edrpous=missing_edrpous)
    for company_user in company_users:
        # In the "users" table for some reason, we have invalid emails
        if not is_valid_email(company_user.email):
            continue

        aggregator.add(
            edrpou=company_user.edrpou,
            email=company_user.email,
            is_hidden=True,
        )

    return aggregator


def prepare_filled_emails_response(
    recipients: list[DataDict],
) -> list[DataDict]:
    """
    Generate dictionary with edrpou and filled emails if they are not hidden.
    """
    return [
        {
            'edrpou': recipient['edrpou'],
            'is_hidden': recipient['is_emails_hidden'],
            'emails': (None if recipient['is_emails_hidden'] else recipient['emails']),
        }
        for recipient in recipients
    ]


def get_hidden_email_key(
    *,
    role_id: str,
    recipient_edrpou: str,
) -> str:
    return f'hidden-emails:{role_id}-{recipient_edrpou}'


async def save_hidden_emails(
    redis: Redis,
    user: User,
    recipients: list[RecipientAggregatedData],
) -> None:
    """
    Save to Redis hidden emails on several hours for retrieving on
    change recipient action.
    """
    async with redis.pipeline(transaction=True) as pipe:
        for recipient in recipients:
            if not recipient.is_hidden or not recipient.emails:
                continue

            key = get_hidden_email_key(
                role_id=user.role_id,
                recipient_edrpou=recipient.edrpou,
            )
            pipe = pipe.delete(key).sadd(key, *recipient.emails).expire(key, TTL_HIDDEN_EMAIL)

        await pipe.execute()


async def get_required_recipient(
    conn: DBConnection,
    *,
    document_id: str,
    document_owner_edrpou: str,
    edrpou_recipient: str | None,
    emails_recipient: list[str] | None,
) -> RecipientsEmailsOptions | None:
    """
    Instantiate RecipientOption from parameters edrpou_recipient
    and email_recipient. If some parameter is empty, try to select recipient
    info from database.
    """

    edrpou: str | None = edrpou_recipient
    emails: list[str] | None = emails_recipient
    assert not isinstance(emails_recipient, str), 'Split email before'
    is_emails_hidden = False
    if not edrpou_recipient or not emails_recipient:
        recipient = await select_bilateral_document_recipient(
            conn=conn,
            document_id=document_id,
            document_owner_edrpou=document_owner_edrpou,
        )

        if recipient:
            edrpou = recipient.edrpou
            emails = recipient.emails or []
            is_emails_hidden = recipient.is_emails_hidden

    if edrpou and emails:
        return RecipientsEmailsOptions(
            edrpou=edrpou,
            emails=emails,
            is_emails_hidden=is_emails_hidden,
        )
    return None


async def replace_bilateral_recipient_email(
    conn: DBConnection,
    *,
    document_id: str,
    edrpou: str,
    new_emails: list[str],
    is_emails_hidden: bool = False,
) -> None:
    """
    Replace recipient emails for bilateral document. Can be useful
    for changing hidden recipient emails to email who first sign document.
    """
    await insert_recipients(
        conn=conn,
        recipient=UpsertDocumentRecipientDict(
            document_id=document_id,
            edrpou=edrpou,
            emails=new_emails,
            is_emails_hidden=is_emails_hidden,
        ),
    )

    await db.update_legacy_document_recipient_email(
        conn=conn,
        document_id=document_id,
        emails=new_emails,
    )


async def connect_document_with_tags_by_contact(
    edrpou: str,
    contact_edrpou: str,
    documents_ids: list[str],
) -> None:
    await services.kafka.send_record(
        topics.CREATE_DOCUMENTS_TAGS_BY_CONTACT,
        value={
            'company_edrpou': edrpou,
            'contact_edrpou': contact_edrpou,
            'documents_ids': documents_ids,
        },
    )


async def can_delete_documents(
    conn: DBConnection,
    *,
    documents_ids: list[str],
    current_user: User | AuthUser,
) -> dict[str, bool]:
    """
    Check ability to delete documents without approval with recipient.
    """
    from app.archive.utils import is_documents_archived

    if current_user.role_id is None or current_user.company_id is None:
        return {doc_id: False for doc_id in documents_ids}

    documents_rows = await select_documents_by_ids(conn, ids=documents_ids)
    documents_map = {row.id: Document.from_row(row) for row in documents_rows}

    documents_recipients = await select_document_recipients(
        conn=conn,
        documents_ids=documents_ids,
    )
    documents_recipients_map = defaultdict(list)
    for recipient in documents_recipients:
        documents_recipients_map[recipient.document_id].append(recipient)

    signatures = await select_signatures(conn, document_ids=documents_ids)
    signatures_map = defaultdict(list)
    for signature in signatures:
        signatures_map[signature.document_id].append(signature)

    delete_document_settings = await select_delete_document_settings(
        conn=conn,
        document_ids=documents_ids,
    )
    delete_document_settings_map = {
        setting.document_id: setting for setting in delete_document_settings
    }
    is_archived_map = await is_documents_archived(
        conn=conn,
        document_ids=documents_ids,
        company_id=current_user.company_id,
    )

    is_pending_delete_requests = await get_documents_pending_delete_requests_mapping(
        conn=conn, documents_ids=documents_ids
    )

    registered_recipients_edrpous = await select_registered_companies_edrpous(
        conn=conn,
        companies_edrpous=list({r.edrpou for r in documents_recipients}),
    )

    # Build dict with document_id and value if document can be deleted
    result: dict[str, bool] = {}
    for document_id in documents_ids:
        document: Document | None = documents_map.get(document_id)
        if not document:
            result[document_id] = False
            continue

        signatures = signatures_map.get(document_id, [])
        is_signed_by_user = any(s.role_id == current_user.role_id for s in signatures)
        is_signed_by_recipient = any(s.owner_edrpou != document.edrpou_owner for s in signatures)

        status = can_delete_document(
            current_user=current_user,
            document=document,
            is_signed_by_user=is_signed_by_user,
            is_signed_by_recipient=is_signed_by_recipient,
            document_recipients=documents_recipients_map[document_id],
            registered_recipients_edrpous=registered_recipients_edrpous,
            delete_document_setting=delete_document_settings_map.get(document.id),
            has_unprocessed_delete_request=bool(is_pending_delete_requests.get(document_id)),
            is_archived=is_archived_map[document.id],
        )
        result[document_id] = status.is_ok

    return result


def can_delete_document(
    *,
    current_user: User | AuthUser,
    document: Document,
    is_signed_by_user: bool,
    is_signed_by_recipient: bool,
    document_recipients: list[DocumentRecipient],
    registered_recipients_edrpous: set[str],
    delete_document_setting: DeleteDocumentSettings | None,
    has_unprocessed_delete_request: bool,
    is_archived: bool,
) -> GenericResult[None, Error]:
    """
    Note: Before call this function you should validate user access to document

    Check the ability to delete the document without any reconciliation with coworker or recipients

    Details: https://tabula-rasa.atlassian.net/wiki/spaces/vchasno/pages/27328529/QA+checklist+3.0

    NOTE: Keep in sync with frontend implementation "getIsUserCanDirectlyDeleteDocument"
    """

    # Check company permission to delete the document
    if document.edrpou_owner != current_user.company_edrpou:
        return GenericResult(
            error=AccessDenied(
                reason=_(
                    'Неможливо видалити документ, оскільки ваша компанія не є власником '
                    'цього документу'
                )
            ),
        )

    # Check user permission to delete the document
    if not has_permission(
        current_user,
        {'can_delete_document', 'can_delete_document_extended'},
        all_permissions=False,
    ):
        return GenericResult(
            error=AccessDenied(reason=_('Немає прав на видалення документу')),
        )

    # For archives, we have special permission for deleting documents from archive
    if is_archived and not has_permission(
        current_user,
        {'can_delete_archived_documents', 'can_delete_document_extended'},
        all_permissions=False,
    ):
        return GenericResult(
            error=AccessDenied(reason=_('Неможливо видалити архівований документ')),
        )

    # Check if the document is locked for deletion directly and should be deleted
    # only by delete request process
    if delete_document_setting and delete_document_setting.is_delete_request_required:
        return GenericResult(
            error=AccessDenied(reason=_('Документ можна видалити тільки через запит на видалення')),
        )

    if has_unprocessed_delete_request:
        return GenericResult(
            error=AccessDenied(reason=_('Документ має неопрацьований запит на видалення')),
        )

    if document.is_internal:
        return _can_delete_internal_document(
            user=current_user,
            document=document,
            is_signed_by_user=is_signed_by_user,
        )

    return _can_delete_external_document(
        document=document,
        is_signed_by_recipient=is_signed_by_recipient,
        document_recipients=document_recipients,
        registered_recipients_edrpous=registered_recipients_edrpous,
    )


def _can_delete_external_document(
    document: Document,
    is_signed_by_recipient: bool,
    document_recipients: list[DocumentRecipient],
    registered_recipients_edrpous: set[str],
) -> GenericResult[None, Error]:
    """
    Check if a user can delete an external document without any coordination with recipient
    """

    if is_signed_by_recipient:
        return GenericResult(
            error=AccessDenied(
                reason=_('Неможливо видалити документ, оскільки він підписаний одним з отримувачів')
            ),
        )

    if document.status in (
        # not sent to recipient yet
        DocumentStatus.uploaded,
        DocumentStatus.ready_to_be_signed,
        # no legal obligations from rejected documents, so it's OK to delete them directly
        # without any coordination with recipient
        DocumentStatus.reject,
    ):
        return GenericResult(result=None)

    other_recipients = [r for r in document_recipients if r.edrpou != document.edrpou_owner]
    recipients_sent = [r for r in other_recipients if r.is_sent]

    # If there are no recipients to send the delete request to, then we need to offer
    # the user to delete the document directly without a delete request process
    if not any(r.edrpou in registered_recipients_edrpous for r in recipients_sent):
        return GenericResult(result=None)

    if document.status == DocumentStatus.finished or document.status == DocumentStatus.revoked:
        return GenericResult(
            error=AccessDenied(reason=_('Неможливо видалити завершений документ')),
        )

    return GenericResult(result=None)


def _can_delete_internal_document(
    user: User | AuthUser,
    document: Document,
    is_signed_by_user: bool,
) -> GenericResult[None, Error]:
    """
    Check if a user can delete an internal document without any coordination with coworker
    """
    if document.status in (
        DocumentStatus.uploaded,
        DocumentStatus.ready_to_be_signed,
        DocumentStatus.signed,
    ):
        return GenericResult(result=None)

    is_uploaded_by_user = document.uploaded_by == user.role_id

    if document.status == DocumentStatus.finished and (
        has_permission(user, {'can_delete_document_extended'})
        or is_uploaded_by_user
        or is_signed_by_user
    ):
        return GenericResult(result=None)

    if document.status == DocumentStatus.reject and (
        has_permission(user, {'can_delete_document_extended'}) or is_uploaded_by_user
    ):
        return GenericResult(result=None)

    return GenericResult(
        error=AccessDenied(reason=_('Неможливо видалити документ')),
    )


async def update_document_status(
    conn: DBConnection,
    document_id: str,
    status: DocumentStatus,
) -> None:
    await update_document(conn=conn, data={'document_id': document_id, 'status_id': status.value})


async def charge_document_on_document_sending(
    conn: DBConnection,
    user: User | None,
    options: SendDocumentOptions,
) -> None:
    # Currently we do not charge from anon users
    if not user:
        return  # TODO: add charging from anon

    charge_context = options.charge_context
    if charge_context is None:
        return

    await charge_document(conn, context=charge_context)
    await increment_company_upload_documents_left(
        conn=conn,
        company_id=user.company_id,
        payer_id=charge_context.payer_id,
        uploader_company_id=options.uploader_company_id,
    )


async def send_notifications_on_document_sending(
    options: SendDocumentOptions,
    user: User | None,
) -> None:
    """Send email/telegram notification about new documents"""

    if options.is_internal_bill_sending:
        return

    document = options.document
    # send back to owner 3p document after recipient sent
    if options.is_3p and not options.is_owner_sending and not options.is_one_sign:
        from app.signatures.emailing import send_notification_to_owner

        await send_notification_to_owner(
            kafka=services.kafka,
            company_edrpou=document.edrpou_owner,
            document_id=document.id,
            sender_role_id=user.role_id if user else None,
            sender_edrpou=options.sender_edrpou,
        )
        return

    # Only owner can share document to recipient
    if not options.is_owner_sending:
        return

    # cast because when is_sending_to_recipient is True then recipient is required
    recipient = cast(RecipientsEmailsOptions, options.recipient)
    # In other case owner send document to recipient about new document
    for email in recipient.emails:
        await services.kafka.send_record(
            topic=topics.SEND_DOCUMENT_TO_RECIPIENT,
            value={
                'recipient_edrpou': recipient.edrpou,
                'recipient_email': email,
                'document_id': document.id,
                'current_role_id': document.uploaded_by,
            },
        )


async def update_contacts_on_document_sending(
    conn: DBConnection,
    options: SendDocumentOptions,
    user: User | None,
) -> None:
    """Save all recipients in contacts"""

    if not (options.is_3p and options.is_owner_sending and user):
        # ignore any other cases, because for non 3p cases edrpou and email was
        # saved to contacts on change recipient step.
        return

    # cast because when is_sending_to_recipient is True then recipient is required
    recipient = cast(RecipientsEmailsOptions, options.recipient)

    await update_main_contacts_on_document_update(
        conn=conn,
        company_id=user.company_id,
        recipient_edrpou=recipient.edrpou,
        recipient_emails=recipient.emails,
        is_emails_hidden=False,
    )


async def update_main_contacts_on_document_update(
    conn: DBConnection,
    company_id: str,
    recipient_edrpou: str,
    recipient_emails: list[str],
    is_emails_hidden: bool,
) -> None:
    """
    Update main contact emails on document update
    """
    if not recipient_emails:
        return

    # Update only first email as main and ignore others, because don't know
    # how to handle multiple emails for one recipient
    recipient_email, *ignored_emails = recipient_emails

    await update_main_recipient(
        conn=conn,
        company_id=company_id,
        details=ContactDetails(
            edrpou=recipient_edrpou,
            email=recipient_email,
            is_email_hidden=is_emails_hidden,
        ),
    )

    if ignored_emails:
        logger.info(
            msg='Some emails was ignored on update main contact',
            extra={
                'recipient_edrpou': recipient_edrpou,
                'ignored_emails': ignored_emails,
                'company_id': company_id,
                'recipient_email': recipient_email,
            },
        )


async def send_bilateral_document_to_company(
    conn: DBConnection,
    options: SendDocumentOptions,
    user: User | None,
    request_source: Source,
) -> None:
    """
    Actual sending of a document from owner to recipient or from recipient to owner
    """
    from app.comments.utils import schedule_remove_comments_from_index

    document = options.document

    # cast because when is_sending_to_recipient is True then recipient is required
    recipient = cast(RecipientsEmailsOptions, options.recipient)

    previous_recipients = await get_document_recipients(conn, document_id=document.id)

    if options.is_owner_sending:
        # In case when owner send document to recipient we need charge document,
        # save invites, update contacts, open access for recipient, move document
        # to next status and send notification about new input documents and
        # open combine recipient contact tags with documents

        async with conn.begin():
            await update_contacts_on_document_sending(conn, options, user)
            await charge_document_on_document_sending(conn, user, options)

            # TODO: consider to remove change recipient step from "send document" logic
            #  to simplify the process of sending documents. Use "change recipient" logic
            #  before sending document to recipient.
            await update_bilateral_recipient(
                conn=conn,
                document_id=document.id,
                document_owner_edrpou=options.company_edrpou,
                new_recipient_edrpou=recipient.edrpou,
                new_recipient_emails=recipient.emails,
                new_recipient_emails_hidden=recipient.is_emails_hidden,
                prev_recipients=previous_recipients,
            )
            # delete all resources created by previous companies, such as
            # tags, access, comments, and so on.
            # NOTE: that we can have more than one recipient on the document
            delete_resources_edrpous = {r.edrpou for r in previous_recipients}
            delete_resources_edrpous.discard(recipient.edrpou)
            delete_resources_edrpous.discard(document.edrpou_owner)
            deleted_resources = await delete_recipients_document_resources(
                conn=conn,
                document_id=document.id,
                companies_edrpous=list(delete_resources_edrpous),
            )
            # Always open access for recipient on document sending
            await send_bilateral_document_to_recipient(
                conn=conn,
                document_id=document.id,
                recipient_edrpou=recipient.edrpou,
                recipient_emails=recipient.emails,
            )

            await update_document_status(
                conn=conn,
                document_id=document.id,
                status=options.next_status,
            )

        # Run this functions outside of transaction to avoid possible rollback
        await send_sms_to_receiver(
            kafka=services.kafka,
            company_edrpou=options.company_edrpou,
            recipient=recipient,
        )

        # Connect recipient contact tags with sent document
        await connect_document_with_tags_by_contact(
            edrpou=recipient.edrpou,
            contact_edrpou=options.company_edrpou,
            documents_ids=[document.id],
        )

        # Find and apply template for document in recipient company
        await start_document_automation(
            document_id=document.id,
            company_edrpou=recipient.edrpou,
            source=request_source,
        )

        await schedule_remove_comments_from_index(comment_ids=deleted_resources.comments_ids)

    else:
        # For sending document from recipient to owner we need only two actions:
        # move to the next status and send notification back for owner in non 3p case
        async with conn.begin():
            await update_document_status(conn, document.id, status=options.next_status)
            await send_bilateral_document_to_owner(
                conn=conn,
                document_id=document.id,
                document_owner_edrpou=document.edrpou_owner,
            )

        # Connect recipient contact tags with sent document
        await connect_document_with_tags_by_contact(
            edrpou=options.document.edrpou_owner,
            contact_edrpou=recipient.edrpou,
            documents_ids=[document.id],
        )

        # Find and apply template for document in owner company
        await start_document_automation(
            document_id=document.id,
            company_edrpou=document.edrpou_owner,
            source=request_source,
        )

    await send_notifications_on_document_sending(
        options=options,
        user=user,
    )


async def delete_default_access(
    conn: DBConnection,
    *,
    document_id: str,
    company_edrpou: str,
    user_emails: list[str],
) -> DeleteListingsCtx:
    """
    Delete default ("recipient") access for document in company
    """
    if not user_emails:
        return DeleteListingsCtx()

    listings = await select_listings_by_source(
        conn=conn,
        document_id=document_id,
        company_edrpou=company_edrpou,
        user_emails=user_emails,
        source=AccessSource.default,
    )
    listings_ids = [item.id for item in listings]

    return await remove_listings_source(
        conn=conn,
        listings_ids=listings_ids,
        source=AccessSource.default,
    )


async def send_document_to_coworker(
    conn: DBConnection,
    options: SendDocumentOptions,
) -> None:
    # Just move document to next status only.
    await update_document_status(
        conn=conn,
        document_id=options.document.id,
        status=options.next_status,
    )


_default_date_delivered = object()


async def update_documents_date_delivered_job(
    role_id: str | None,
    documents_ids: list[str],
    date_delivered: datetime | object | None = _default_date_delivered,
) -> None:
    if not role_id:
        logger.warning(
            msg='Can not update documents date delivered without role_id',
            extra={'role_id': role_id},
        )
        return

    if date_delivered == _default_date_delivered:
        date_delivered = utc_now()

    await services.kafka.send_record(
        topic=topics.UPDATE_DOCS_DATE_DELIVERED,
        value={
            'role_id': role_id,
            'documents_ids': documents_ids,
            'date_delivered': date_delivered,
        },
    )


class DocumentUpdate:
    """
    Class to perform update several parts of document in one transaction.

    The main ideas of this class:
    1. gather all functions that related only to document update in one place
    2. hold the state of updated/deleted/created objects to avoid passing them through
    all functions
    """

    def __init__(
        self,
        *,
        document: Document,
    ) -> None:
        """
        NOTE: try to avoid passing input parameters to __init__ of this class, because
        this class is designed to hold the state of updated/deleted/created objects
        and avoid passing them through all functions. Also, some of the @staticmethod or
        @classmethod can be used without creating an instance of this class.

        Args:
            document: initial document object, can be updated during transaction
        """

        # Hold the original document object to have reference value for comparison
        # what was changed during the transaction
        self.original_document = document

        # Document during update, functions that updates document should update this
        # object to have the latest state of a document during transaction
        self.document = document

        # Artifact of recipient update
        self.deleted_company_listing_ids: list[str] = []
        self.comments_ids_deleted: list[str] = []
        self.should_send_bilateral_recipient_jobs: bool = False
        self.should_send_multilateral_recipient_jobs: bool = False
        self.should_schedule_finished_status_jobs: bool = False

        self.version_update: UpdateDocumentVersionSettingsOutput | None = None

        self.delete_tag_access_listing_ids: set[str] = set()
        self.open_tag_access_tags_ids: set[str] = set()

        # Artifact of viewers updates
        self.added_viewers: list[str] = []

        # TODO: add action for each section of the document update
        self.document_actions: list[document_actions.DocumentAction] = []

    async def perform_update(
        self,
        conn: DBConnection,
        options: UpdateDocumentOptions,
        source: Source,
    ) -> None:
        """
        Main entrypoint to perform document update
        """

        await self._perform_main_transaction(
            conn=conn,
            options=options,
            source=source,
        )

        await self._save_actions()

        await self._schedule_async_jobs(
            conn=conn,
            options=options,
            source=source,
        )

        await self.send_notifications(
            conn=conn,
            options=options,
            source=source,
        )

        await self._cleanup_document_version_from_s3()

    async def _perform_main_transaction(
        self,
        conn: DBConnection,
        options: UpdateDocumentOptions,
        source: Source,
    ) -> None:
        """
        Process document update, update document info, document signers, reviews,
        parameters, etc.

        WARNING: try to avoid scheduling jobs inside transaction, because you
        can lose some updates if transaction is rolled back or a task
        will be performed before transaction commits.
        """
        from app.signatures import utils as signatures

        # We can safety use functions with transaction inside, because
        # sqlalchemy handle nested transaction as one, the first seen
        user = options.user
        logger.info(
            msg='Process document update',
            extra={
                'user_edrpou': user.company_edrpou,
                'user_email': user.email,
                'document_id': self.document.id,
                'recipients_settings': options.recipients_settings,
                'document_settings': options.document_settings,
                'signers_settings': options.signers_settings,
                'reviews_settings': options.reviews_settings,
                'viewers_settings': options.viewers_settings,
                'parameters_settings': options.parameters_settings,
                'tags_settings': options.tags_settings,
                'versioned_settings': options.version_settings,
            },
        )
        async with conn.begin():
            if recipients_settings := options.recipients_settings:
                await self._update_recipients(
                    conn=conn,
                    document=self.document,
                    settings=recipients_settings,
                    user=options.user,
                    versioned_settings=options.version_settings,
                )

            document_settings = options.document_settings
            if document_settings:
                updated_document = await update_document(
                    conn=conn,
                    data={
                        'document_id': self.document.id,
                        'category': document_settings.category,
                        'date_document': document_settings.date,
                        'number': document_settings.number,
                        'title': document_settings.title,
                        'amount': document_settings.amount,
                    },
                )
                self.document = updated_document

            signers_settings = options.signers_settings
            if signers_settings:
                signers = clear_duplicates(signers_settings.signer_entities)
                updated_document = await signatures.update_document_signers(
                    conn,
                    document=self.document,
                    company_id=user.company_id,
                    company_edrpou=user.company_edrpou,
                    signers=signers,
                    parallel_signing=signers_settings.is_parallel,
                    is_document_owner=options.is_document_owner,
                    current_role_id=user.role_id,
                    signers_source=signers_settings.signers_source,
                )
                self.document = updated_document

            if options.reviews_settings:
                await reviews_utils.replace_review_requests_in_db(
                    conn=conn,
                    user=user,
                    ctx=options.reviews_settings,
                )
                await reviews_utils.update_review_statuses_in_db(
                    conn=conn,
                    documents_ids=[self.document.id],
                    company_edrpou=user.company_edrpou,
                    company_id=user.company_id,
                )

            parameters_settings = options.parameters_settings
            if parameters_settings is not None:
                await update_document_parameters(
                    conn=conn,
                    document_id=self.document.id,
                    user=user,
                    ctx=parameters_settings,
                )

            tags_settings = options.tags_settings
            if tags_settings is not None:
                await self._update_tags(
                    conn=conn,
                    settings=tags_settings,
                    user=user,
                    document=self.document,
                    source=source,
                )

            version_settings = options.version_settings
            if version_settings is not None:
                output = await update_document_version_settings(
                    conn=conn,
                    document=self.document,
                    is_versioned=version_settings.is_versioned,
                    user=user,
                )
                self.version_update = output

            if viewers_settings := options.viewers_settings:
                await self._update_viewers(
                    conn=conn,
                    document=self.document,
                    settings=viewers_settings,
                    user=user,
                )

            if access_settings := options.access_settings:
                await self._update_access_settings(
                    conn=conn,
                    document=self.document,
                    user=user,
                    settings=access_settings,
                )

            # We have the update of the document status after applying all settings objects
            # because several settings objects should be taken into account when updating the
            # document status.
            if options.signers_settings or options.recipients_settings:
                await self._recalculate_document_status(conn)

            await self._delete_access(conn, options)
            await self._create_access(conn, options)

            await change_documents_for_public_api(
                conn=conn,
                document_ids=[self.document.id],
            )

    @staticmethod
    def _calculate_document_status_for_internal(
        document: Document,
        signatures: list[Signature],
        document_signers: list[DocumentSignerWithEdrpou],
    ) -> DocumentStatus:
        """
        Calculate document status for an internal document based on document state after update.
        """
        signatures_count = get_signatures_count(
            signatures=signatures,
            signers=document_signers,
            target_edrpou=document.edrpou_owner,
        )
        if not signatures_count:
            return DocumentStatus.ready_to_be_signed  # initial status for internal documents

        if signatures_count < document.expected_owner_signatures:
            return DocumentStatus.signed

        return DocumentStatus.finished

    @staticmethod
    def _calculate_document_status_for_bilateral(
        document: Document,
        recipients: list[DocumentRecipient],
        document_signers: list[DocumentSignerWithEdrpou],
        signatures: list[Signature],
        is_versioned: bool,
    ) -> DocumentStatus:
        """
        Calculate document status for a bilateral document based on document state after update.

        This code follows state change diagram for bilateral documents that you can find in the
        comments to the DocumentStatus enum.
        Additionally, this version takes into account document versioning: for versioned documents,
        first_sign_by is always considered as recipient for status calculation.
        """
        first_sign_by = document.first_sign_by
        if is_versioned:
            # For versioned documents, always treat as first_sign_by=recipient for status logic
            first_sign_by = FirstSignBy.recipient

        recipient = next((r for r in recipients if r.edrpou != document.edrpou_owner), None)
        if not recipient:
            return DocumentStatus.uploaded

        signatures_count_owner = get_signatures_count(
            signatures=signatures,
            signers=document_signers,
            target_edrpou=document.edrpou_owner,
        )
        signatures_count_recipient = get_signatures_count(
            signatures=signatures,
            signers=document_signers,
            target_edrpou=recipient.edrpou,
        )

        if first_sign_by == FirstSignBy.owner:
            return DocumentUpdate._calculate_document_status_for_bilateral_owner_first(
                document=document,
                recipient=recipient,
                signatures_count_owner=signatures_count_owner,
                signatures_count_recipient=signatures_count_recipient,
            )

        if first_sign_by == FirstSignBy.recipient:
            return DocumentUpdate._calculate_document_status_for_bilateral_recipient_first(
                document=document,
                recipient=recipient,
                signatures_count_owner=signatures_count_owner,
                signatures_count_recipient=signatures_count_recipient,
            )

        raise ValueError(f'Unknown first sign by value: {first_sign_by}')

    @staticmethod
    def _calculate_document_status_for_bilateral_owner_first(
        document: Document,
        recipient: DocumentRecipient,
        signatures_count_owner: int,
        signatures_count_recipient: int,
    ) -> DocumentStatus:
        """
        Calculate document status for a bilateral document when owner signs first.

        This is a helper function that handles the logic for documents where the owner signs first.
        It follows the state change diagram for bilateral documents.
        """
        # The document isn't signed by owner yet, waiting for an owner to sign
        if signatures_count_owner == 0:
            return DocumentStatus.ready_to_be_signed

        next_status = (
            DocumentStatus.signed
            if document.expected_recipient_signatures
            else DocumentStatus.approved
        )

        # The document is still on the owner side in process of signing
        if signatures_count_owner < document.expected_owner_signatures:
            return next_status

        # ... from this point we have enough signatures from the owner to send a document to
        # the recipient

        # [note-1] There can be a case when an owner has already signed the document but
        # hasn't sent it yet. Historically, for bilateral documents, we have a separate
        # action (send_document) to send the document to the recipient which user should
        # perform manually after update. If you are looking for a way to automate that logic,
        # consider using "documents.autosend_document_on_sign"
        if not recipient.is_sent:
            return next_status

        if not document.expected_recipient_signatures:
            return DocumentStatus.finished

        # Signed by owner and sent to recipient. And a recipient hasn't started signing yet
        if signatures_count_recipient == 0:
            return DocumentStatus.signed_and_sent

        # The recipient started signing
        if signatures_count_recipient < document.expected_recipient_signatures:
            return DocumentStatus.approved

        return DocumentStatus.finished

    @staticmethod
    def _calculate_document_status_for_bilateral_recipient_first(
        document: Document,
        recipient: DocumentRecipient,
        signatures_count_owner: int,
        signatures_count_recipient: int,
    ) -> DocumentStatus:
        """
        Calculate document status for a bilateral document when recipient signs first.

        This is a helper function that handles the logic for documents where the recipient
        signs first. It follows the state change diagram for bilateral documents.
        """

        # Preserve state in which owner is uploaded the document, but not sent it to the
        # recipient yet. See comment [note-1] for more details

        if not recipient.is_sent and signatures_count_recipient == 0:
            # Ready to be signed by owner (or first signer) and ready to be sent to recipient
            return DocumentStatus.ready_to_be_signed

        if signatures_count_recipient == 0:
            return DocumentStatus.sent  # sent by owner to recipient

        # The document is still on the recipient side
        if signatures_count_recipient < document.expected_recipient_signatures:
            # If there are any signatures from the recipient, keep signed status
            return DocumentStatus.signed

        # ... from this point we have enough signatures from the recipient

        # If owner is not expected to sign (changed to viewer or removed), document is finished
        if document.expected_owner_signatures == 0:
            return DocumentStatus.finished

        # If owner is expected to sign but hasn't signed yet
        if signatures_count_owner == 0:
            return DocumentStatus.signed_and_sent

        # If owner is expected to sign and is in process of signing
        if signatures_count_owner < document.expected_owner_signatures:
            return DocumentStatus.approved

        # Both sides have signed enough
        return DocumentStatus.finished

    @staticmethod
    def _calculate_document_status_for_multilateral(
        document: Document,
        recipients: list[DocumentRecipient],
        flows: list[FlowItem],
    ) -> DocumentStatus:
        """
        Calculate document status for a multilateral document based on document state after update.

        Status calculation logic:
        1. Preserve final statuses (finished, rejected, deleted)
        2. No recipients -> uploaded
        3. Document sent or signed -> check if all external
        flows completed or are viewers -> finished/flow
        4. Document not sent -> uploaded
        5. All flows completed or are viewers -> finished, otherwise -> flow
        """
        # Preserve final statuses - they should never be changed
        if document.status.is_final:
            return document.status

        # No recipients means document is still in uploaded state
        if not recipients:
            return DocumentStatus.uploaded

        # Get all flows from external companies (excluding document owner)
        external_flows = [flow for flow in flows if flow.edrpou != document.edrpou_owner]

        # Check if document has been sent to recipients or signed by anyone
        is_document_in_process = any(flow.is_signed_once for flow in external_flows) or any(
            flow.is_sent for flow in external_flows
        )

        if is_document_in_process:
            # Document is in signing process - check if all external flows are completed
            # Flow is considered completed if it's finished OR recipient became viewer
            all_external_flows_completed = all(
                flow.is_finished or flow.signatures_count == 0 for flow in external_flows
            )
            return DocumentStatus.finished if all_external_flows_completed else DocumentStatus.flow

        # Document exists but not sent to any external recipients yet
        if not any(
            recipient.is_sent
            for recipient in recipients
            if recipient.edrpou != document.edrpou_owner
        ):
            return DocumentStatus.uploaded

        # Document sent but not all flows completed - check final completion status
        # Include viewers (signatures_count == 0) as completed flows
        all_flows_completed = all(flow.is_finished or flow.signatures_count == 0 for flow in flows)
        return DocumentStatus.finished if all_flows_completed else DocumentStatus.flow

    async def _recalculate_document_status(self, conn: DBConnection) -> None:
        """
        Recalculate and save document status after updating objects that can affect status:
         - recipients (options.recipients_settings)
         - document signers (options.signers_settings)
         - signatures (not updated in DocumentUpdate, can be cached if needed someone else)
        """

        from app.document_versions.utils import is_versioned_document
        from app.flow.utils import get_flows_state

        document = self.document

        # Preserve all final status as is: finished, rejected and deleted. There is no indicator
        # that can determine that document was rejected, except the status itself.
        if document.status.is_final:
            return

        # Get fresh objects that effect on document status
        recipients = await db.select_document_recipients(conn=conn, document_id=document.id)
        signatures = await select_signatures(conn, document_ids=[document.id])
        document_signers = await select_document_signers_extended(conn, document_id=document.id)
        flows = await get_flows_state(conn=conn, document_id=document.id)

        new_status: DocumentStatus | None = None
        if document.is_internal:
            new_status = self._calculate_document_status_for_internal(
                document=document,
                signatures=signatures,
                document_signers=document_signers,
            )
        elif document.is_bilateral:
            is_versioned = await is_versioned_document(conn=conn, document_id=document.id)
            new_status = self._calculate_document_status_for_bilateral(
                document=document,
                recipients=recipients,
                signatures=signatures,
                document_signers=document_signers,
                is_versioned=is_versioned,
            )
        elif document.is_multilateral:
            new_status = self._calculate_document_status_for_multilateral(
                document=document,
                recipients=recipients,
                flows=flows.flows,
            )

        if new_status and document.status != new_status:
            await update_document_status(
                conn=conn,
                document_id=document.id,
                status=new_status,
            )
            if new_status == DocumentStatus.finished:
                self.should_schedule_finished_status_jobs = True

    async def _update_recipients_to_internal(
        self,
        conn: DBConnection,
        document: Document,
        settings: UpdateRecipientsExtendedCtx,
    ) -> None:
        """
        Change to a document to an internal type during recipients update
        """

        # The document is already internal, nothing to do
        if document.is_internal:
            return

        # Because internal documents should not have any records in `recipients` table, we
        # remove all previous recipients, including the document owner.
        await self._remove_document_flows(conn, document_id=document.id)
        await self._remove_previous_recipients(
            conn=conn,
            document_id=document.id,
            previous_recipients=settings.prev_recipients,
            ignore_edrpous=[],
        )

        await self._remove_previous_recipients_resources(
            conn=conn,
            document_id=document.id,
            previous_recipients=settings.prev_recipients,
            # keep document owner resources
            ignore_edrpous=[document.edrpou_owner],
        )

        signers_count = settings.prev_document_signers_count
        signers_count_owner = signers_count[document.edrpou_owner]

        # Change from bilateral or multilateral to internal
        await update_document(
            conn=conn,
            data={
                'document_id': document.id,
                'is_internal': True,
                'is_multilateral': False,
                'expected_recipient_signatures': 0,
                # can be updated by signer settings letter
                'expected_owner_signatures': signers_count_owner or 1,
                '_unsafe_edrpou_recipient': None,
                '_unsafe_email_recipient': None,
                'first_sign_by': FirstSignBy.owner,
                # - "status_id" will be updated after all settings objects are processed by
                #   "self._recalculate_document_status"
            },
        )

    async def _update_tags(
        self,
        conn: DBConnection,
        settings: UpdateTagsSettings,
        document: Document,
        user: User,
        source: Source,
    ) -> None:
        from app.tags import utils as tags_utils

        action = settings.action
        if action == UpdateTagsAction.add:
            await tags_utils.add_tags(
                conn=conn,
                company_id=user.company_id,
                company_edrpou=user.company_edrpou,
                documents_ids=[document.id],
                tags_ids=settings.tags_ids,
                new_tags_names=settings.new_tags,
                assigner_role_id=user.role_id,
            )
            self.open_tag_access_tags_ids.update(settings.tags_ids)

            return

        if action == UpdateTagsAction.remove:
            listing_ids = await tags_utils.disconnect_documents_and_tags(
                conn=conn,
                user=user,
                documents_ids=[document.id],
                tags_ids=settings.tags_ids,
                request_source=source,
            )

            # We will schedule an async job to delete tag access after transaction commit
            self.delete_tag_access_listing_ids.update(listing_ids)
            return

        if action == UpdateTagsAction.replace:
            output = await tags_utils.replace_tags(
                conn=conn,
                user=user,
                document_id=document.id,
                document_edrpou_owner=document.edrpou_owner,
                document_title=document.title,
                new_tags_ids=settings.tags_ids,
                new_tags_names=settings.new_tags,
            )

            # We will schedule an async job to delete tag access after transaction commit
            self.delete_tag_access_listing_ids.update(output.listings_ids_to_delete)
            self.open_tag_access_tags_ids.update(output.tags_ids_connected)
            return

        assert_never(action)

    async def _update_viewers(
        self,
        conn: DBConnection,
        document: Document,
        settings: UpdateViewersData,
        user: User,
    ) -> None:
        action = settings.action
        if action == UpdateViewersAction.add:
            # Filter out roles that already have viewer access
            roles_to_add = []
            group_roles_to_add = []
            if settings.viewer_role_ids:
                viewer_listings = await db.select_listings_by_source(
                    conn=conn,
                    document_id=document.id,
                    source=AccessSource.viewer,
                    company_edrpou=user.company_edrpou,
                    roles_ids=settings.viewer_role_ids,
                )
                existed_roles = {listing.role_id for listing in viewer_listings}
                roles_to_add = list(set(settings.viewer_role_ids) - existed_roles)
            if settings.group_ids:
                group_viewer_listings = await db.select_listings_by_source(
                    conn=conn,
                    document_id=document.id,
                    source=AccessSource.group_viewer,
                    company_edrpou=user.company_edrpou,
                    roles_ids=settings.viewer_role_ids,
                )
                existed_roles = {listing.role_id for listing in group_viewer_listings}
                group_roles_to_add = list(set(settings.group_viewer_role_ids) - existed_roles)

            # Insert new viewers access
            aggregator = ListingDataAggregator()
            for role_id in roles_to_add:
                aggregator.add(
                    document_id=document.id,
                    access_edrpou=user.company_edrpou,
                    role_id=role_id,
                    source=AccessSource.viewer,
                )
            for role_id in group_roles_to_add:
                aggregator.add(
                    document_id=document.id,
                    access_edrpou=user.company_edrpou,
                    role_id=role_id,
                    source=AccessSource.group_viewer,
                )
            await insert_listings(conn=conn, data=aggregator.as_db())
            if settings.group_ids:
                await insert_group_document_access(
                    conn=conn,
                    values_list=[
                        {
                            'document_id': document.id,
                            'group_id': group_id,
                            'created_by': user.role_id,
                            'company_id': user.company_id,
                        }
                        for group_id in settings.group_ids
                    ],
                )

            # This list will be used to send notifications about new access
            self.added_viewers.extend(roles_to_add)
            self.added_viewers.extend(group_roles_to_add)

            return

        if action == UpdateViewersAction.remove:
            if settings.viewer_role_ids:
                viewer_listings = await db.select_listings_by_source(
                    conn=conn,
                    document_id=document.id,
                    source=AccessSource.viewer,
                    company_edrpou=user.company_edrpou,
                    roles_ids=settings.viewer_role_ids,
                )
                listings_ids = [listing.id for listing in viewer_listings]
                deleted_viewers_ctx = await remove_listings_source(
                    conn=conn,
                    listings_ids=listings_ids,
                    source=AccessSource.viewer,
                )
                self.deleted_company_listing_ids.extend(deleted_viewers_ctx.company_listing_ids)
            if settings.group_ids:
                remove_access_for_roles = await get_roles_to_remove_on_group_remove(
                    conn=conn,
                    document=document,
                    settings=settings,
                )
                group_viewer_listings = await db.select_listings_by_source(
                    conn=conn,
                    document_id=document.id,
                    source=AccessSource.group_viewer,
                    company_edrpou=user.company_edrpou,
                    roles_ids=remove_access_for_roles,
                )
                group_listings_ids = [listing.id for listing in group_viewer_listings]
                deleted_group_viewer_ctx = await remove_listings_source(
                    conn=conn,
                    listings_ids=group_listings_ids,
                    source=AccessSource.group_viewer,
                )
                await delete_group_document_accesses(
                    conn=conn,
                    document_id=document.id,
                    group_ids=settings.group_ids,
                )
                self.deleted_company_listing_ids.extend(
                    deleted_group_viewer_ctx.company_listing_ids
                )
            return

        if action == UpdateViewersAction.replace:
            listings = await db.select_listings_by_source(
                conn=conn,
                document_id=document.id,
                source=AccessSource.viewer,
                company_edrpou=user.company_edrpou,
            )
            existing_map = {listing.role_id: listing for listing in listings}
            existing_roles = set(existing_map.keys())
            new_roles = set(settings.viewer_role_ids)

            # Find which roles to remove and which to add
            roles_to_remove = list(existing_roles - new_roles)
            roles_to_add = list(new_roles - existing_roles)

            group_viewer_listings = await db.select_listings_by_source(
                conn=conn,
                document_id=document.id,
                source=AccessSource.group_viewer,
                company_edrpou=user.company_edrpou,
            )
            existing_group_roles_map = {
                listing.role_id: listing for listing in group_viewer_listings
            }
            existing_group_roles = set(existing_group_roles_map.keys())
            new_roles = set(settings.group_viewer_role_ids)

            group_roles_to_remove = list(existing_group_roles - new_roles)
            group_roles_to_add = list(new_roles - existing_group_roles)

            # Actually remove roles that are not in the new list
            listings_to_remove = [existing_map[role_id].id for role_id in roles_to_remove]
            deleted_ctx = await remove_listings_source(
                conn=conn,
                listings_ids=listings_to_remove,
                source=AccessSource.viewer,
            )
            listings_to_remove = [
                existing_group_roles_map[role_id].id for role_id in group_roles_to_remove
            ]
            deleted_group_ctx = await remove_listings_source(
                conn=conn,
                listings_ids=listings_to_remove,
                source=AccessSource.group_viewer,
            )
            # Insert new viewers access
            aggregator = ListingDataAggregator()
            for role_id in roles_to_add:
                aggregator.add(
                    document_id=document.id,
                    access_edrpou=user.company_edrpou,
                    role_id=role_id,
                    source=AccessSource.viewer,
                )
            for role_id in group_roles_to_add:
                aggregator.add(
                    document_id=document.id,
                    access_edrpou=user.company_edrpou,
                    role_id=role_id,
                    source=AccessSource.group_viewer,
                )
            await insert_listings(conn=conn, data=aggregator.as_db())
            await delete_group_document_accesses(
                conn=conn,
                document_id=document.id,
            )
            if settings.group_ids:
                await insert_group_document_access(
                    conn=conn,
                    values_list=[
                        {
                            'document_id': document.id,
                            'group_id': group_id,
                            'created_by': user.role_id,
                            'company_id': user.company_id,
                        }
                        for group_id in settings.group_ids
                    ],
                )

            # Set attributes for async jobs
            self.added_viewers.extend(roles_to_add)
            self.added_viewers.extend(group_roles_to_add)

            self.deleted_company_listing_ids.extend(deleted_ctx.company_listing_ids)
            self.deleted_company_listing_ids.extend(deleted_group_ctx.company_listing_ids)

            return

        assert_never(action)

    async def _update_access_settings(
        self,
        conn: DBConnection,
        *,
        document: Document,
        user: User,
        settings: UpdateDocumentAccessSettingsCtx,
    ) -> None:
        """
        Update access settings for the document

        Currently, we have only one access settings - "level". We expected that minor amount of
        documents in companies will be private, so we use specialized table
        "document_access_private_table" to store only the private documents. If this table doesn't
        have a record, it means that the document has "extended" access level.

        In the future, this block might be extended with other access levels or settings, like
        sharing document publicly by link.
        """

        if settings.prev_level == settings.new_level:
            return

        if settings.new_level == DocumentAccessLevel.private:
            await db.insert_document_access_settings_private(
                conn=conn,
                documents_ids=[document.id],
                edrpou=user.company_edrpou,
            )

            # When we make document private, we should remove "tag" access from the document
            # for everyone in the current company, like we've never opened access by tag.
            # Users that had only "tag" access will lose access to the document.
            listings_ids = await select_document_listings_with_tag_source(
                conn=conn,
                document_id=document.id,
                company_edrpou=user.company_edrpou,
            )
            self.delete_tag_access_listing_ids.update(listings_ids)
            self.open_tag_access_tags_ids.clear()

        elif settings.new_level == DocumentAccessLevel.extended:
            await db.delete_document_access_settings_private(
                conn=conn,
                documents_ids=[document.id],
                edrpou=user.company_edrpou,
            )

            # When a document is private, we don't open access by tag for roles that have this
            # tag in their profile. But it is expected that if a document is extended, then roles
            # that at least one tag from a document in their profile, should have access to the
            # document
            tags_ids = await select_document_tags_ids(
                conn=conn,
                document_id=document.id,
                company_id=user.company_id,
            )
            self.open_tag_access_tags_ids.update(tags_ids)

        else:
            assert_never(settings.new_level)

        self.document_actions.append(
            document_actions.DocumentAction(
                action=document_actions.Action.access_settings_changed,
                document_id=document.id,
                document_edrpou_owner=document.edrpou_owner,
                document_title=document.title,
                company_id=user.company_id,
                company_edrpou=user.company_edrpou,
                email=user.email,
                role_id=user.role_id,
                extra={'level': settings.new_level},
            )
        )

    async def _update_recipients_to_bilateral_empty(
        self,
        conn: DBConnection,
        document: Document,
        settings: UpdateRecipientsExtendedCtx,
    ) -> None:
        """
        It is possible to move from internal to bilateral and do not set any recipients.

        This is equivalent to the uploading of a new document with empty "recipients" field.
        """

        assert document.is_internal, 'Document must be internal'

        previous_recipients = settings.prev_recipients

        await self._remove_document_flows(conn, document_id=document.id)
        await self._remove_previous_recipients(
            conn=conn,
            document_id=document.id,
            previous_recipients=previous_recipients,
            # empty bilateral document has no recipients, so we can remove a document
            # owner from `recipients` table too
            ignore_edrpous=[],
        )

        await self._remove_previous_recipients_resources(
            conn=conn,
            document_id=document.id,
            previous_recipients=previous_recipients,
            # keep document owner resources
            ignore_edrpous=[document.edrpou_owner],
        )

        signers_count = settings.prev_document_signers_count
        signers_count_owner = signers_count[document.edrpou_owner]

        update_data = UpdateDocumentDict(
            document_id=document.id,
            is_internal=False,
            is_multilateral=False,
            expected_owner_signatures=signers_count_owner or 1,
            expected_recipient_signatures=1,
            first_sign_by=FirstSignBy.owner,
            # such as we allow to move from internal to empty bilateral, columns "edrpou_recipient"
            # and "email_recipient" should be already empty, so we don't need to reset them as in
            # other cases.
        )

        await update_document(conn, data=update_data)

    async def _update_recipients_to_bilateral(
        self,
        conn: DBConnection,
        user: User,
        document: Document,
        settings: UpdateRecipientsExtendedCtx,
        versioned_settings: UpdateVersionedCtx | None,
    ) -> None:
        """
        Update recipients of the document and change document type to bilateral
        """

        ctx = settings.get_bilateral_recipients(document_owner_edrpou=document.edrpou_owner)
        recipient = ctx.recipient

        await self._remove_document_flows(conn, document_id=document.id)
        # We don't delete here recipients and their resources because the function
        # "change_bilateral_recipient_base" contains that logic inside.

        # { edrpou: count }
        prev_signers_count = settings.prev_document_signers_count
        signers_count_recipient = prev_signers_count[recipient.edrpou]
        signers_count_owner = prev_signers_count[document.edrpou_owner]

        # If recipient role changed to viewer, force expected_recipient_signatures to 0
        if not recipient.is_signer:
            signers_count_recipient = 0

        # If document is versioned, we always should set first_sign_by=recipient
        if versioned_settings is None:
            is_versioned = await is_versioned_document(conn, document_id=document.id)
        else:
            is_versioned = versioned_settings.is_versioned

        update_data = UpdateDocumentDict(
            document_id=document.id,
            is_internal=False,
            is_multilateral=False,
            # When recipient or owner has document signers "expected_signatures" fields depends
            # on the number of document signers for the corresponding company.
            expected_owner_signatures=(signers_count_owner or ctx.expected_signatures_owner),
            expected_recipient_signatures=(
                signers_count_recipient or ctx.expected_signatures_recipient
            ),
            first_sign_by=FirstSignBy.recipient if is_versioned else ctx.first_sign_by,
            # - "edrpou_recipient" and "email_recipient" will be updated later
            #   by "db.replace_bilateral_recipients" function
            # - "status" will be updated later by "self._recalculate_document_status" function
        )

        await update_document(conn, data=update_data)

        update = await change_bilateral_recipient_base(
            conn=conn,
            document_id=document.id,
            document_edrpou_owner=document.edrpou_owner,
            document_title=document.title,
            new_recipient_edrpou=recipient.edrpou,
            new_recipient_emails=recipient.emails or [],
            new_recipient_emails_hidden=recipient.is_email_hidden,
            new_first_sign_by=ctx.first_sign_by,
            previous_first_sign_by=document.first_sign_by,
            previous_is_bilateral=document.is_bilateral,
            previous_recipients=settings.prev_recipients,
            previous_status=document.status,
            current_user=user,
        )

        self.deleted_company_listing_ids.extend(update.deleted.listings_ctx.company_listing_ids)
        self.comments_ids_deleted.extend(update.deleted.comments_ids)
        self.should_send_bilateral_recipient_jobs = update.resend_to_recipient

    async def _update_recipients_to_multilateral(
        self,
        conn: DBConnection,
        document: Document,
        settings: UpdateRecipientsExtendedCtx,
        user: User,
    ) -> None:
        """
        Change a document type to multilateral, update recipients and create flows
        """

        from app.flow import utils as flow_utils

        await self._remove_previous_recipients_resources(
            conn=conn,
            document_id=document.id,
            previous_recipients=settings.prev_recipients,
            ignore_edrpous=settings.recipients_edrpous,
        )

        # For multilateral documents, we first remove all previous recipients and flows
        # and then try to recreate them based on the new settings.
        await self._remove_document_flows(conn, document_id=document.id)
        await self._remove_previous_recipients(
            conn=conn,
            document_id=document.id,
            previous_recipients=settings.prev_recipients,
            ignore_edrpous=[],
        )

        await update_document(
            conn=conn,
            data={
                'document_id': document.id,
                'is_internal': False,
                'is_multilateral': True,
                'expected_recipient_signatures': 1,
                'expected_owner_signatures': 1,
                '_unsafe_edrpou_recipient': None,
                '_unsafe_email_recipient': None,
                'first_sign_by': FirstSignBy.owner,
                # the status will be updated later by create_flows function
            },
        )

        prev_state = DocumentRecipientsState(
            document=document,
            recipients=settings.prev_recipients,
            flows=settings.prev_flow_state.flows,
            signatures=settings.prev_signatures,
            document_signers=settings.prev_document_signers,
        )

        # { edrpou: count }
        prev_signers_count = settings.prev_document_signers_count

        # recipients are already ordered in the same way as they selected for signing
        prev_recipients: list[DocumentRecipientStateItem] = copy.copy(prev_state.recipients_items)
        flows_ctx = []
        for order, recipient in enumerate(settings.recipients, start=0):
            if recipient.is_signer:
                expected_signatures_count = prev_signers_count[recipient.edrpou] or 1
            else:
                expected_signatures_count = 0

            item = CreateFlowCtx(
                flow_edrpou=recipient.edrpou,
                flow_order=order if settings.is_ordered else None,
                # can be overwritten with the number of document signers
                # in the "create_flows" function
                flow_signatures_count=expected_signatures_count,
                recipient_emails=recipient.emails or [],
            )

            # Find a first recipient with the same EDRPOU and emails and remove it
            # from the list to avoid reusing the same recipient object more than once.
            prev_recipient = find_remove_in_list(
                items=prev_recipients,
                key=lambda r: r.is_equal_recipient(
                    edrpou=recipient.edrpou,
                    emails=recipient.emails or [],
                ),
            )
            # Owner in previous recipients may not have an emails at all, so should try to find
            # an owner object by EDRPOU only.
            if (
                not prev_recipient
                and prev_state.is_bilateral
                and recipient.edrpou == document.edrpou_owner
            ):
                prev_recipient = find_remove_in_list(
                    items=prev_state.recipients_items,
                    key=lambda r: r.edrpou == recipient.edrpou,
                )

            # Copy attributes from previously deleted objects to preserve them
            # and make it look like we are updating them.
            if prev_recipient:
                item.flow_id = prev_recipient.flow_id
                item.flow_meta = prev_recipient.flow_meta
                item.flow_date_sent = prev_recipient.date_sent

                item.recipient_id = prev_recipient.recipient_id
                item.recipient_is_emails_hidden = prev_recipient.is_emails_hidden
                item.recipient_date_created = prev_recipient.date_created
                item.recipient_assigner_role_id = prev_recipient.assigner_role_id
                item.recipient_external_meta = prev_recipient.external_meta

                # Unlike with the update to bilateral, we don't perform an "unsend" for
                # recipients, even if, after the recipients update, they move further in the
                # signing order and are no longer the next company expected to sign. For example,
                # the company was moved from 1st to 3rd place, while we still await a signature
                # from the 1st company to sign, we don't unsend a document to this company after
                # update.
                #
                # There are no business requirements for such difference between bilateral and
                # multilateral documents, it was just historically implemented this way
                item.recipient_date_sent = prev_recipient.date_sent
                item.recipient_date_received = prev_recipient.date_received
                item.recipient_date_delivered = prev_recipient.date_delivered

                # Special attention to "pending_signatures" attributes. When a request has a
                # more or less "signatures" count than the previous recipient, we should update
                # pending signatures count properly to avoid having more pending signatures
                # than expected.
                if item.flow_signatures_count == prev_recipient.expected_signatures:
                    item.flow_pending_signatures_count = prev_recipient.pending_signatures
                else:
                    # how many signatures were added? (should be not more than expected)
                    _added = min(
                        prev_recipient.expected_signatures - prev_recipient.pending_signatures,
                        prev_recipient.expected_signatures,
                    )
                    # how many signatures are pending? (should non-negative)
                    _pending = max(item.flow_signatures_count - _added, 0)
                    item.flow_pending_signatures_count = _pending

            flows_ctx.append(item)

        # We shouldn't send a document to the next recipient if flows is_ordered=True
        # and one of flows has unfinished status
        exist_unfinished_flow = any(
            flow_.flow_meta and flow_.flow_meta.unfinished for flow_ in flows_ctx
        )
        should_send = (
            False if (exist_unfinished_flow and settings.is_ordered) else settings.should_autosend
        )
        _options = AddFlowOptions(
            should_send=should_send,
            # we manage document status by ourselves in "self._recalculate_document_status" method
            should_update_document_status=False,
            # we manage "flow_signatures_count" by ourselves in this function
            should_count_signers=False,
            documents=[document],
            receivers=flows_ctx,
            assigner_role_id=user.role_id,
        )
        await flow_utils.create_flows(conn, _options)

        if _options.should_send:
            self.should_send_multilateral_recipient_jobs = True

    @staticmethod
    async def _remove_previous_recipients(
        conn: DBConnection,
        document_id: str,
        previous_recipients: list[DocumentRecipient],
        ignore_edrpous: list[str],
    ) -> None:
        """
        Remove previous recipients from the document, except the owner
        """

        edrpous = {recipient.edrpou for recipient in previous_recipients}
        edrpous = edrpous - set(ignore_edrpous)

        logger.info(msg='Removing previous recipients', extra={'edrpous': edrpous})

        await db.delete_recipients(conn=conn, document_id=document_id, edrpous=list(edrpous))

    async def _remove_previous_recipients_resources(
        self,
        conn: DBConnection,
        document_id: str,
        previous_recipients: list[DocumentRecipient],
        ignore_edrpous: list[str],
    ) -> None:
        """
        Remove resources created by previous recipients, except the owner
        """

        edrpous = {recipient.edrpou for recipient in previous_recipients}
        edrpous = edrpous - set(ignore_edrpous)

        logger.info(msg='Removing previous recipients resources', extra={'edrpous': edrpous})

        deleted = await delete_recipients_document_resources(
            conn=conn,
            document_id=document_id,
            companies_edrpous=list(edrpous),
        )
        self.deleted_company_listing_ids.extend(deleted.listings_ctx.company_listing_ids)
        self.comments_ids_deleted.extend(deleted.comments_ids)

    @staticmethod
    async def _remove_document_flows(conn: DBConnection, document_id: str) -> None:
        from app.flow import utils as flow_utils

        await flow_utils.delete_flows(conn, document_id=document_id)

    async def _update_recipients(
        self,
        conn: DBConnection,
        document: Document,
        settings: UpdateRecipientsExtendedCtx,
        user: User,
        versioned_settings: UpdateVersionedCtx | None,
    ) -> None:
        """
        Update recipients of the document based on the settings
        """

        # Create new recipients and related resources by applying appropriate
        # logic based on a new document type, determined by the number of recipients
        # passed in the settings
        if settings.is_internal:
            await self._update_recipients_to_internal(
                conn=conn,
                document=document,
                settings=settings,
            )

        elif settings.is_empty:
            await self._update_recipients_to_bilateral_empty(
                conn=conn,
                document=document,
                settings=settings,
            )

        elif settings.is_bilateral:
            await self._update_recipients_to_bilateral(
                conn=conn,
                user=user,
                document=document,
                settings=settings,
                versioned_settings=versioned_settings,
            )

        elif settings.is_multilateral:
            await self._update_recipients_to_multilateral(
                conn=conn,
                document=document,
                settings=settings,
                user=user,
            )

        # In all functions above we update the document in database, so we need to update the
        # document object as well to have the latest state of the document for further updates
        self.document = await get_expected_document(conn, document_id=document.id)

    async def send_notifications(
        self,
        conn: DBConnection,
        options: UpdateDocumentOptions,
        source: Source,
    ) -> None:
        """
        Send notifications about document update to signers, reviewers, viewers, etc.

        Should be called outside of transaction to avoid losing updates
        """
        from app.flow.utils import send_multilateral_document_notification_job

        user = options.user

        if options.signers_settings and options.signers_settings.roles_ids:
            # Send notification to signer only when all required reviews
            # was finished
            no_active_reviews = await reviews.all_required_reviews_was_finished(
                conn=conn,
                edrpou=user.company_edrpou,
                document_id=self.document.id,
                # TODO: add proper document version ID here, because in case when user update
                #  versioned document, we should also check that review for last version of document
                #  was finished
                version_id=None,
            )

            # See details about "not sent" condition in "_validate_can_company_update_signers"
            if no_active_reviews and not is_bilateral_3p_not_sent_document(self.document):
                await reviews.send_first_notification_to_signers(
                    documents_ids=[self.document.id],
                    current_company_id=user.company_id,
                    current_role_id=user.role_id,
                )
                await send_multilateral_document_notification_job(
                    document_id=self.document.id,
                    company_edrpou=user.company_edrpou,
                )

        reviews_settings = options.reviews_settings
        if reviews_settings:
            states = {
                self.document.id: reviews_settings.state,
            }
            await reviews_utils.send_review_notifications(
                conn=conn,
                documents_ids=[self.document.id],
                user=user,
                states=states,
                request_source=Source.api_internal,
            )

    async def schedule_bilateral_recipient_jobs(
        self,
        conn: DBConnection,
        options: UpdateDocumentOptions,
        user: User,
        source: Source,
    ) -> None:
        recipient = await select_bilateral_document_recipient(
            conn=conn,
            document_id=self.document.id,
            document_owner_edrpou=self.document.edrpou_owner,
        )
        if not recipient:
            # It can happen when the recipient is the same company as the owner.
            # Some time ago, we allowed documents to be sent to the same company as the
            # owner, so we need to handle this case properly.
            logger.info(
                msg='Could not send change bilateral recipient job',
                extra={
                    'document_id': self.document.id,
                    'document_owner_edrpou': self.document.edrpou_owner,
                    'user_edrpou': user.company_edrpou,
                },
            )
        else:
            await send_change_bilateral_document_recipient_jobs(
                conn=conn,
                document_id=self.document.id,
                recipient_edrpou=recipient.edrpou,
                recipient_emails=recipient.emails,
                user=user,
                request_source=source,
            )

    async def schedule_multilateral_recipient_jobs(self, source: Source) -> None:
        from app.flow.utils import send_multilateral_documents_to_recipients_job

        await send_multilateral_documents_to_recipients_job(
            documents_ids=[self.document.id],
            source=source,
        )

    async def _schedule_async_jobs(
        self,
        conn: DBConnection,
        options: UpdateDocumentOptions,
        source: Source,
    ) -> None:
        """
        Schedule async jobs that can be performed outside of transaction
        """
        from app.comments.utils import schedule_remove_comments_from_index
        from app.tags.utils import (
            schedule_create_document_access_on_document_tag_job,
            schedule_delete_tag_access_job,
        )

        user = options.user

        if comments_ids := self.comments_ids_deleted:
            await schedule_remove_comments_from_index(comment_ids=comments_ids)

        await schedule_remove_company_listings_es(
            company_listing_ids=self.deleted_company_listing_ids
        )

        if self.should_send_bilateral_recipient_jobs:
            await self.schedule_bilateral_recipient_jobs(
                conn=conn,
                options=options,
                user=user,
                source=source,
            )

        if self.should_send_multilateral_recipient_jobs:
            await self.schedule_multilateral_recipient_jobs(source=source)

        if listings_ids := self.delete_tag_access_listing_ids:
            await schedule_delete_tag_access_job(listings_ids=list(listings_ids))

        if tags_ids := self.open_tag_access_tags_ids:
            await schedule_create_document_access_on_document_tag_job(
                documents_ids=[self.document.id],
                tags_ids=list(tags_ids),
                company_edrpou=user.company_edrpou,
                assigner_role_id=user.role_id,
                source=source,
            )

        if added_viewers := self.added_viewers:
            # Remove current user from the list who will receive a notification about new access
            added_viewers = [role_id for role_id in added_viewers if role_id != user.role_id]
            await send_notification_about_document_access(
                user=user,
                document_id=self.document.id,
                document_title=self.document.title,
                roles_ids=added_viewers,
                source=source,
            )

        if self.should_schedule_finished_status_jobs:
            documents_with_info = await select_documents_by_ids_with_company_info(
                conn, [self.document.id]
            )
            await schedule_jobs_about_finished_document(
                document=DocumentWithUploader.from_row(documents_with_info[0])
            )

        if self.original_document.status != self.document.status:
            await send_document_status_callback_job(
                document_id=self.document.id,
                uploaded_by_edrpou=self.document.edrpou_owner,
            )

    async def _save_actions(self) -> None:
        """
        Actions is stored in separate database, we must collect and save them outside
        the main transaction
        """
        await document_actions.add_document_actions(self.document_actions)

    async def delete_access_for_signers(
        self,
        conn: DBConnection,
        *,
        document_id: str,
        user_company_edrpou: str,
        ignore_roles_ids: list[str],
    ) -> None:
        listings = await select_listings_by_source(
            conn=conn,
            document_id=document_id,
            company_edrpou=user_company_edrpou,
            source=AccessSource.signer,
        )
        listings_ids = [item.id for item in listings if item.role_id not in ignore_roles_ids]

        deleted_listings_ctx = await remove_listings_source(
            conn, listings_ids, source=AccessSource.signer
        )
        self.deleted_company_listing_ids.extend(deleted_listings_ctx.company_listing_ids)

    async def delete_access_for_reviewers(
        self,
        conn: DBConnection,
        *,
        document_id: str,
        user_company_edrpou: str,
        ignore_roles_ids: list[str],
    ) -> None:
        listings = await select_listings_by_source(
            conn=conn,
            document_id=document_id,
            company_edrpou=user_company_edrpou,
            source=AccessSource.reviewer,
        )
        listings_ids = [item.id for item in listings if item.role_id not in ignore_roles_ids]
        deleted_listings_ctx = await remove_listings_source(
            conn, listings_ids, source=AccessSource.reviewer
        )
        self.deleted_company_listing_ids.extend(deleted_listings_ctx.company_listing_ids)

    async def delete_access_for_group_viewers(
        self,
        conn: DBConnection,
        *,
        document_id: str,
        user_company_edrpou: str,
        ignore_roles_ids: list[str],
    ) -> None:
        listings = await select_listings_by_source(
            conn=conn,
            document_id=document_id,
            company_edrpou=user_company_edrpou,
            source=AccessSource.group_viewer,
        )
        listings_ids = [item.id for item in listings if item.role_id not in ignore_roles_ids]
        deleted_listings_ctx = await remove_listings_source(
            conn, listings_ids, source=AccessSource.group_viewer
        )
        self.deleted_company_listing_ids.extend(deleted_listings_ctx.company_listing_ids)

    async def _delete_access(
        self,
        conn: DBConnection,
        options: UpdateDocumentOptions,
    ) -> None:
        # Revoke access for all recipients, except the owner. Access should be recreated on
        # the next steps.

        # all access with signer source and remove listings from sources that
        # does not contain in a new signers list.
        if signers_settings := options.signers_settings:
            await self.delete_access_for_signers(
                conn=conn,
                document_id=self.document.id,
                user_company_edrpou=options.user.company_edrpou,
                ignore_roles_ids=signers_settings.roles_ids,
            )

        if review_settings := options.reviews_settings:
            await self.delete_access_for_reviewers(
                conn=conn,
                document_id=self.document.id,
                user_company_edrpou=options.user.company_edrpou,
                ignore_roles_ids=review_settings.roles_ids,
            )

    @staticmethod
    def prepare_access_for_signers(
        *,
        document_id: str,
        user_company_edrpou: str,
        roles_ids: list[str],
    ) -> ListingDataAggregator:
        listing_data = ListingDataAggregator()

        for role_id in roles_ids:
            listing_data.add(
                access_edrpou=user_company_edrpou,
                document_id=document_id,
                role_id=role_id,
                source=AccessSource.signer,
            )

        return listing_data

    @staticmethod
    def prepare_access_for_reviewers(
        *,
        document_id: str,
        user_company_edrpou: str,
        roles_ids: list[str],
    ) -> ListingDataAggregator:
        listing_data = ListingDataAggregator()

        for role_id in roles_ids:
            listing_data.add(
                access_edrpou=user_company_edrpou,
                document_id=document_id,
                role_id=role_id,
                source=AccessSource.reviewer,
            )

        return listing_data

    async def _create_access(
        self,
        conn: DBConnection,
        options: UpdateDocumentOptions,
    ) -> None:
        """Execute one update of access in listing table on document update"""
        listing_data = ListingDataAggregator()

        signers_settings = options.signers_settings
        if signers_settings:
            signers_listing_data = self.prepare_access_for_signers(
                document_id=self.document.id,
                user_company_edrpou=options.user.company_edrpou,
                roles_ids=signers_settings.roles_ids,
            )
            listing_data.merge(signers_listing_data)

        if reviews_settings := options.reviews_settings:
            reviewer_listing_data = self.prepare_access_for_reviewers(
                document_id=self.document.id,
                user_company_edrpou=options.user.company_edrpou,
                roles_ids=reviews_settings.roles_ids,
            )
            listing_data.merge(reviewer_listing_data)

        await insert_listings(conn, data=listing_data.as_db())

    async def _cleanup_document_version_from_s3(self) -> None:
        """
        During the update of version settings, we copy the content of the document from the old
        place to the new one. However, we don't remove content from the old place before the
        transaction commits to avoid losing data in case of a rollback. Here, we are removing the
        content from the old place on S3.
        """
        output = self.version_update
        if not output:
            return

        if output.is_version_created:
            # Remove the content from S3 for non-versioned documents
            key = get_document_s3_key(document_id=self.document.id, version_id=None)
            await s3_utils.delete(key=key)

        if output.is_version_deleted:
            # Remove the content from S3 for versioned documents
            key = get_document_s3_key(document_id=self.document.id, version_id=output.version.id)
            await s3_utils.delete(key=key)


async def handle_document_update(
    conn: DBConnection,
    user: User,
    data: DataDict,
    request_source: Source,
    signers_source: SignersSource,
) -> None:
    """
    Handle a full process of document update, including signers, reviewers, tags
    and document metadata
    """
    from app.documents.validators import validate_update_document

    options = await validate_update_document(
        conn=conn,
        user=user,
        raw_data=data,
        request_source=request_source,
        signers_source=signers_source,
    )

    update = DocumentUpdate(document=options.document)
    await update.perform_update(
        conn=conn,
        options=options,
        source=request_source,
    )


async def create_document_children(conn: DBConnection, ctx: AddDocumentChildrenCtx) -> set[str]:
    document_ids_to_reindex = set(ctx.children_ids)
    delete_data = []
    for parent_id, children_ids in ctx.old_links_mapping.items():
        document_ids_to_reindex.add(parent_id)
        for child_id in children_ids:
            document_ids_to_reindex.add(child_id)
            delete_data.append(
                {
                    'parent_id': parent_id,
                    'child_id': child_id,
                    'company_edrpou': ctx.company_edrpou,
                }
            )

    add_data = [
        {
            'parent_id': ctx.parent_id,
            'child_id': child_id,
            'company_edrpou': ctx.company_edrpou,
        }
        for child_id in ctx.children_ids
    ]

    async with conn.begin():
        await delete_documents_links(conn=conn, data=delete_data)
        await add_documents_links(conn=conn, data=add_data)
    return document_ids_to_reindex


async def create_document_child(conn: DBConnection, ctx: AddDocumentChildCtx) -> None:
    if ctx.link_exists:
        return

    async with conn.begin():
        # delete reversed link
        await delete_document_child(
            conn=conn,
            company_edrpou=ctx.company_edrpou,
            parent_id=ctx.child_id,
            child_id=ctx.parent_id,
        )

        await add_document_child(
            conn=conn,
            company_edrpou=ctx.company_edrpou,
            parent_id=ctx.parent_id,
            child_id=ctx.child_id,
        )


async def remove_document_child(conn: DBConnection, ctx: DeleteDocumentChildCtx) -> None:
    await delete_document_child(
        conn=conn,
        company_edrpou=ctx.company_edrpou,
        parent_id=ctx.parent_id,
        child_id=ctx.child_id,
    )


def _get_document_original_s3_key(*, document_id: str) -> str:
    """
    Get S3 of the document original file

    WARNING: probably you need get_document_s3_key instead of this function, which returns
    the current version of the document, not the original one.
    """
    return document_id


def get_document_s3_key(*, document_id: str, version_id: str | None) -> str:
    if version_id is not None:
        return get_document_version_key(version_id=version_id)

    # Fallback to the initial version of the document
    return _get_document_original_s3_key(document_id=document_id)


def get_asic_s3_key(document_id: str) -> str:
    return f'asic_{document_id}'


async def create_delete_request(request: web.Request, user: User, data: DataDict) -> list[DBRow]:
    # TODO: refactor this function
    from app.documents.validators import validate_create_delete_request

    async with request.app['db'].acquire() as conn:
        documents, message = await validate_create_delete_request(conn, user, data)
        if not documents:
            return []

        delete_requests, created_comments = await create_delete_doc_requests(
            conn,
            user,
            documents,
            message,
        )

        await send_comments_for_indexation(comments_ids=[c.id for c in created_comments])

        await send_document_delete_request_notifications(
            initiator_role_id=user.role_id,
            message=message,
            document_ids=[doc.id for doc in documents],
        )

        await document_actions.add_document_actions(
            document_actions=[
                document_actions.DocumentAction(
                    action=document_actions.Action.delete_request_create,
                    document_id=document.id,
                    document_edrpou_owner=document.edrpou_owner,
                    document_title=document.title,
                    company_id=user.company_id,
                    company_edrpou=user.company_edrpou,
                    email=user.email,
                    role_id=user.role_id,
                )
                for document in documents
            ]
        )

    return delete_requests


async def accept_delete_request_util(
    request: web.Request, user: User, data: AcceptDeleteReqData
) -> None:
    from app.documents.validators import validate_accept_delete_request

    app = request.app
    async with app['db'].acquire() as conn:
        delete_requests, doc_ids_to_delete = await validate_accept_delete_request(
            conn=conn, user=user, data=data, source=get_source(request)
        )

        logger.info(
            'Accept delete request data',
            extra={
                'delete_requests': [req.id for req in delete_requests],
                'docs_to_delete': doc_ids_to_delete,
                'user_email': user.email,
            },
        )

        async with conn.begin():
            await accept_delete_requests(conn, doc_ids_to_delete)
            documents_to_delete = [
                Document.from_row(row)
                for row in await select_documents_by_ids(conn, doc_ids_to_delete)
            ]
            documents_deleter = DocumentsDelete(documents=documents_to_delete)
            await documents_deleter.perform_main_transaction(conn)

        await document_actions.add_document_actions(
            document_actions=[
                document_actions.DocumentAction(
                    action=document_actions.Action.delete_request_accept,
                    document_id=document.id,
                    document_edrpou_owner=document.edrpou_owner,
                    document_title=document.title,
                    company_id=user.company_id,
                    company_edrpou=user.company_edrpou,
                    email=user.email,
                    role_id=user.role_id,
                )
                for document in documents_to_delete
            ]
        )

        await documents_deleter.perform_async_actions()
        await send_document_delete_request_accepted_emails(
            conn=conn,
            user=user,
            all_documents=documents_to_delete,  # type: ignore[arg-type]
            delete_requests=delete_requests,
        )


async def reject_delete_request_util(request: web.Request, user: User, data: DataDict) -> None:
    from app.documents.validators import validate_reject_delete_request

    async with request.app['db'].acquire() as conn:
        reject_message, delete_requests = await validate_reject_delete_request(
            conn, user, data, source=get_source(request)
        )
        document_ids = {del_req.document_id for del_req in delete_requests}

        delete_requests = await reject_delete_requests(
            conn,
            rejecter=user,
            document_ids=document_ids,
            reject_message=reject_message,
        )
        await send_reject_delete_request_emails(conn, user, delete_requests)


async def cancel_delete_request_util(request: web.Request, user: User, data: DataDict) -> None:
    from app.documents.db import cancel_delete_requests
    from app.documents.validators import validate_cancel_delete_request

    async with request.app['db'].acquire() as conn:
        delete_requests = await validate_cancel_delete_request(
            conn,
            user,
            data,
        )
        delete_requests_ids = [
            delete_request.id
            for delete_request in delete_requests
            if delete_request.status == DeleteRequestStatus.new
        ]
        await cancel_delete_requests(
            conn,
            delete_requests_ids,
        )


async def get_document_hash(
    user: AuthUser | User,
    document_id: str,
) -> str:
    """Get base64 encoded DSTU hash of given document by ID"""

    from api.downloads.types import FileOptions
    from api.downloads.utils import download_file_as_bytes
    from app.documents.validators import validate_get_document_hash

    async with services.db.acquire() as conn:
        # Validation
        document = await validate_get_document_hash(conn, user, document_id)

    async with services.db_readonly.acquire() as conn:
        document_meta = await select_document_meta(conn, document_id=document.id)

    if document_meta:
        return document_meta.content_hash

    # Preparing document to downloading from S3
    download_file = s3_utils.DownloadFile(
        key=get_document_s3_key(
            document_id=document.id,
            # TODO: Find out and document what we should do with versioned document here.
            #  Should we return hash of the last version or it's always should be original?
            version_id=None,
        ),
    )
    file_options = FileOptions(
        document=document,  # type: ignore[arg-type]
        download_file=download_file,
        raw_file_name=document.title,
        file_name=document.title,
    )
    document_bytes = await download_file_as_bytes(file_options)
    document_hash = await eusign_utils.generate_hash_base64(document_bytes)

    meta = {
        'document_id': document.id,
        'content_hash': document_hash,
        'content_length': len(document_bytes),
    }

    async with services.db.acquire() as conn:
        await add_documents_meta(conn, data=[meta])

    return document_hash


def calculate_total_visible_documents_count(
    *,
    max_documents_count: int | None,
    max_archive_documents_count: int | None,
) -> int | None:
    # Can view all documents
    if not max_documents_count:
        return None

    # Currently, archived documents only increase the visible document count.
    # Unlimited archived documents are not supported and should be counted as 0 additional
    # documents that user can view.
    return max_documents_count + (max_archive_documents_count or 0)


async def filter_documents_by_billing_config_restrictions(
    conn: DBConnection,
    *,
    company_edrpou: str,
    company_id: str,
    document_ids: list[str],
) -> BillingDocumentsLimitCtx | None:
    """
    Retrieve the ctx object, which contains documents filtered
        based on billing config restrictions.

    The primary criteria for filtering is document visibility, which depends on:
        - max_documents_count
        - max_archive_documents_count

    Only accessible documents are counted as available for viewing.
    """
    active_rates = await select_active_company_rates(conn=conn, company_id=company_id)
    web_rates = [r for r in active_rates if r.rate.is_web]

    has_unlimiled_documents_rate = any(r.has_unlimited_documents for r in web_rates)

    # TODO: remove this condition and flag after we will enable limits for other rates. In the
    # future, we should just check into billing config without selecting rates at all
    if has_unlimiled_documents_rate:
        return None

    config = await get_billing_company_config(conn, company_id=company_id)

    # There is no limit for visible documents for a config
    if not config.max_documents_count or not config.max_visible_documents_count:
        return None

    available_document_ids = await filter_visible_document_ids_for_user(
        conn=conn,
        company_edrpou=company_edrpou,
        document_ids=document_ids,
        visibility_limit=config.max_visible_documents_count,
    )

    return BillingDocumentsLimitCtx(
        documents_ids=available_document_ids,
        max_visible_documents_count=config.max_visible_documents_count,
        max_documents_count=config.max_documents_count,
        max_archive_documents_count=config.max_archive_documents_count,
    )


async def notify_when_document_view_limit(
    company_id: str,
    documents_count: int,
    max_documents_count: int,
    active_rates: list[CompanyRate],
) -> None:
    """
    Send notification to admins. If a certain limit is reached.
    But only once a month (it's random value, maybe it might be more or less)

    Pay attention that "documents_count" is never bigger than "max_documents_count" because
    we don't need to have the precise value of documents_count in the company, we just need to
    know when we reach the limit.

    DOC-6923 - We don't send a notification to the companies which have active archive rate
    """

    event: Event | None = None

    limit_percent = documents_count / max_documents_count
    # can be < 0
    hiddendocs = documents_count - max_documents_count

    # over limit docs by 146% / 130%
    if limit_percent > 1.45:
        event = Event.document_view_over_limit_146p
    elif limit_percent >= 1.3:
        event = Event.document_view_over_limit_130p
    elif limit_percent >= 1:
        event = Event.document_view_limit_full
    elif limit_percent >= 0.9:
        event = Event.document_view_limit_90p

    web_rates = [r for r in active_rates if r.rate.is_web]
    archive_rates = [r for r in active_rates if r.rate.is_archive]

    if (
        not event
        or not web_rates
        or archive_rates
        or any(r.has_unlimited_documents for r in web_rates)
    ):
        return

    # For each company and limit, we have separate cache key to guarantee that
    # after increasing or decreasing limit, we will send notification again after
    # reaching the limit. For example (<30 days):
    # 1) company=1, limit=10 -> send event
    # 2) company=1, limit=10 -> skip event
    # 3) ... skipping events with the same limit
    # 4) company=1, limit=25 -> send event
    # 5) company=1, limit=25 -> skip event
    key = f'events:{company_id}:document_view_limit:{max_documents_count}'

    # How long to store the key in cache in seconds (7 days)
    ttl = 7 * 24 * 3600

    # This event was already sent, so we don't need to send it again until
    # TTL expires or the limit changes
    sent_event = await services.redis.get(key)
    if sent_event is not None and sent_event == event.value:
        return

    # Find web rate with the maximum limit because we assume that this rate was used
    # to set the limit for the company during rate activation
    max_rate: CompanyRate | None = None
    max_rate_limit: int = -1
    for rate in web_rates:
        rate_limits = get_rate_config_limits(rate.rate, edrpou=rate.company_edrpou)
        rate_limit = rate_limits.get(CompanyLimit.documents)
        if rate_limit is not None and rate_limit > max_rate_limit:
            max_rate = rate
            max_rate_limit = rate_limit

    if not max_rate:
        # If we didn't find any rate with the limit, let's make a fallback to the last rate.
        # Maybe no correct one, but it's better than doing nothing
        logger.info(
            msg='No max rate found',
            extra={'company_id': company_id, 'rates': [r.rate for r in web_rates]},
        )
        max_rate = web_rates[-1]

    extra_params = {'rate': RATES_NAME_MAP[max_rate.rate]}
    if hiddendocs > 0:
        extra_params['hiddendocs'] = str(hiddendocs)

    await services.kafka.send_record(
        topic=topics.ESPUTNIK_SEND_EVENT_TO_USERS,
        value={
            'event': event,
            'company_id': company_id,
            'extra_params': extra_params,
        },
    )
    await services.redis.setex(key, value=event.value, time=ttl)


async def get_document_owner(conn: DBConnection, document: Document) -> User | None:
    """
    Select user with company info, that is document owner of the document. This function
    return nothing for case when document is uploaded anonymously or by vendor company
    (Sovtes or Agoroyard case)
    """
    role_id = document.role_id

    # Document is uploaded anonymously, so we can't find owner of document in database
    if not role_id:
        return None

    user = await auth.get_user(conn, role_id=role_id)
    if not user:
        return None

    # It case when vendor companies can upload documents on behalf of their companies
    # (Sovtes or Agoroyard case). See app.documents.db::document_table for details
    if user.company_edrpou != document.edrpou_owner:
        return None

    return user


async def reject_documents(conn: DBConnection, *, document_ids: list[str]) -> None:
    """
    Reject document and update has_changed_for_public_api property.
    """

    async with conn.begin():
        await db.update_documents(
            conn=conn,
            document_ids=document_ids,
            data={
                'status_id': DocumentStatus.reject.value,
                'date_finished': datetime.now(),
            },
        )
        await change_documents_for_public_api(conn, document_ids)


async def delete_recipients_document_resources(
    conn: DBConnection,
    *,
    document_id: str,
    companies_edrpous: list[str],
) -> DeleteRecipientsResources:
    """
    Delete resources created by previous recipients on document recipients change

    :param: companies_edrpous: list of companies that were recipients of a document,
    but now they are not recipients anymore and their resources should be deleted

    IMPORTANT!
      When calling this function, you need manually process its result.
      Remove resources listed in DeleteRecipientsResources.
    """
    from app.tags.utils import delete_company_document_tags

    result = DeleteRecipientsResources()

    if not companies_edrpous:
        return result

    await delete_company_document_tags(
        conn=conn,
        documents_ids=[document_id],
        companies_edrpous=companies_edrpous,
    )

    # Remove previous recipient's access
    listings_ctx = await delete_recipients_access(
        conn=conn,
        document_id=document_id,
        companies_edrpous=companies_edrpous,
    )

    # Delete comments from previous recipient
    comments_ids = await delete_comments(
        conn=conn,
        document_id=document_id,
        companies_edrpous=companies_edrpous,
    )

    # Delete document signers from previous recipient
    deleted_signers = await delete_document_signers(
        conn=conn,
        document_id=document_id,
        companies_edrpou=companies_edrpous,
    )

    # Currently we return only comments ids, but if you need to remove more resources
    # create separate dataclass for a result and return it
    return DeleteRecipientsResources(
        comments_ids=comments_ids,
        listings_ctx=listings_ctx,
        document_signers=deleted_signers,
    )


async def update_bilateral_recipient(
    conn: DBConnection,
    *,
    document_id: str,
    document_owner_edrpou: str,
    new_recipient_edrpou: str,
    new_recipient_emails: list[str],
    new_recipient_emails_hidden: bool,
    prev_recipients: list[DocumentRecipient],
) -> None:
    """
    Change bilateral recipient of the document
    """

    async with conn.begin():
        # remove and insert new recipients
        await db.replace_bilateral_recipients(
            conn=conn,
            document_id=document_id,
            recipient_edrpou=new_recipient_edrpou,
            recipient_emails=new_recipient_emails,
            is_recipient_emails_hidden=new_recipient_emails_hidden,
            document_owner_edrpou=document_owner_edrpou,
            prev_recipients=prev_recipients,
        )

        await change_documents_for_public_api(conn, document_ids=[document_id])


async def unsend_document_to_recipients(
    conn: DBConnection,
    *,
    document_id: str,
    companies_edrpous: list[str],
) -> DeleteRecipientsResources:
    """
    Redo the sending of the document to the recipient by removing sensitive data created
    by recipients (tags, comments, etc.) and setting the date_sent to None
    """

    await db.update_recipients_date_received_raw(
        conn=conn,
        document_id=document_id,
        companies_edrpous=companies_edrpous,
        value=None,
    )
    await db.update_recipients_date_sent_raw(
        conn=conn,
        document_id=document_id,
        companies_edrpous=companies_edrpous,
        value=None,
    )
    # todo: investigate why we don't reset date_delivered here?

    deleted_resources = await delete_recipients_document_resources(
        conn=conn,
        document_id=document_id,
        companies_edrpous=list(companies_edrpous),
    )
    return deleted_resources


async def send_bilateral_document_to_recipient(
    conn: DBConnection,
    *,
    document_id: str,
    recipient_edrpou: str,
    recipient_emails: list[str] | None,
    reset_date_delivered: bool = True,
) -> None:
    """
    Send a bilateral document to the recipient. Recipient should be already prepared and set on
    the previous steps. This function handles only opening access and properly marking that the
    document was sent to the recipient.

    NOTE: this function doesn't do anything related to sending notifications to the recipient or
    updating the document status because in different cases, like changing recipient or sending
    a blackbox uploaded document, we want to do it differently than during the regular sending.

    Related functions:
     - "send_bilateral_document_to_owner"
     - "send_bilateral_document_to_recipient"
     - "send_multilateral_document_to_recipients"
    """

    await create_document_access_for_recipient(
        conn=conn,
        document_id=document_id,
        emails=recipient_emails,
        edrpou=recipient_edrpou,
    )
    await update_recipients_date_sent(
        conn=conn,
        document_id=document_id,
        companies_edrpous=[recipient_edrpou],
    )

    # TODO: deprecate writing date delivery to the documents.date_delivery column
    if reset_date_delivered:
        await update_document_date_delivered_none(conn, document_id=document_id)


async def send_bilateral_document_to_owner(
    conn: DBConnection,
    *,
    document_id: str,
    document_owner_edrpou: str,
) -> None:
    """
    Send a bilateral document from recipient to owner (also known as 3p case)

    Unlike sending a bilateral document to the recipient, we don't need to open access to
    the owner because it already has access to the document as the owner

    Related functions:
     - "send_bilateral_document_to_owner"
     - "send_bilateral_document_to_recipient"
     - "send_multilateral_document_to_recipients"
    """
    await update_recipients_date_sent(
        conn=conn,
        document_id=document_id,
        companies_edrpous=[document_owner_edrpou],
    )


async def _should_autosend_document(conn: DBConnection, document: Document) -> bool:
    """
    Should we automatically send the document to the recipient or owner after new signature
    was added to the document?
    """

    # Internal documents don't require sending to recipients. Sending between coworkers
    # is handled by "add_signature" and "document_signers" logic.
    if document.is_internal:
        return False

    # Multilateral documents are sent to recipients when new signature is added. See
    # "add_sign_to_flow" function for more details
    if document.is_multilateral:
        return False

    # These two statuses indicate that the document is in the process of signing
    # (at least one signature has been added). And potentially it can be sent to the next
    # recipient if the document collects all the necessary signatures from the current side
    if document.status not in (DocumentStatus.signed, DocumentStatus.approved):
        return False

    signatures = await select_signatures(conn, document_ids=[document.id])
    document_signers = await select_document_signers_extended(conn, document_id=document.id)

    counter = get_document_signatures_counter(
        edrpou_owner=document.edrpou_owner,
        edrpou_recipient=document.edrpou_recipient,
        expected_owner_signatures=document.expected_owner_signatures,
        expected_recipient_signatures=document.expected_recipient_signatures,
        first_sign_by=document.first_sign_by,
        signatures=signatures,
        document_signers=document_signers,
    )

    # Detect on which side the document is (owner or recipient), by looking
    # if second signers started to sign the document
    is_owner_side: bool = False
    if document.first_sign_by == FirstSignBy.owner:
        is_owner_side = counter.recipient_count == 0
    elif document.first_sign_by == FirstSignBy.recipient:
        is_owner_side = counter.owner_count != 0

    # Now check that we have enough signatures form the current side of the document
    if is_owner_side:
        return counter.owner_count >= document.expected_owner_signatures

    # not is_owner_side:
    return counter.recipient_count >= document.expected_recipient_signatures


async def autosend_document_on_sign(
    conn: DBConnection,
    company_edrpou: str,
    user: User,
    document_id: str,
    request_source: Source,
) -> None:
    """
    Auto-send Document to Recipients After New Signature is Added

    The main purpose of this function is to prevent a situation where the document is signed but
    not sent to the recipient. Currently, signing and sending are two separate processes,
    and the client (web, mobile) must manually call the "send_document" endpoint to send the
    document to the recipient after signing. Since these two actions are separate, there is a
    possibility that the document may fail to be sent or might not be sent at all.

    This function addresses this issue by automatically sending the document to the recipient on
    the backend side after a new signature is added. Additionally, we are scheduling an
    asynchronous job that will retry auto-sending in case of a failure.
    """

    if not get_flag(FeatureFlags.AUTOSEND_DOCUMENTS):
        return

    log_extra = {
        'company_edrpou': company_edrpou,
        'role_id': user.role_id,
        'document_id': document_id,
        'request_source': request_source.value,
    }

    try:
        await _autosend_document(
            conn=conn,
            company_edrpou=company_edrpou,
            user=user,
            document_id=document_id,
            request_source=request_source,
        )
    except Error as error:
        # Ignore all handled errors because, in case of retrying, we will receive the same error
        # again and again.
        logger.info('Can not autosend document', extra={**log_extra, 'error': error.to_dict()})
        return

    except BaseException:
        logger.exception('Failed to autosend document', extra=log_extra)

        # Schedule an async job that will try to send the document to the recipient
        # in case if autosending above fail.

        # Make sure that the connection is open, because it due to the exception
        # such as CancelledError.
        async with ensure_connection_open(conn) as conn2:
            await services.kafka.add_task(
                conn=conn2,
                topic=topics.AUTOSEND_DOCUMENT,
                delay_min=5,
                data={
                    'company_edrpou': company_edrpou,
                    'role_id': user.role_id,
                    'document_id': document_id,
                    'request_source': request_source.value,
                },
            )
        # Do not raise an exception, because we don't want to interrupt the main flow
        # of the "add_signature" function
        return


async def _autosend_document(
    conn: DBConnection,
    company_edrpou: str,
    user: User,
    document_id: str,
    request_source: Source,
) -> None:
    """
    Send a document to recipients automatically if it is possible after new signature.

    NOTE: you probably don't need to use this function directly, use "autosend_document_on_sign"
    which will schedule an async job that retry autosending in case of failure

    Returns True if autosending is called, otherwise False
    """

    # Get a fresh instance of the document, because a document selected during validation before
    # adding a new signature can be outdated
    document = await get_expected_document(conn, document_id=document_id)
    should_send = await _should_autosend_document(conn, document=document)

    log_extra = {
        'document_id': document_id,
        'document_is_internal': document.is_internal,
        'document_is_multilateral': document.is_multilateral,
        'document_status': document.status.value,
        'document_first_sign_by': document.first_sign_by.value,
        'document_expected_owner_signatures': document.expected_owner_signatures,
        'document_expected_recipient_signatures': document.expected_recipient_signatures,
        'document_owner_edrpou': document.edrpou_owner,
        'document_recipient_edrpou': document.edrpou_recipient,
        'company_edrpou': company_edrpou,
        'role_id': user.role_id,
        'user_email': user.email,
        'request_source': request_source.value,
    }

    if not should_send:
        logger.info('Document is not ready for autosending', extra=log_extra)
        return

    logger.info('Auto-sending document', extra=log_extra)
    await send_document(
        conn=conn,
        company_edrpou=company_edrpou,
        user=user,
        raw_data={
            'document_id': document.id,
        },
        request_source=request_source,
    )


async def schedule_send_first_notification_to_signers_job(
    documents_ids: list[str],
    current_company_id: str,
    current_role_id: str,
) -> None:
    """
    Schedule a job that will send notifications to signers of the document
    """
    await services.kafka.send_record(
        topic=topics.SEND_FIRST_NOTIFICATION_TO_SIGNERS,
        value={
            'document_ids': documents_ids,
            'current_company_id': current_company_id,
            'current_role_id': current_role_id,
        },
    )


def get_bilateral_document_side(document: Document) -> DocumentBilateralSide:  # noqa: C901
    """
    Detect on which side is bilateral document (owner or recipient) by looking on status of
    the document and expected signatures from owner and recipient.

    See full diagram of the document statuses at Confluence:
     - https://tabula-rasa.atlassian.net/wiki/spaces/vchasno/pages/4952001 (Флоу документов)
    """
    assert document.is_bilateral, 'Document is not bilateral'

    # Basic variables to simplify reading the code
    owner_side: Literal['owner'] = 'owner'
    recipient_side: Literal['recipient'] = 'recipient'

    status = document.status
    first_sign_by = document.first_sign_by
    expected_owner_signatures = document.expected_owner_signatures
    expected_recipient_signatures = document.expected_recipient_signatures

    # This is initial statuses of the document, when it's just uploaded
    # and the recipient is not set yet
    if status in (DocumentStatus.uploaded, DocumentStatus.ready_to_be_signed):
        return DocumentBilateralSide(
            current_side=owner_side,
            is_received_by_recipient=False,
            is_finished_by_owner=False,
            is_finished_by_recipient=False,
        )

    if first_sign_by == FirstSignBy.owner:
        # It's owner-only status for first sign by owner (signatures from recipient are expected).
        # Document is still on owner side and never was on the recipient side
        if status == DocumentStatus.signed:
            return DocumentBilateralSide(
                current_side=owner_side,
                is_received_by_recipient=False,
                is_finished_by_owner=False,  # in the process of signing
                is_finished_by_recipient=False,  # waiting for the owner
            )

        # This status can use either owner or recipient side depending
        # on expected signatures from recipients. The document is still in the process of signing
        if status == DocumentStatus.approved and not expected_recipient_signatures:
            return DocumentBilateralSide(
                current_side=owner_side,
                is_received_by_recipient=False,
                is_finished_by_owner=False,  # in the process of signing
                is_finished_by_recipient=False,  # waiting for the owner
            )

        if status == DocumentStatus.approved and expected_recipient_signatures:
            return DocumentBilateralSide(
                current_side=recipient_side,
                is_received_by_recipient=True,
                is_finished_by_owner=True,
                is_finished_by_recipient=False,  # in the process of signing
            )

        if status == DocumentStatus.signed_and_sent:
            return DocumentBilateralSide(
                current_side=recipient_side,
                is_received_by_recipient=True,
                is_finished_by_owner=True,
                is_finished_by_recipient=False,  # expected to start signing
            )

        if status == DocumentStatus.reject:
            return DocumentBilateralSide(
                current_side=recipient_side,  # last side of the document
                is_received_by_recipient=True,
                is_finished_by_owner=True,
                is_finished_by_recipient=False,  # rejected by recipient
            )

        if status == DocumentStatus.finished:
            return DocumentBilateralSide(
                current_side=recipient_side,  # last side of the document
                is_received_by_recipient=True,
                is_finished_by_owner=True,
                is_finished_by_recipient=True,
            )

        raise ValueError(f'Unhandled status {status}')

    if first_sign_by == FirstSignBy.recipient:
        if status == DocumentStatus.sent:
            return DocumentBilateralSide(
                current_side=recipient_side,
                is_received_by_recipient=True,
                is_finished_by_owner=False,  # waiting for the recipient
                is_finished_by_recipient=False,  # expecting recipient to start signing
            )

        if status == DocumentStatus.reject:
            return DocumentBilateralSide(
                current_side=recipient_side,  # last side of the document
                is_received_by_recipient=True,
                is_finished_by_owner=False,  # rejected by recipient
                is_finished_by_recipient=False,
            )

        if status == DocumentStatus.finished:
            return DocumentBilateralSide(
                current_side=recipient_side,  # last side of the document
                is_received_by_recipient=True,
                is_finished_by_owner=True,
                is_finished_by_recipient=True,
            )

        # This status is recipient-only status for first sign by recipient and signatures from
        # an owner are expected
        if status == DocumentStatus.signed:
            return DocumentBilateralSide(
                current_side=recipient_side,
                is_received_by_recipient=True,
                is_finished_by_owner=False,  # waiting for the recipient
                is_finished_by_recipient=False,  # in the process of signing
            )

        if status == DocumentStatus.signed_and_sent:
            return DocumentBilateralSide(
                current_side=owner_side,
                is_received_by_recipient=True,
                is_finished_by_owner=False,  # waiting to start signing
                is_finished_by_recipient=True,
            )

        if status == DocumentStatus.approved and not expected_owner_signatures:
            return DocumentBilateralSide(
                current_side=recipient_side,
                is_received_by_recipient=True,
                is_finished_by_owner=False,  # waiting for the recipient
                is_finished_by_recipient=False,  # in the process of signing
            )

        if status == DocumentStatus.approved and expected_owner_signatures:
            return DocumentBilateralSide(
                current_side=owner_side,
                is_received_by_recipient=True,
                is_finished_by_owner=False,  # in the process of signing
                is_finished_by_recipient=True,
            )

        raise ValueError(f'Unhandled status {status}')

    assert_never(first_sign_by)


class DocumentRecipientsState:
    """
    Build a list of recipient items (unified model between "document_recipients" and "flows")
    for the document.

    You can use it when you need to convert from bilateral to multilateral, or from internal to
    bilateral, etc.
    """

    def __init__(
        self,
        document: Document,
        recipients: list[DocumentRecipient],
        flows: list[FlowItem],
        signatures: list[Signature],
        document_signers: list[DocumentSignerWithEdrpou],
    ) -> None:
        self._document = document
        self._recipients = recipients
        self._flows = flows
        self._signatures = signatures
        self._document_signers = document_signers

        self.recipients_items: list[DocumentRecipientStateItem] = []
        if self._document.is_internal:
            self.recipients_items = self.build_items_internal(
                document=document,
                signatures=signatures,
                document_signers=document_signers,
            )
        elif self._document.is_bilateral:
            self.recipients_items = self.build_items_bilateral(
                document=document,
                recipients=recipients,
                signatures=signatures,
                document_signers=document_signers,
            )
        elif self._document.is_multilateral:
            self.recipients_items = self.build_items_multilateral(
                recipients=recipients,
                flows=flows,
            )
        else:
            raise ValueError('Unknown document type')

        self.recipients_items = sorted(self.recipients_items, key=lambda item: item.sort_key)

    @property
    def is_bilateral(self) -> bool:
        return self._document.is_bilateral

    @staticmethod
    def build_items_internal(
        document: Document,
        signatures: list[Signature],
        document_signers: list[DocumentSignerWithEdrpou],
    ) -> list[DocumentRecipientStateItem]:
        """
        Build recipients state items for internal documents. Internal documents have only one
        recipient - the owner of the document. We always consider internal documents as sent to
        this one recipient (owner), because it's only and first recipient of the document.
        """

        signatures_count = get_signatures_count(
            signatures=signatures,
            target_edrpou=document.edrpou_owner,
            signers=document_signers,
        )

        expected_signatures: int = (
            document.expected_owner_signatures if not document_signers else len(document_signers)
        )

        pending_signatures: int = max(expected_signatures - signatures_count, 0)
        signed_roles_ids = [s.role_id for s in signatures if s.role_id]

        # If a document is finished then recipient is finished too
        is_finished = document.status.is_final
        return [
            DocumentRecipientStateItem(
                recipient_id=None,
                flow_id=None,
                document_id=document.id,
                edrpou=document.edrpou_owner,
                emails=[],
                is_emails_hidden=False,
                # internal considered as ordered to simplify the logic
                order=0,
                expected_signatures=document.expected_owner_signatures,
                pending_signatures=pending_signatures,
                # the date when a document was created is considered as the date
                # when we sent it to the recipient
                date_sent=document.date_created,
                date_received=document.date_created,
                date_delivered=document.date_created,
                date_created=document.date_created,
                date_updated=None,
                assigner_role_id=document.role_id,
                signed_roles_ids=signed_roles_ids,
                is_finished=is_finished,
                # such as an owner is uploading the document, then there is no
                # need to actually send jobs and notifications to the recipient (owner),
                # we just mark them as already sent
                send_jobs_executed=True,
                send_notifications_executed=True,
                # there is no such concept as external meta for internal documents
                external_meta=None,
            )
        ]

    @staticmethod
    def build_items_bilateral_for_owner(
        document: Document,
        signatures: list[Signature],
        recipients: list[DocumentRecipient],
        document_signers: list[DocumentSignerWithEdrpou],
    ) -> DocumentRecipientStateItem:
        """
        Build recipient state items for bilateral documents for the owner side.
        """
        edrpou_owner = document.edrpou_owner

        owner_signers = [s for s in document_signers if s.company_edrpou == edrpou_owner]
        owner_signatures = [s for s in signatures if s.owner_edrpou == edrpou_owner]
        recipient_signatures = [s for s in signatures if s.owner_edrpou != edrpou_owner]

        expected_signatures = document.expected_owner_signatures
        if owner_signers:
            expected_signatures = len(owner_signers)

        signatures_count = get_signatures_count(
            signatures=signatures,
            target_edrpou=edrpou_owner,
            signers=document_signers,
        )
        pending_signatures = max(expected_signatures - signatures_count, 0)
        signed_roles_ids = [s.role_id for s in owner_signatures if s.role_id]

        document_side = get_bilateral_document_side(document)
        is_finished = document_side.is_finished_by_owner

        # By default, we consider that owner signs the document first
        order = 0

        # This is a most straightforward case when we have an owner in the recipient table
        # And use most of the data from the recipient table
        owner_row = next((r for r in recipients if r.edrpou == document.edrpou_owner), None)
        if owner_row:
            date_send = owner_row.date_sent
            return DocumentRecipientStateItem(
                recipient_id=owner_row.id,
                flow_id=None,
                document_id=document.id,
                edrpou=owner_row.edrpou,
                emails=owner_row.emails or [],
                is_emails_hidden=owner_row.is_emails_hidden,
                order=order,  # can be updated later
                expected_signatures=expected_signatures,
                pending_signatures=pending_signatures,
                date_sent=date_send,
                date_received=owner_row.date_received,
                date_delivered=owner_row.date_delivered,
                date_created=owner_row.date_created,
                date_updated=owner_row.date_created,
                assigner_role_id=owner_row.assigner_role_id,
                signed_roles_ids=signed_roles_ids,
                is_finished=is_finished,
                send_jobs_executed=date_send is not None,
                send_notifications_executed=date_send is not None,
                external_meta=owner_row.external_meta,
            )

        date_sent: datetime | None = None
        if document.first_sign_by == FirstSignBy.owner:
            date_sent = document.date_created  # date when an owner uploaded the document

        elif document.first_sign_by == FirstSignBy.recipient:
            if document_side.is_finished_by_recipient:
                # We consider as date sent the date when the recipient finished signing.
                # It can be not the most accurate date, but it's better than having no date at all
                date_sent = max(s.date_created for s in recipient_signatures)
        else:
            assert_never(document.first_sign_by)

        # This is a case when we don't store an owner in recipient table. It usually happens
        # when the first sign is expected from the owner side (most of the cases) or when
        # the recipient is not set yet or the document is too old.
        return DocumentRecipientStateItem(
            recipient_id=None,
            flow_id=None,
            document_id=document.id,
            edrpou=document.edrpou_owner,
            emails=[],
            is_emails_hidden=False,
            order=order,  # can be updated later
            expected_signatures=expected_signatures,
            pending_signatures=pending_signatures,
            date_sent=date_sent,
            date_received=date_sent,
            date_delivered=date_sent,
            date_created=document.date_created,
            date_updated=document.date_created,
            assigner_role_id=None,
            signed_roles_ids=signed_roles_ids,
            is_finished=is_finished,
            send_jobs_executed=date_sent is not None,
            send_notifications_executed=date_sent is not None,
            external_meta=None,
        )

    @staticmethod
    def build_items_bilateral_for_recipient(
        document: Document,
        recipients: list[DocumentRecipient],
        signatures: list[Signature],
        document_signers: list[DocumentSignerWithEdrpou],
    ) -> DocumentRecipientStateItem | None:
        """
        Build recipient state items for bilateral documents for the recipient side.
        """

        order = 1  # by default, the recipient is the second signer

        recipient_row = next((r for r in recipients if r.edrpou != document.edrpou_owner), None)
        recipient_edrpou = recipient_row.edrpou if recipient_row else document.edrpou_recipient
        if not recipient_edrpou:
            return None

        recipient_signers = [s for s in document_signers if s.company_edrpou == recipient_edrpou]
        recipient_signatures = [s for s in signatures if s.owner_edrpou == recipient_edrpou]
        owner_signatures = [s for s in signatures if s.owner_edrpou != recipient_edrpou]

        expected_signatures = document.expected_recipient_signatures
        if recipient_signers:
            expected_signatures = len(recipient_signers)

        signatures_count = get_signatures_count(
            signatures=signatures,
            target_edrpou=recipient_edrpou,
            signers=document_signers,
        )
        pending_signatures = max(expected_signatures - signatures_count, 0)
        signers_roles_ids = [s.role_id for s in recipient_signatures if s.role_id]

        document_side = get_bilateral_document_side(document)
        is_finished = document_side.is_finished_by_recipient
        date_sent: datetime | None = None
        if recipient_row:
            date_sent = recipient_row.date_sent
            return DocumentRecipientStateItem(
                recipient_id=recipient_row.id,
                flow_id=None,
                document_id=document.id,
                edrpou=recipient_row.edrpou,
                emails=recipient_row.emails or [],
                is_emails_hidden=recipient_row.is_emails_hidden,
                order=order,  # can be updated later
                expected_signatures=expected_signatures,
                pending_signatures=pending_signatures,
                date_sent=date_sent,
                date_received=recipient_row.date_received,
                date_delivered=recipient_row.date_delivered,
                date_created=recipient_row.date_created,
                date_updated=recipient_row.date_created,
                assigner_role_id=recipient_row.assigner_role_id,
                signed_roles_ids=signers_roles_ids,
                is_finished=is_finished,
                send_jobs_executed=date_sent is not None,
                send_notifications_executed=date_sent is not None,
                external_meta=recipient_row.external_meta,
            )

        if document_side.is_received_by_recipient:
            # maybe not the most accurate dates, but it's better than having no date at all
            if document.first_sign_by == FirstSignBy.recipient:
                date_sent = document.date_created
            elif document.first_sign_by == FirstSignBy.owner:
                date_sent = max(s.date_created for s in owner_signatures)
            else:
                assert_never(document.first_sign_by)

        if document.edrpou_recipient:
            return DocumentRecipientStateItem(
                recipient_id=None,
                flow_id=None,
                document_id=document.id,
                edrpou=document.edrpou_recipient,
                emails=split_comma_separated_emails(document.email_recipient),
                is_emails_hidden=False,
                order=order,  # can be updated later
                expected_signatures=expected_signatures,
                pending_signatures=pending_signatures,
                date_sent=date_sent,
                date_received=date_sent,
                date_delivered=document.date_delivered,
                date_created=document.date_created,
                date_updated=document.date_created,
                assigner_role_id=None,
                signed_roles_ids=signers_roles_ids,
                is_finished=is_finished,
                send_jobs_executed=date_sent is not None,
                send_notifications_executed=date_sent is not None,
                external_meta=None,
            )

        return None

    @staticmethod
    def build_items_bilateral(
        document: Document,
        recipients: list[DocumentRecipient],
        signatures: list[Signature],
        document_signers: list[DocumentSignerWithEdrpou],
    ) -> list[DocumentRecipientStateItem]:
        """
        Build recipient state items for bilateral documents.

        Bilateral documents are the oldest type of documents that we have, therefore, it's a bit
        harder to create a state item for them. Bilateral documents usually have only one
        recipient (first sign by owner), two recipients (first sign by recipient), but also old
        documents can have zero recipients. As well as "bilateral" document can be in empty state,
        where recipients are not set yet.
        """

        # Empty case when recipients are not set yet
        if not recipients and document.edrpou_recipient is None:
            return []

        owner_item = DocumentRecipientsState.build_items_bilateral_for_owner(
            document=document,
            signatures=signatures,
            recipients=recipients,
            document_signers=document_signers,
        )
        recipient_item = DocumentRecipientsState.build_items_bilateral_for_recipient(
            document=document,
            recipients=recipients,
            signatures=signatures,
            document_signers=document_signers,
        )

        items = [owner_item]

        # In an empty state, it's possible that recipient is not set yet
        if recipient_item:
            items.append(recipient_item)

        if document.first_sign_by == FirstSignBy.recipient:
            items = list(reversed(items))
            # Update "order" field according to the order of recipients
            for i, item in enumerate(items):
                item.order = i

        return items

    @staticmethod
    def build_items_multilateral(
        recipients: list[DocumentRecipient],
        flows: list[FlowItem],
    ) -> list[DocumentRecipientStateItem]:
        """
        Build recipient state items for multilateral documents.
        """

        # Multilateral documents have multiple recipients
        flows_mapping = {flow.recipient_id: flow for flow in flows}

        items = []
        for recipient in recipients:
            flow = flows_mapping[recipient.id]  # there is 1:1 mapping between recipient and flow

            item = DocumentRecipientStateItem(
                recipient_id=recipient.id,
                flow_id=flow.id,
                document_id=recipient.document_id,
                edrpou=recipient.edrpou,
                emails=recipient.emails,
                is_emails_hidden=recipient.is_emails_hidden,
                order=flow.order,
                expected_signatures=flow.signatures_count,
                pending_signatures=flow.pending_signatures_count,
                date_sent=recipient.date_sent,
                date_received=recipient.date_received,
                date_delivered=recipient.date_delivered,
                date_created=recipient.date_created,
                date_updated=recipient.date_created,
                assigner_role_id=recipient.assigner_role_id,
                signed_roles_ids=flow.meta.role_ids,
                is_finished=flow.is_finished,
                send_jobs_executed=flow.meta.send_jobs_executed,
                send_notifications_executed=flow.meta.send_notifications_executed,
                external_meta=recipient.external_meta,
            )

            items.append(item)

        return items


async def convert_office_document_to_pdf(
    conn: DBConnection,
    ctx: ConvertOfficeDocumentToPDFCtx,
    user: User,
) -> None:
    """
    Download the latest version of the document, convert it to PDF format using Gotenberg service
    and add it as a new version of the document.
    """

    from api.downloads import utils as downloads_utils

    download_document = downloads_utils.get_download_document_from_db_document(ctx.document)
    content_office = await downloads_utils.download_document_content(
        conn=conn,
        document=download_document,
        version_id=ctx.latest_version.id,
    )

    content_pdf = await GotenbergClient.convert_office_file_to_pdf(
        content=content_office,
        extension=ctx.latest_version.extension,
        landscape=is_landscaped(ctx.latest_version.extension),
    )

    await document_versions_utils.add_convert_format_document_version(
        conn=conn,
        document=ctx.document,
        content=content_pdf,
        extension='.pdf',
        user=user,
        source=ctx.source,
        latest_version=ctx.latest_version,
    )


def get_document_status_text_internal(status: DocumentStatus) -> LazyI18nString | None:
    """
    Get document status text for internal documents
    """
    if status == DocumentStatus.uploaded:
        return _('Завантажений')
    if status == DocumentStatus.reject:
        return _('Відхилений')
    if status == DocumentStatus.ready_to_be_signed:
        return _('Готовий для підпису')
    if status == DocumentStatus.signed:
        return _('В процесі підписання вашою компанією')
    if status == DocumentStatus.finished:
        return _('Підписаний всіма')

    return None


def get_document_status_text_multilateral(status: DocumentStatus) -> LazyI18nString | None:
    """
    Get document status text for multilateral documents
    """
    if status == DocumentStatus.uploaded:
        return _('Завантажений')
    if status == DocumentStatus.flow:
        return _('В процесі підписання')
    if status == DocumentStatus.finished:
        return _('Підписаний всіма')
    if status == DocumentStatus.reject:
        return _('Відхилений')
    return None


def get_document_status_text_bilateral(  # noqa: C901
    expected_owner_signatures: int,
    expected_recipient_signatures: int,
    is_owner: bool,
    status: DocumentStatus,
    first_sign_by: FirstSignBy,
) -> LazyI18nString | None:
    """
    Get document status text for bilateral documents
    """
    is_recipient = not is_owner
    is_expected_owner_signatures = expected_owner_signatures > 0
    is_expected_recipient_signatures = expected_recipient_signatures > 0

    if status == DocumentStatus.histored:
        return _('Імпортований')

    if status == DocumentStatus.uploaded:
        return _('Завантажений')

    if status == DocumentStatus.ready_to_be_signed:
        return _('Готовий для підпису та надсилання')

    if status == DocumentStatus.sent:
        # Status "sent" is used only for "first sign by recipient" documents when owner sends
        # a document to recipient for the first time.
        if first_sign_by == FirstSignBy.recipient:
            if is_owner:
                return _('Надісланий на перший підпис контрагенту')
            if is_recipient:
                return _('Очікує підпису вашої компанії')

        return None

    if status == DocumentStatus.signed:
        # This status is used to show that the document is still in process of signing
        # by the first signer, where two sides should sign the document.
        if first_sign_by == FirstSignBy.owner:
            if is_owner:
                return _('В процесі підписання вашою компанією')
            if is_recipient:
                return _('Підписується контрагентом')

        if first_sign_by == FirstSignBy.recipient:
            if is_owner:
                return _('Підписується контрагентом')
            if is_recipient:
                return _('В процесі підписання вашою компанією')

        return None

    if status == DocumentStatus.signed_and_sent:
        # This status is used to show that document is signed by first side and sent to the second
        # side for signing (owner -> recipient or recipient -> owner)
        if first_sign_by == FirstSignBy.owner:
            if is_owner:
                return _('Очікує підпису контрагента')
            if is_recipient:
                return _('Очікують підпису вашої компанії')
        if first_sign_by == FirstSignBy.recipient:
            if is_owner:
                return _('Очікують підпису вашої компанії')
            if is_recipient:
                return _('Очікує підпису контрагента')
        return None

    if status == DocumentStatus.reject:
        return _('Відхилений')

    if status == DocumentStatus.approved:
        if first_sign_by == FirstSignBy.owner and is_expected_recipient_signatures:
            # document is on recipient side, in process of signing by recipient
            if is_owner:
                return _('Підписується контрагентом')
            if is_recipient:
                return _('В процесі підписання вашою компанією')
        if first_sign_by == FirstSignBy.owner and not is_expected_recipient_signatures:
            # document is on owner side, in process of signing by owner
            if is_owner:
                return _('В процесі підписання вашою компанією')
            if is_recipient:
                return _('Підписується контрагентом')
        if first_sign_by == FirstSignBy.recipient and is_expected_owner_signatures:
            # the document is on the owner side, in the process of signing by owner
            if is_owner:
                return _('В процесі підписання вашою компанією')
            if is_recipient:
                return _('Підписується контрагентом')

        if first_sign_by == FirstSignBy.recipient and not is_expected_owner_signatures:
            # the document is on recipient side, in process of signing by recipient
            if is_owner:
                return _('Підписується контрагентом')
            if is_recipient:
                return _('В процесі підписання вашою компанією')

        return None

    if status == DocumentStatus.finished:
        if is_owner and not is_expected_recipient_signatures:
            return _('Надісланий контрагенту')
        if is_owner and not is_expected_owner_signatures:
            return _('Отриманий вами')
        if is_recipient and not is_expected_recipient_signatures:
            return _('Отриманий вами')
        if is_recipient and not is_expected_owner_signatures:
            return _('Надісланий контрагенту')

        # Both sides are expected to sign the document, and both sides signed it
        return _('Підписаний всіма')

    if status == DocumentStatus.revoked:
        return _('Анульований')

    # Those statuses are not used in "bilateral" documents
    if (
        status == DocumentStatus.deleted
        or status == DocumentStatus.flow
        or status == DocumentStatus.seen
    ):
        return None

    assert_never(status)


def get_document_status_text(
    is_internal: bool,
    is_multilateral: bool,
    expected_owner_signatures: int,
    expected_recipient_signatures: int,
    is_owner: bool,
    status: DocumentStatus,
    first_sign_by: FirstSignBy,
) -> LazyI18nString:
    """
    Get document status based on the document status and the document type
    """
    status_text: LazyI18nString | None
    if is_internal:
        status_text = get_document_status_text_internal(status=status)
    elif is_multilateral:
        status_text = get_document_status_text_multilateral(status=status)
    else:
        status_text = get_document_status_text_bilateral(
            expected_owner_signatures=expected_owner_signatures,
            expected_recipient_signatures=expected_recipient_signatures,
            is_owner=is_owner,
            status=status,
            first_sign_by=first_sign_by,
        )

    return status_text or _('Невизначений статус')


async def get_roles_to_remove_on_group_remove(
    conn: DBConnection,
    document: Document,
    settings: UpdateViewersData,
) -> list[str]:
    """
    Get roles that should be removed if groups are removed from the document.

    We should remove roles
    ONLY if there are no granted access via another group that won't be removed.
    """

    groups_accesses = await select_group_document_accesses(
        conn=conn,
        document_ids=[document.id],
    )
    remaining_group_ids = set()
    for group_access in groups_accesses:
        if group_access.group_id not in settings.group_ids:
            remaining_group_ids.add(group_access.group_id)

    remaining_groups_role_ids = await get_group_members_by_group_ids(
        conn=conn,
        group_ids=list(remaining_group_ids),
    )

    remaining_role_ids = set()
    for members in remaining_groups_role_ids.values():
        remaining_role_ids.update(members)

    remove_access_for_roles = set()
    for role_id in settings.group_viewer_role_ids:
        if role_id not in remaining_role_ids:
            remove_access_for_roles.add(role_id)

    return list(remove_access_for_roles)


async def update_recipients_date_sent(
    conn: DBConnection,
    *,
    document_id: str,
    companies_edrpous: list[str],
) -> None:
    """
    Mark document as "sent" for given companies in document_recipients table
    """

    await db.update_recipients_date_sent_raw(
        conn=conn,
        document_id=document_id,
        companies_edrpous=companies_edrpous,
        value=sa.text('now()'),
    )

    # Such as "date_sent" is closely related to "date_received" field, we update both of them
    # to the same value if the recipient company is registered in the system
    registered_edrpous = await select_registered_companies_edrpous(
        conn=conn,
        companies_edrpous=companies_edrpous,
    )
    if registered_edrpous:
        await db.update_recipients_date_received_raw(
            conn=conn,
            document_id=document_id,
            companies_edrpous=list(registered_edrpous),
            value=sa.text('now()'),
        )
