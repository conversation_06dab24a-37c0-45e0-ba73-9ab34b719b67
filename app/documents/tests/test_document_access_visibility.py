from http import HTTPStatus

import uj<PERSON>
from aiohttp import FormData

from app.documents.enums import DocumentAccessLevel
from app.flags import FeatureFlags
from app.tests.common import (
    API_V2_DOCUMENTS_URL,
    GRAPHQL_URL,
    graphql_response,
    prepare_auth_headers,
    prepare_client,
    prepare_user_data,
    with_elastic,
)

TEST_EMAIL_1 = '<EMAIL>'
TEST_EMAIL_2 = '<EMAIL>'
TEST_COMPANY_EDRPOU = '12345678'


async def check_graphql(client, headers, document_id, expected_access_level: DocumentAccessLevel):
    """
    Check document access level through GraphQL API
    and verify document presence in filtered list
    """
    response = await client.post(
        GRAPHQL_URL,
        data=ujson.dumps({'query': (f'{{ document(id: "{document_id}") {{ id accessLevel }} }}')}),
        headers=headers,
    )

    data = await graphql_response(response)
    assert data['document']['accessLevel'] == expected_access_level.value

    response = await client.post(
        GRAPHQL_URL,
        data=ujson.dumps({'query': '{ allDocuments { documents { id } } }'}),
        headers=headers,
    )

    data = await graphql_response(response)
    document_ids = [doc['id'] for doc in data['allDocuments']['documents']]
    assert document_id in document_ids, f'Document {document_id} not found in filtered list'


async def test_private_document_visibility_with_listing_date_flag(aiohttp_client, test_flags):
    """
    Test that private documents are visible in the table when:
    Given:
    - ENABLE_LISTING_DATE_FROM_ES flag is enabled
    - User has access to private documents but not extended documents
    - Documents are uploaded with different access levels
    When:
    - User2 uploads a private document (same company)
    - User3 uploads a private document (different company)
    - User2 uploads an extended document (same company)
    Then:
    - Only private document from the same company is visible
    - Extended document is not visible (can_view_document=False)
    - Private document from different company is not visible
    """
    # Enable the flag
    test_flags[FeatureFlags.ENABLE_LISTING_DATE_FROM_ES.name] = False

    app, client, user1 = await prepare_client(
        aiohttp_client=aiohttp_client,
        email=TEST_EMAIL_1,
        company_edrpou=TEST_COMPANY_EDRPOU,
        can_view_private_document=True,
        can_view_document=False,
    )

    user2 = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_2,
        company_edrpou=TEST_COMPANY_EDRPOU,
    )

    data = FormData()
    data.add_field('file', b'content', filename='private_doc.txt')
    params = {
        'access_settings_level': DocumentAccessLevel.private.value,
    }

    response = await client.post(
        API_V2_DOCUMENTS_URL,
        headers=prepare_auth_headers(user2),
        params=params,
        data=data,
    )
    assert response.status == HTTPStatus.CREATED
    upload_data = await response.json()
    document_id = upload_data['documents'][0]['id']

    user3 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou='87654321',
    )

    data2 = FormData()
    data2.add_field('file', b'content2', filename='private_doc2.txt')
    params2 = {
        'access_settings_level': DocumentAccessLevel.private.value,
    }

    response2 = await client.post(
        API_V2_DOCUMENTS_URL,
        headers=prepare_auth_headers(user3),
        params=params2,
        data=data2,
    )
    assert response2.status == HTTPStatus.CREATED
    upload_data2 = await response2.json()
    document_id2 = upload_data2['documents'][0]['id']

    data3 = FormData()
    data3.add_field('file', b'content3', filename='extended_doc.txt')
    params3 = {
        'access_settings_level': DocumentAccessLevel.extended.value,
    }

    response3 = await client.post(
        API_V2_DOCUMENTS_URL,
        headers=prepare_auth_headers(user2),
        params=params3,
        data=data3,
    )
    assert response3.status == HTTPStatus.CREATED
    upload_data3 = await response3.json()
    document_id3 = upload_data3['documents'][0]['id']

    # Create a document that user1 should NOT have access to
    # This will be an extended document from a different company
    user4 = await prepare_user_data(
        app=app,
        email='<EMAIL>',
        company_edrpou='99999999',  # Different company
    )

    data4 = FormData()
    data4.add_field('file', b'content4', filename='no_access_doc.txt')
    params4 = {
        'access_settings_level': DocumentAccessLevel.extended.value,
    }

    response4 = await client.post(
        API_V2_DOCUMENTS_URL,
        headers=prepare_auth_headers(user4),
        params=params4,
        data=data4,
    )
    assert response4.status == HTTPStatus.CREATED
    upload_data4 = await response4.json()
    document_id4 = upload_data4['documents'][0]['id']

    async with with_elastic(app, [document_id, document_id2, document_id3, document_id4]):
        response = await client.get(
            API_V2_DOCUMENTS_URL,
            headers=prepare_auth_headers(user1),
        )
        assert response.status == HTTPStatus.OK
        data = await response.json()
        documents = data['documents']

        # Check that user1 only sees the private document from the same company
        assert len(documents) == 1
        assert documents[0]['id'] == document_id

        # Verify that document_id4 (from different company) is NOT in the results
        document_ids_in_response = [doc['id'] for doc in documents]
        assert document_id4 not in document_ids_in_response, (
            f'Document {document_id4} should not be accessible to user1'
        )

        await check_graphql(
            client=client,
            headers=prepare_auth_headers(user1),
            document_id=document_id,
            expected_access_level=DocumentAccessLevel.private,
        )
