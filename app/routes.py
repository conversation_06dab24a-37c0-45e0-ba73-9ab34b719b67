from collections.abc import Awaitable, Callable
from contextlib import suppress

from aiohttp import web

import app.events.views as events_views
from api.debug import handlers as debug_handlers
from api.downloads import views as downloads
from api.graph import views as graph
from api.private.blackbox import views as blackbox_views
from api.private.edi import handlers as edi
from api.private.integrations import handlers as private_integrations
from api.private.integrations.hrs import handlers as private_hrs_integration
from api.private.super_admin import views as private_api_views
from api.private.super_admin.tmp_csp import views as tmp_csp_views
from api.public import decorators as public_api_decorators
from api.public import views as public_api_views
from api.shared import views as shared_views
from app.actions import views as actions
from app.analytics import views as analytics
from app.archive.views import archive_documents, unarchive_documents, upload_histored_document
from app.auth import views as auth
from app.auth.decorators import (
    base_login_required,
    login_required,
    redirect_to_app,
    sign_session_base_login_required,
    sign_session_login_required,
)
from app.auth.phone_auth import views as phone_auth
from app.auth.views import update_company_secure_permissions
from app.banner import views as banner_views
from app.billing import sa_views as sa_billing
from app.billing import views as billing
from app.cloud_signer import views as cloud_signer
from app.comments import views as comments
from app.config import get_level
from app.contacts import views as contacts
from app.crm import views as crm_views
from app.csat import views as csat_views
from app.diia import handlers as diia
from app.directories import views as directories
from app.document_automation import views as document_automation_views
from app.document_categories import views as document_categories_views
from app.document_revoke.views import (
    create_document_revoke,
    diia_request_document_revoke,
    get_xml_document_content,
    last_internal_signature_document_revoke,
    reject_document_revoke,
    sign_document_revoke,
)
from app.document_versions.views import (
    delete_versioned,
    upload_versioned,
)
from app.documents import views as documents
from app.documents import views as documents_views
from app.documents_ai import views as documents_ai
from app.documents_ai.structured_data_poc.views import extract_structured_data_from_document
from app.documents_fields import views as documents_fields
from app.documents_required_fields import views as documents_required_fields
from app.drafts.views import (
    convert_draft_to_document,
    convert_draft_to_new_version,
    create_draft_create,
    create_draft_from_template,
    create_draft_from_versioned,
    delete_draft,
    get_draft_content,
)
from app.editor import views as editor
from app.es import handlers as elastic
from app.feedbacks import views as feedbacks
from app.flags import views as flags_views
from app.flow import views as flow
from app.groups.views import (
    add_group_handler,
    add_group_member_handler,
    delete_group_handler,
    delete_group_member_handler,
    get_group_handler,
    get_group_members_handler,
    get_groups_handler,
    update_group_handler,
)
from app.landing import views as landing
from app.lib import cors
from app.lib.enums import AppLevel
from app.lib.redirects import redirect_route
from app.lib.static import app_page_handler, favicon, static_route
from app.lib.types import UserHandler
from app.mobile import views as mobile_views
from app.mobile.auth.decorators import mobile_base_login_required, mobile_login_required
from app.notifications import views as notifications
from app.openapi.utils import OpenApiDocs
from app.profile import views as profile
from app.proxy import views as proxy
from app.registration import sa_views as sa_registration
from app.registration import views as registration
from app.reviews import views as reviews
from app.services import services
from app.sign_sessions import views as sign_sessions
from app.signatures import views as signatures
from app.signatures.views import kep_graphql_proxy, kep_user_certificates
from app.system import handlers as system
from app.tags import views as tags
from app.telegram import dispatcher as telegram
from app.templates import views as templates_views
from app.tokens import views as tokens
from app.trigger_notifications import views as trigger_notifications
from app.uploads import views as uploads

VERSION_V1_V2 = '{version:v1|v2}'

type RawHandler = Callable[[web.Request], Awaitable[web.StreamResponse]]
type Handler = RawHandler | UserHandler


class AddRouteFunc:
    """

    Usage:
    ```python
    add_route = add_route_factory(app)
    add_route(
        method='GET',
        path='/api/v1/documents/{document_id}',
        handler=public_api_views.retrieve_document,
    )
    ```

    """

    def __init__(
        self,
        app: web.Application,
        *,
        check_prefix: str | None = None,
        add_options: bool = False,
        docs: OpenApiDocs | None = None,
    ) -> None:
        self.app = app
        self.check_prefix = check_prefix
        self.add_options = add_options
        self.docs = docs

    def __call__(
        self,
        method: str,
        path: str,
        handler: RawHandler,
        *,
        name: str | None = None,
        add_options: bool = False,
    ) -> None:
        """
        Wrapper on top of base add route func to supply proper URLs for API
        view functions.
        """

        # Check prefix before adding route
        if self.check_prefix is not None and not path.startswith(self.check_prefix):
            raise ValueError(
                f'Route path "{path}" does not start with "{self.check_prefix}"',
            )

        self.app.router.add_route(
            method=method,
            path=path,
            handler=handler,
            name=name,
        )
        if self.add_options or add_options:
            # RuntimeError: Added route will never be executed, method OPTIONS is already registered
            # This might happen if the same path, but different method is registered with
            # add_options=True
            with suppress(RuntimeError):
                self.app.router.add_route(
                    method='OPTIONS',
                    path=path,
                    handler=cors.options_handler,
                )

        if self.docs:
            self.docs.add_route(
                method=method,
                path=path,
                handler=handler,
                name=name,
            )


def add_route_factory(
    app: web.Application,
    *,
    check_prefix: str | None = None,
    add_options: bool = False,
    docs: OpenApiDocs | None = None,
) -> AddRouteFunc:
    """Return function to add route to app."""

    return AddRouteFunc(
        app=app,
        check_prefix=check_prefix,
        add_options=add_options,
        docs=docs,
    )


def setup_routes(app: web.Application) -> None:
    """
    Setup routes for a given app.

    WARNING!
    Be careful with setup routes with CPU-bound tasks, that kind of routes
    must be handled with http-server-second servers.
    Actual rules at:
    https://gitlab.vchasno.com.ua/devops/argocd-meta-app/-/blob/master/production-aws/infrastructure/istio-virtualservices/edo-prd-vu-virtualservice.yaml

    Example task:
    https://vchasno-group.atlassian.net/browse/VCDO-789
    """

    setup_static_routes(app)
    setup_static_pages(app)
    setup_error_pages(app)
    setup_confirmation_routes(app)
    setup_landing_routes(app)
    setup_sign_session_routes(app)
    setup_shared_view_routes(app)
    setup_download_routes(app)
    setup_telegram_routes(app)
    setup_auth_api(app)
    setup_blackbox_api(app)
    setup_edi_api(app)
    setup_integrations_api(app)
    setup_super_admin_api(app)
    setup_internal_api(app)
    setup_api(app)
    setup_system_routes(app)
    setup_mobile_routes(app)
    setup_analytics_routes(app)
    setup_editor(app)
    setup_debug_api(app)


def setup_system_routes(app: web.Application) -> None:
    """
    Setup routes for system handlers.
    """

    add_route = add_route_factory(app)

    # Routes for k8s probes
    add_route('GET', '/~liveness', system.simple_healthcheck)
    add_route('GET', '/~readiness', system.simple_healthcheck)

    # Routes for external availability check (by 3-party services)
    add_route('GET', '/~health/check', system.health_check)
    add_route('GET', '/~health/status', system.health_status)
    add_route(
        'GET',
        '/~availability/simple',
        system.health_check,
        name='availability-simple',
    )

    # CSP and static
    add_route('GET', '/~static-hash', system.static_hash)
    add_route('GET', '/~csp-report', system.csp_report, name='csp-report')
    add_route('POST', '/~csp-report', system.csp_report, name='csp-report')

    # sentry for frontend
    add_route('POST', '/~frontend-logs/', system.sentry_frontend_logs)

    # well-known
    add_route(
        'GET',
        '/.well-known/microsoft-identity-association.json',
        system.get_well_known_ms_id,
    )
    add_route(
        method='GET',
        path='/.well-known/assetlinks.json',
        handler=system.get_well_known_asset_links,
    )
    add_route(
        method='GET',
        path='/.well-known/apple-app-site-association',
        handler=system.get_well_known_apple_app_site_association,
    )

    add_route(
        method='GET',
        path='/.well-known/security.txt',
        handler=system.get_security_txt,
    )


def setup_static_routes(app: web.Application) -> None:
    """Setup routes to static files."""
    app.router.add_static('/static', 'static', name='static')
    app.router.add_route('GET', '/favicon.ico', favicon)
    app.router.add_static('/swagger', 'app/openapi/swagger-ui', name='static-swagger')


def setup_api(app: web.Application) -> None:
    """Setup routes for internal (public/vendor) API."""
    add_route = add_route_factory(app, check_prefix='/api/', add_options=True)

    # Healthcheck for api services
    add_route('GET', '/api/~healthcheck', system.health_check)

    add_route('POST', '/api/v2/diia', diia.diia_action)
    add_route('GET', '/api/v2/diia/{request_id}', diia.get_request_action)

    # Billing
    add_route(
        'POST',
        '/api/v2/billing/companies/rates/trials',
        billing.add_trial_company_rate_api,
    )

    # Public GraphQL API
    add_route('POST', '/api/graphql', public_api_views.graphql)

    # Tags
    add_route(
        method='GET',
        path='/api/v2/tags',
        handler=tags.get_tags,
    )
    add_route(
        method='GET',
        path='/api/v2/tags/{tag_id}/roles',
        handler=tags.get_tag_roles,
    )
    add_route(
        'POST',
        '/api/v2/tags/documents',
        public_api_decorators.api_handler(tags.create_tags_for_documents),
    )
    add_route(
        'POST',
        '/api/v2/tags/documents/connections',
        public_api_decorators.api_handler(tags.connect_tags_and_documents),
    )
    add_route(
        'DELETE',
        '/api/v2/tags/documents/connections',
        public_api_decorators.api_handler(tags.disconnect_documents_and_tags),
    )

    add_route(
        'POST',
        '/api/v2/tags/roles',
        public_api_decorators.api_handler(tags.create_tags_for_roles),
    )
    add_route(
        'POST',
        '/api/v2/tags/roles/connections',
        public_api_decorators.api_handler(tags.connect_tags_and_roles),
    )
    add_route(
        'DELETE',
        '/api/v2/tags/roles/connections',
        public_api_decorators.api_handler(tags.disconnect_tags_and_roles),
    )

    # Comments
    add_route(
        'POST',
        '/api/v2/documents/{document_id}/comments',
        public_api_views.create_comment,
    )
    add_route(
        'GET',
        '/api/v2/documents/{document_id}/comments',
        public_api_decorators.api_handler(public_api_views.list_document_comments),
    )
    add_route('GET', '/api/v2/documents/comments', comments.list_comments)

    # Documents
    add_route('POST', f'/api/{VERSION_V1_V2}/documents', public_api_views.upload_documents)
    add_route('GET', f'/api/{VERSION_V1_V2}/documents', public_api_views.list_owner_documents)
    add_route(
        'POST',
        '/api/v2/documents/mark-as-processed',
        public_api_views.mark_as_processed,
    )
    add_route(
        'DELETE',
        '/api/v2/documents/{document_id}/delete-requests',
        public_api_views.cancel_delete_requests,
    )
    add_route(
        'POST',
        '/api/v2/documents/{document_id}/delete-requests',
        public_api_views.create_delete_request_handler,
    )
    add_route(
        'POST',
        '/api/v2/documents/{document_id}/delete-requests/acceptions',
        public_api_views.accept_delete_request_api,
    )
    add_route(
        'POST',
        '/api/v2/documents/{document_id}/delete-requests/rejections',
        public_api_views.reject_delete_request,
    )
    add_route(
        'GET',
        '/api/v2/documents/delete-requests',
        public_api_views.get_delete_requests_by_company,
    )
    add_route(
        'POST',
        '/api/v2/documents/delete-requests/lock-delete',
        public_api_views.lock_delete_request,
    )
    add_route(
        'DELETE',
        '/api/v2/documents/delete-requests/lock-delete',
        public_api_views.unlock_delete_request,
    )

    add_route(
        'POST',
        '/api/v2/documents/archive',
        public_api_decorators.api_handler(archive_documents),
    )
    add_route(
        'DELETE',
        '/api/v2/documents/archive',
        public_api_decorators.api_handler(unarchive_documents),
    )

    add_route(
        'GET',
        f'/api/{VERSION_V1_V2}/documents/{{document_id}}',
        public_api_views.retrieve_document,
    )
    add_route(
        'DELETE',
        f'/api/{VERSION_V1_V2}/documents/{{document_id}}',
        public_api_views.delete_document,
    )
    add_route(
        'POST',
        '/api/v2/documents/{document_id}/signers',
        public_api_views.set_signers,
    )
    add_route(
        method='PATCH',
        path='/api/v2/documents/{document_id}/info',
        handler=public_api_views.update_document_info,
    )
    add_route(
        method='PATCH',
        path='/api/v2/documents/{document_id}/access-settings',
        handler=public_api_views.update_document_access_settings,
    )
    add_route(
        'GET',
        f'/api/{VERSION_V1_V2}/documents/{{document_id}}/fields',
        public_api_views.get_document_additional_fields,
        name='public_api.get_document_additional_fields',
    )
    add_route(
        'POST',
        f'/api/{VERSION_V1_V2}/documents/{{document_id}}/fields',
        public_api_views.add_additional_fields_to_document,
        name='public_api.add_additional_fields_to_document',
    )
    add_route(
        'GET',
        f'/api/{VERSION_V1_V2}/documents/{{document_id}}/archive',
        public_api_decorators.api_handler(downloads.archive),
        name='public_api.retrieve_document_archive',
    )
    add_route(
        'GET',
        f'/api/{VERSION_V1_V2}/documents/{{document_id}}/p7s',
        public_api_decorators.api_handler(downloads.p7s_container),
        name='public_api.retrieve_document_p7s',
    )
    add_route(
        'GET',
        f'/api/{VERSION_V1_V2}/documents/{{document_id}}/asic',
        public_api_decorators.api_handler(downloads.asic),
        name='public_api.retrieve_document_asic_container',
    )
    add_route(
        'GET',
        f'/api/{VERSION_V1_V2}/documents/{{document_id}}/original',
        public_api_decorators.api_handler(downloads.original),
        name='public_api.retrieve_document_original',
    )
    add_route(
        'GET',
        f'/api/{VERSION_V1_V2}/documents/{{document_id}}/pdf/print',
        public_api_decorators.api_handler(downloads.viewer_print),
    )
    add_route(
        'POST',
        '/api/v2/documents/{document_id}/xml-to-pdf',
        public_api_decorators.api_handler(downloads.create_xml_to_pdf),
    )
    add_route(
        'GET',
        '/api/v2/documents/{document_id}/xml-to-pdf',
        public_api_decorators.api_handler(downloads.xml_to_pdf),
        name='public_api.retrieve_document_xml_to_pdf',
    )
    add_route(
        'POST',
        '/api/v2/documents/{document_id}/reject',
        public_api_decorators.api_handler(documents_views.reject),
    )
    add_route(
        'POST',
        f'/api/{VERSION_V1_V2}/documents/{{document_id}}/send',
        public_api_decorators.api_handler(documents.send_document),
    )
    add_route(
        'POST',
        '/api/v2/documents/{document_id}/signatures',
        public_api_decorators.api_handler(public_api_views.add_signature),
    )
    add_route(
        'GET',
        '/api/v2/documents/{document_id}/signatures',
        public_api_views.retrieve_signatures,
    )
    add_route(
        'GET',
        '/api/v2/documents/{document_id}/flows',
        public_api_views.retrieve_flows,
    )

    # Reviews
    add_route(
        'GET',
        '/api/v2/documents/{document_id}/reviews',
        public_api_views.retrieve_reviews,
    )
    add_route(
        'GET',
        '/api/v2/documents/{document_id}/reviews/status',
        public_api_views.retrieve_review_status,
    )
    add_route(
        'GET',
        '/api/v2/documents/{document_id}/reviews/requests',
        public_api_views.retrieve_review_requests,
    )
    add_route(
        'POST',
        '/api/v2/documents/{document_id}/reviews/requests',
        public_api_views.add_review_request_api_handler,
    )
    add_route(
        'DELETE',
        '/api/v2/documents/{document_id}/reviews/requests',
        public_api_views.delete_review_request_api_handler,
    )

    # Flow
    add_route('POST', '/api/v2/documents/{doc_id}/flow', public_api_views.add_flow)

    # Download Documents
    add_route(
        'GET',
        '/api/v2/download-documents',
        public_api_views.list_download_documents,
    )

    # Incoming Documents
    add_route(
        'GET',
        f'/api/{VERSION_V1_V2}/incoming-documents',
        public_api_views.list_incoming_documents,
    )

    # Child Documents
    add_route(
        'POST',
        '/api/v2/documents/{document_id}/child/{child_id}',
        public_api_decorators.api_handler(documents.add_child),
    )
    add_route(
        'DELETE',
        '/api/v2/documents/{document_id}/child/{child_id}',
        public_api_decorators.api_handler(documents.delete_child),
    )

    # Logging
    add_route('POST', '/api/v2/logging', public_api_views.create_log)

    # Recipients
    add_route('GET', '/api/v1/recipients/{edrpou}', public_api_views.retrieve_recipient)

    # Roles
    add_route('GET', '/api/v2/roles', public_api_views.list_roles)
    add_route(
        'PATCH',
        '/api/v2/roles/{role_id}',
        public_api_decorators.api_handler(profile.update_role),
    )
    add_route(
        'DELETE',
        '/api/v2/roles/{role_id}',
        public_api_decorators.api_handler(profile.delete_role),
    )

    # Sign Sessions
    add_route(
        'POST',
        f'/api/{VERSION_V1_V2}/sign-sessions',
        public_api_decorators.api_handler(sign_sessions.create_sign_session),
    )
    add_route(
        'GET',
        f'/api/{VERSION_V1_V2}/sign-sessions/{{sign_session_id}}',
        public_api_decorators.api_handler(sign_sessions.retrieve_sign_session),
    )

    # Tokens
    add_route('POST', '/api/v1/tokens', public_api_views.create_token)
    add_route('POST', '/api/v2/tokens', auth.add_tokens)
    add_route('DELETE', '/api/v2/tokens', auth.delete_tokens)

    add_route('POST', '/api/v2/recreate-token', auth.recreate_token)

    # Users
    add_route('POST', '/api/v2/coworker', public_api_views.create_coworker)
    add_route('POST', '/api/v2/invite/coworkers', public_api_views.invite_coworkers)

    add_route(
        'PATCH',
        '/api/v2/companies/{edrpou}',
        public_api_views.update_company,
    )

    add_route(
        'GET',
        '/api/v2/flags/{flag}',
        public_api_views.check_flag,
    )

    add_route(
        'POST',
        '/api/v2/sync-listing',
        public_api_views.sync_listing,
    )

    # document metadata
    add_route(
        'POST',
        '/api/v2/fields',
        public_api_decorators._api_login_required(documents_fields.create),
    )
    add_route(
        'POST',
        '/api/v2/fields/accesses',
        public_api_decorators._api_login_required(documents_fields.create_access),
    )
    add_route(
        'DELETE',
        '/api/v2/fields/accesses',
        public_api_decorators._api_login_required(documents_fields.delete_access),
    )
    add_route(
        'GET',
        '/api/v2/fields',
        public_api_decorators.api_handler(public_api_views.get_fields_by_company_id),
    )

    # cloud-signer proxy
    add_route(
        'POST',
        '/api/v2/cloud-signer/sessions/create',
        cloud_signer.cloud_signer_sessions_create,
    )

    add_route(
        'POST',
        '/api/v2/cloud-signer/sessions/refresh',
        cloud_signer.cloud_signer_sessions_refresh,
    )

    add_route(
        'POST',
        '/api/v2/cloud-signer/sessions/refresh/check',
        cloud_signer.cloud_signer_sessions_refresh_check,
    )

    add_route(
        'POST',
        '/api/v2/cloud-signer/sessions/check',
        cloud_signer.cloud_signer_sessions_check,
    )

    add_route(
        'POST',
        '/api/v2/cloud-signer/sessions/sign-document',
        cloud_signer.cloud_signer_sessions_sign_document,
    )

    add_route('GET', '/api/v2/events', events_views.get_events)

    add_route(
        'POST',
        '/api/evopay/webhook',
        billing.evopay_process_webhook,
    )

    # Document versions
    add_route(
        'POST',
        '/api/v2/documents/{document_id}/version',
        public_api_decorators.api_handler(upload_versioned),
    )
    add_route(
        'DELETE',
        '/api/v2/documents/{document_id}/version/{version_id}',
        public_api_decorators.api_handler(delete_versioned),
    )

    # Groups
    add_route(
        'GET',
        '/api/v2/groups',
        public_api_decorators.api_handler(get_groups_handler),
    )
    add_route(
        'GET',
        '/api/v2/groups/{group_id}',
        public_api_decorators.api_handler(get_group_handler),
    )
    add_route(
        'POST',
        '/api/v2/groups',
        public_api_decorators.api_handler(add_group_handler),
    )
    add_route(
        'PATCH',
        '/api/v2/groups/{group_id}',
        public_api_decorators.api_handler(update_group_handler),
    )
    add_route(
        'DELETE',
        '/api/v2/groups/{group_id}',
        public_api_decorators.api_handler(delete_group_handler),
    )
    add_route(
        'GET',
        '/api/v2/groups/{group_id}/members',
        public_api_decorators.api_handler(get_group_members_handler),
    )
    add_route(
        'POST',
        '/api/v2/groups/{group_id}/members',
        public_api_decorators.api_handler(add_group_member_handler),
    )
    add_route(
        'POST',
        '/api/v2/groups/{group_id}/members/remove',
        public_api_decorators.api_handler(delete_group_member_handler),
    )

    # Templates
    add_route(
        'GET',
        '/api/v2/templates',
        public_api_decorators.api_handler(document_automation_views.get_templates),
    )
    add_route(
        'GET',
        '/api/v2/templates/{template_id}',
        public_api_decorators.api_handler(document_automation_views.get_template),
    )

    # Document categories
    add_route(
        'GET',
        '/api/v2/document-categories',
        public_api_decorators.api_handler(document_categories_views.list_document_categories),
    )
    add_route(
        'POST',
        '/api/v2/document-categories',
        public_api_decorators.admin_api_handler(
            document_categories_views.create_internal_document_category
        ),
    )
    add_route(
        'DELETE',
        '/api/v2/document-categories/{document_category_id}',
        public_api_decorators.admin_api_handler(
            document_categories_views.delete_internal_document_category
        ),
    )
    add_route(
        'PATCH',
        '/api/v2/document-categories/{document_category_id}',
        public_api_decorators.admin_api_handler(
            document_categories_views.update_internal_document_category
        ),
    )

    # Report requests
    add_route(
        'POST',
        '/api/v2/document-actions/request-report',
        events_views.create_document_actions_report_request_public,
    )
    add_route(
        'POST',
        '/api/v2/user-actions/request-report',
        events_views.create_user_actions_report_request_public,
    )
    add_route(
        'GET',
        '/api/v2/actions/download-report/{report_id}',
        events_views.download_actions_report_file_public,
    )
    add_route(
        'GET',
        '/api/v2/actions/report-status/{report_id}',
        events_views.get_actions_report_status_public,
    )


def setup_auth_api(app: web.Application) -> None:
    """Setup auth API routes.

    Routes here will use different limit settingss in IPS, while routes in
    internal API should be requested only for authenticated users and will have
    larger limit settings in IPS.
    """
    add_route = add_route_factory(app, check_prefix='/auth-api/')

    # Authentication
    add_route('POST', '/auth-api/login', auth.login, name='api.auth.login')
    add_route(
        method='POST',
        path='/auth-api/logout',
        handler=auth.logout,
        name='api.auth.logout',
        add_options=True,
    )
    add_route(
        'POST',
        '/auth-api/logout-all-sessions',
        auth.logout_all_sessions,
        name='api.auth.logout_all_sessions',
    )
    add_route(
        'POST',
        '/auth-api/logout-session',
        auth.logout_session,
    )

    # External providers
    add_route(
        method='POST',
        path='/auth-api/providers/google',
        handler=redirect_to_app(auth.google_auth),
    )
    add_route(
        method='POST',
        path='/auth-api/providers/microsoft',
        handler=redirect_to_app(auth.microsoft_auth),
    )
    add_route(
        method='POST',
        path='/auth-api/providers/apple',
        handler=redirect_to_app(auth.apple_auth),
        name='api.auth.apple',
    )

    # Registration
    add_route(
        'POST',
        '/auth-api/registration',
        registration.registration,
        name='api.registration.registration',
    )
    add_route(
        'POST',
        '/auth-api/registration/token',
        registration.registration_with_token,
        name='api.registration.registration_with_token',
    )
    add_route(
        'POST',
        '/auth-api/registration/short',
        registration.short_registration,
        name='api.registration.short_registration',
    )
    add_route(
        'POST',
        '/auth-api/registration/info',
        registration.save_registration_info,
    )
    add_route(
        'PATCH',
        '/auth-api/registration/change-email',
        base_login_required()(registration.change_email),
        name='api.registration.change_email',
    )

    # Auth by phone number (WEB)
    add_route('POST', '/auth-api/phone-auth/send-code', phone_auth.send_phone_auth_code)
    add_route('POST', '/auth-api/phone-auth/process-code', phone_auth.process_phone_auth_code)


def setup_blackbox_api(app: web.Application) -> None:
    """Setup blackbox API routes.

    This API is private, so shouldn't be a subject of IPS limits.
    """
    add_route = add_route_factory(app, check_prefix='/api/private/blackbox/')
    add_route('POST', '/api/private/blackbox/documents', blackbox_views.upload_document)
    add_route(
        'GET',
        '/api/private/blackbox/document/{document_id}/hash',
        public_api_decorators.api_handler(documents.get_document_hash),
    )
    add_route(
        'POST',
        '/api/private/blackbox/documents/{document_id}/signatures',
        blackbox_views.add_external_signature,
    )
    add_route(
        'POST',
        '/api/private/blackbox/document/{document_id}/signature',
        blackbox_views.add_external_signature,
    )


def setup_edi_api(app: web.Application) -> None:
    """
    Setup EDI API routes.

    EDI was one of the first integrations, and at the time, it wasn't clear that we would
    have more product integrations. That's why it has a separate API, not included in the
    "setup_integrations_api"
    """
    # Must be synced with Istio config
    add_route = add_route_factory(app, check_prefix='/api/private/edi/')
    add_route('POST', '/api/private/edi/documents', edi.upload)
    add_route('GET', '/api/private/edi/documents/{document_id}', edi.get_document)
    add_route('DELETE', '/api/private/edi/documents/{document_id}', edi.delete)
    add_route('POST', '/api/private/edi/sign-sessions', edi.create_sign_session)
    add_route(
        'POST',
        '/api/private/edi/documents/{document_id}/reject',
        edi.document_reject,
    )
    add_route(
        'GET',
        '/api/private/edi/documents/{document_id}/archive',
        edi.get_document_archive,
    )
    add_route(
        'GET',
        '/api/private/edi/documents/{document_id}/comments',
        edi.list_document_comments,
    )

    add_route(
        'GET',
        '/api/private/edi/documents/{document_id}/signatures',
        edi.get_signatures,
    )
    add_route(
        'POST',
        '/api/private/edi/documents/{document_id}/signatures',
        edi.add_signature,
    )
    add_route(
        'GET',
        '/api/private/edi/documents/signatures/{signature_id}',
        edi.get_signature,
    )
    add_route(
        'POST',
        '/api/private/edi/documents/{document_id}/send',
        edi.send_document,
    )
    add_route('POST', '/api/private/edi/users/invite', edi.invite_user_edi)

    add_route(
        method='POST',
        path='/api/private/edi/users/create',
        handler=edi.create_vchasno_user_edi,
    )

    # document revoke
    add_route(
        'POST',
        '/api/private/edi/documents/{document_id}/revoke',
        edi.document_revoke_create,
    )
    add_route(
        'GET',
        '/api/private/edi/documents/{document_id}/revoke',
        edi.document_revoke_get,
    )
    add_route(
        'POST',
        '/api/private/edi/documents/{document_id}/revoke/signatures',
        edi.document_revoke_add_signature,
    )
    add_route(
        'GET',
        '/api/private/edi/documents/{document_id}/revoke/signatures',
        edi.get_revoke_signatures,
    )
    add_route(
        'POST',
        '/api/private/edi/documents/{document_id}/revoke/reject',
        edi.document_revoke_reject,
    )


def setup_integrations_api(app: web.Application) -> None:
    """
    Setup API routes for private integrations.

    Check also docs for more details ./api/private/README.md
    """
    docs: OpenApiDocs | None = None

    # This API is private and should not be exposed to the public
    if get_level() in (AppLevel.test, AppLevel.local, AppLevel.dev):
        docs = OpenApiDocs(
            title='Private integrations API',
            schema_path='/api/docs/private-integrations/openapi.json',
            ui_path='/api/docs/private-integrations/ui',
            description='Приватне API для інтеграції сервісів всередині Вчасно',
        )
        docs.add_routes(app)

    add_route = add_route_factory(app, check_prefix='/api/private/integrations/', docs=docs)

    # Zakupki API
    add_route(
        'GET',
        '/api/private/integrations/companies',
        private_integrations.get_companies,
    )

    # KEP API
    add_route(
        'POST',
        '/api/private/integrations/kep/company-billing-accounts',
        private_integrations.get_company_billing_accounts,
    )
    add_route(
        'POST',
        '/api/private/integrations/kep/check-credentials',
        private_integrations.check_credentials_kep,
    )
    add_route(
        'POST',
        '/api/private/integrations/kep/user-companies',
        private_integrations.get_user_companies,
    )
    add_route(
        'GET',
        '/api/private/integrations/kep/documents/{document_id}/signatures',
        private_integrations.get_document_signatures,
    )
    add_route(
        method='POST',
        path='/api/private/integrations/kep/create-vchasno-user',
        handler=private_integrations.create_vchasno_user_kep,
    )

    # KASA API
    add_route(
        'POST',
        '/api/private/integrations/kasa/check-credentials',
        private_integrations.check_credentials_kasa,
    )
    add_route(
        'POST',
        '/api/private/integrations/kasa/registration',
        private_integrations.registration_kasa,
    )
    add_route(
        method='POST',
        path='/api/private/integrations/kasa/create-vchasno-user',
        handler=private_integrations.create_vchasno_user_kasa,
    )

    # TTN API
    add_route(
        'POST',
        '/api/private/integrations/ttn/check-credentials',
        private_integrations.check_credentials_ttn,
    )
    add_route(
        'POST',
        '/api/private/integrations/ttn/invite-user',
        private_integrations.invite_user_ttn,
    )
    add_route(
        method='POST',
        path='/api/private/integrations/ttn/create-vchasno-user',
        handler=private_integrations.create_vchasno_user_ttn,
    )
    add_route(
        method='POST',
        path='/api/private/integrations/ttn/send-phone-auth-code',
        handler=private_integrations.send_phone_auth_code_ttn,
    )
    add_route(
        method='POST',
        path='/api/private/integrations/ttn/process-phone-auth',
        handler=private_integrations.process_phone_auth_ttn,
    )

    # HRS API
    add_route(
        'POST',
        '/api/private/integrations/hrs/invite-user',
        private_hrs_integration.invite_user,
    )
    add_route(
        'POST',
        '/api/private/integrations/hrs/get-users',
        private_hrs_integration.get_users_by_vchasno_ids,
    )
    add_route(
        'POST',
        '/api/private/integrations/hrs/get-user-roles',
        private_hrs_integration.get_user_roles,
    )
    add_route(
        'POST',
        '/api/private/integrations/hrs/get-companies-roles',
        private_hrs_integration.get_companies_roles,
    )
    add_route(
        'POST',
        '/api/private/integrations/hrs/roles',
        private_hrs_integration.add_role,
    )
    add_route(
        'DELETE',
        '/api/private/integrations/hrs/roles',
        private_hrs_integration.delete_role,
    )
    add_route(
        'POST',
        '/api/private/integrations/hrs/companies',
        private_hrs_integration.create_company,
    )
    add_route(
        'GET',
        '/api/private/integrations/hrs/companies/{edrpou}',
        private_hrs_integration.get_company_by_edrpou,
    )
    add_route(
        'POST',
        '/api/private/integrations/hrs/documents',
        private_hrs_integration.documents_upload,
    )
    add_route(
        'POST',
        '/api/private/integrations/hrs/documents/delete-request',
        private_hrs_integration.documents_delete_request,
    )
    add_route(
        'GET',
        '/api/private/integrations/hrs/documents/{document_id}',
        private_hrs_integration.download_document,
    )
    add_route(
        'GET',
        '/api/private/integrations/hrs/documents/{document_id}/print',
        private_hrs_integration.download_print_document,
    )
    add_route(
        'POST',
        '/api/private/integrations/hrs/documents/{document_id}/send',
        private_hrs_integration.send_document,
    )
    add_route(
        'POST',
        '/api/private/integrations/hrs/send-mobile-notification',
        private_hrs_integration.send_push_notification_to_mobile_app,
    )

    # Landing API
    add_route(
        'POST',
        '/api/private/integrations/landing/check/company',
        landing.check_company_integration,
    )
    add_route(
        'POST',
        '/api/private/integrations/landing/check/company/upload',
        landing.check_company_upload_integration,
    )
    add_route(
        method='GET',
        path='/api/private/integrations/landing/current-user',
        handler=landing.get_current_user,
    )

    # Creatio API
    add_route(
        'POST',
        '/api/private/integrations/creatio/bill',
        billing.add_bill_from_creatio,
    )


def setup_super_admin_api(app: web.Application) -> None:
    """Setup private super admin API routes. Do not confuse with integration API

    This API is private, so shouldn't be a subject of IPS limits.
    """

    docs: OpenApiDocs | None = None

    # This API is private and should not be exposed to the public
    if get_level() in (AppLevel.test, AppLevel.local, AppLevel.dev):
        docs = OpenApiDocs(
            title='Private admin API',
            schema_path='/api/docs/private/openapi.json',
            ui_path='/api/docs/private/ui',
            description='Приватне API для супер-адміністраторів',
        )
        docs.add_routes(app)

    add_route = add_route_factory(app, check_prefix='/api/private/', docs=docs)

    add_route(
        'POST',
        '/api/private/companies/rates/trails',
        private_api_views.activate_trial_companies_rates,
    )
    add_route(
        'POST',
        '/api/private/privatbank/fetch',
        private_api_views.fetch_privatbank_transactions,
    )
    add_route(
        'POST',
        '/api/private/pumb/fetch',
        private_api_views.fetch_pumb_transactions,
    )
    add_route(
        'GET',
        '/api/private/v2/flags/{flag_name}',
        private_api_views.activate_feature_flag,
    )
    add_route(
        'GET',
        '/api/private/documents/{document_id}/state',
        private_api_views.get_document_state,
    )
    add_route(
        'POST',
        '/api/private/documents/{document_id}/signatures',
        private_api_views.download_signatures,
    )
    add_route(
        'POST',
        '/api/private/change-email',
        private_api_views.change_email,
    )
    add_route('POST', '/api/private/jobs/{topic}', private_api_views.run_job)

    add_route(
        'POST',
        '/api/private/roles/tokens',
        private_api_views.bulk_token_generation,
    )
    add_route(
        'POST',
        '/api/private/es/documents/{document_id}',
        private_api_views.get_es_document,
    )
    add_route(
        'POST',
        '/api/private/users/autogenerate',
        private_api_views.create_user,
    )
    add_route(
        'POST',
        '/api/private/users/soft-delete',
        private_api_views.soft_delete_user,
    )

    add_route(
        'POST',
        '/api/private/roles/{role_id}/activate',
        private_api_views.activate_role,
    )

    add_route('GET', '/api/private/es-mapping', elastic.show_mapping)
    add_route('POST', '/api/private/es-mapping', elastic.update_mapping)
    add_route(
        method='POST',
        path='/api/private/es-mapping/contact-recipients',
        handler=elastic.update_contact_recipient_mapping,
    )

    add_route(
        method='DELETE',
        path='/api/private/roles/{role_id}',
        handler=private_api_views.sa_delete_role,
    )
    add_route(
        'POST',
        '/api/private/auth/2fa/reset',
        private_api_views.reset_2fa,
    )
    add_route(
        'GET',
        '/api/private/companies/{edrpou}/corporate-domains',
        private_api_views.get_corporate_domains,
        name='companies.get_corporate_domains',
    )
    add_route(
        'PATCH',
        '/api/private/companies/{edrpou}/corporate-domains',
        private_api_views.modify_corporate_domains,
        name='companies.change_corporate_domains',
    )

    add_route(
        'PATCH',
        '/api/private/companies/{company_id}/configs',
        private_api_views.upsert_company_config,
        name='companies.upsert_company_config',
    )

    add_route(
        'PATCH',
        '/api/private/companies/{edrpou}/security/permissions',
        private_api_views.sa_update_company_secure_permissions,
    )

    add_route(
        'PATCH',
        '/api/private/billing/configs',
        private_api_views.update_billing_company_config_handler,
    )

    add_route(
        'POST',
        '/api/private/companies/{edrpou}/sync-company-info-from-youcontrol-data',
        private_api_views.sync_company_info_from_youcontrol_data,
        name='companies.sync_company_info_from_youcontrol_data',
    )

    add_route(
        'PATCH',
        '/api/private/companies/{edrpou}/link-balance',
        private_api_views.link_companies_balance,
        name='companies.link_companies_balance',
    )

    add_route(
        'POST',
        '/api/private/billing/price-per-document',
        private_api_views.update_price_per_document,
        name='companies.update_price_per_document',
    )
    add_route(
        method='POST',
        path='/api/private/documents/send',
        handler=private_api_views.sa_send_document,
    )
    add_route(
        'POST',
        '/api/private/billing/sync-crm-rates',
        private_api_views.crm_sync_rates_handler,
        name='billing.crm_sync_rates',
    )
    add_route(
        'POST',
        '/api/private/billing/update-price-per-user',
        private_api_views.update_price_per_user_sa_handler,
        name='billing.update_price_per_user',
    )
    add_route(
        'PATCH',
        '/api/private/billing/cancel-bill',
        billing.cancel_bill_handler,
        name='billing.cancel_bill_handler',
    )

    # Esputnik marketing campaigns for trials
    add_route(
        method='POST',
        path='/api/private/billing/setup-trials-target-audience',
        handler=sa_billing.setup_target_audience_for_trials,
        name='billing.setup_target_audience_for_trials',
    )
    add_route(
        method='GET',
        path='/api/private/billing/setup-trials-target-audience',
        handler=sa_billing.get_target_audience_for_trials,
        name='billing.get_target_audience_for_trials',
    )
    add_route(
        method='DELETE',
        path='/api/private/billing/setup-trials-target-audience',
        handler=sa_billing.delete_target_audience_for_trials,
        name='billing.delete_target_audience_for_trials',
    )

    add_route(
        method='POST',
        path='/api/private/mobile/notifications',
        handler=private_api_views.send_push_notification_to_mobile_app,
        name='mobile.send_push_notification_to_mobile_app',
    )

    # Document categories
    add_route(
        'GET',
        '/api/private/document-categories',
        document_categories_views.list_public_document_categories,
    )
    add_route(
        'POST',
        '/api/private/document-categories',
        document_categories_views.create_public_document_category,
    )
    add_route(
        'PATCH',
        '/api/private/document-categories/{document_category_id}',
        document_categories_views.update_public_document_category,
    )
    add_route(
        'DELETE',
        '/api/private/document-categories/{document_category_id}',
        document_categories_views.delete_public_document_category,
    )
    add_route(
        'POST',
        '/api/private/documents/restore',
        private_api_views.restore_document,
    )
    add_route(
        'POST',
        '/api/private/documents/{document_id}/recalculate-review-status',
        private_api_views.recalculate_reviews_status,
    )
    add_route(
        method='POST',
        path='/api/private/manage-aws-ses-blacklist',
        handler=private_api_views.manage_aws_ses_blacklist,
    )
    add_route(
        method='POST',
        path='/api/private/survey-user',
        handler=private_api_views.sa_store_survey_user_list,
    )

    # Templates API
    add_route(
        'POST',
        '/api/private/templates-migrate/{template_id}',
        private_api_views.migrate_template,
    )
    add_route(
        'POST',
        '/api/private/templates',
        private_api_views.sa_add_template,
    )
    add_route('PATCH', '/api/private/templates/{template_id}', private_api_views.sa_template_update)
    add_route(
        'DELETE', '/api/private/templates/{template_id}', private_api_views.sa_template_delete
    )

    # CSP Headers config
    add_route(
        'POST',
        '/api/private/csp-headers',
        tmp_csp_views.set_csp_policies,
    )
    add_route(
        'GET',
        '/api/private/csp-headers',
        tmp_csp_views.get_csp_policies,
    )

    add_route(
        method='POST',
        path='/api/private/upload-banner-promo-kasa-list',
        handler=banner_views.update_banner_promo_kasa_list,
    )

    add_route(
        method='POST',
        path='/api/private/block-user',
        handler=private_api_views.block_user,
    )
    add_route(
        method='POST',
        path='/api/private/ban-user',
        handler=private_api_views.ban_user,
    )
    add_route(
        method='POST',
        path='/api/private/update-phone-auth-whitelist',
        handler=private_api_views.update_phone_auth_whitelist,
    )


def setup_debug_api(app: web.Application) -> None:
    """
    Setup API routes for debugging.

    This API shouldn't exist in production environment.
    """

    if get_level() not in (AppLevel.local, AppLevel.test):
        return

    # Basic debug route
    app.router.add_route('GET', '/~/debug', system.debug_handler)

    docs = OpenApiDocs(
        title='Debug API (local only)',
        schema_path='/api/docs/debug/openapi.json',
        ui_path='/api/docs/debug/ui',
        description='API інструменти для локальної розробки',
    )
    docs.add_routes(app)

    # Private debug routes
    add_route = add_route_factory(app, check_prefix='/api/debug/', docs=docs)
    add_route('POST', '/api/debug/jobs', debug_handlers.start_worker_job)
    add_route(
        method='POST',
        path='/api/debug/emails/template/send',
        handler=debug_handlers.send_debug_email,
    )
    add_route(
        method='POST',
        path='/api/debug/emails/template/render',
        handler=debug_handlers.render_debug_email,
    )
    add_route(
        method='POST',
        path='/api/debug/convert-docx-to-pdf',
        handler=debug_handlers.convert_docx_to_pdf,
    )

    add_route(
        method='POST',
        path='/api/debug/generate-super-admin-token',
        handler=debug_handlers.generate_super_admin_token,
    )

    add_route(
        method='POST',
        path='/api/debug/graphql–schema',
        handler=debug_handlers.graphql_schema,
    )


def setup_confirmation_routes(app: web.Application) -> None:
    """Setup routes for handle any confirmation tokens."""

    add_route = add_route_factory(app)

    # Check invites
    add_route(
        'GET',
        '/invite/{mixed_token}',
        tokens.invite_token,
        name='tokens.check_invite',
    )

    # Confirm registration
    add_route(
        'GET',
        '/registration/confirm/{jwt_token}',
        registration.confirm_email,
        name='registration.confirm_email',
    )


def setup_shared_view_routes(app: web.Application) -> None:
    add_route = add_route_factory(app, check_prefix='/shared-document-view')

    add_route(
        'GET',
        '/shared-document-view/{sign_session_id}/{document_id}',
        shared_views.ui,
        name='shared_views.ui',
    )

    add_route(
        'POST',
        '/shared-document-view/{document_id}',
        shared_views.apply_signature,
        name='shared_views.apply_signature',
    )

    add_route(
        'POST',
        '/shared-document-view/{document_id}/diia',
        shared_views.diia_request,
    )

    add_route(
        'POST',
        '/shared-document-view/{document_id}/send',
        shared_views.send_document,
        name='shared_views.send_document',
    )


def setup_download_routes(app: web.Application) -> None:
    """Setup routes to handle document downloads."""

    add_route = add_route_factory(app, check_prefix='/downloads/')

    # Bills
    # Super-admin route, allows download bills to XLS for 1C
    add_route(
        'GET',
        '/downloads/bills.xls',
        sa_billing.download_bills,
        name='downloads.bills',
    )
    add_route(
        'GET',
        '/downloads/companies.xls',
        sa_billing.download_companies,
        name='downloads.companies',
    )

    # App Documents
    add_route(
        'GET',
        '/downloads/{document_id}',
        sign_session_login_required(downloads.viewer_signed),
        name='downloads.signed_file',
    )
    add_route(
        'GET',
        '/downloads/{document_id}/print',
        sign_session_login_required(downloads.viewer_print),
        name='downloads.print_file',
    )
    add_route(
        'GET',
        '/downloads/{document_id}/archive',
        sign_session_login_required(downloads.archive),
        name='downloads.archive',
    )
    add_route(
        'GET',
        '/downloads/{document_id}/p7s',
        sign_session_login_required(downloads.p7s_container),
        name='downloads.p7s_container',
    )
    add_route(
        'GET',
        '/downloads/{document_id}/asic',
        sign_session_login_required(downloads.asic),
        name='downloads.asic',
    )
    add_route(
        'GET',
        '/downloads/{document_id}/original',
        sign_session_login_required(downloads.original),
        name='downloads.original',
    )
    add_route(
        'GET',
        '/downloads/{document_id}/signatures/last',
        sign_session_login_required(downloads.last_internal_signature),
    )
    add_route(
        'GET',
        '/downloads/{document_id}/xml-to-json',
        sign_session_login_required(downloads.xml_to_json),
        name='downloads.xml_to_json',
    )
    add_route(
        'GET',
        '/downloads/{document_id}/xml-to-json/rows',
        sign_session_login_required(downloads.xml_to_json_count_rows),
        name='downloads.xml_to_json_count_rows',
    )
    add_route(
        'GET',
        '/downloads/{document_id}/xml-to-pdf',
        sign_session_login_required(downloads.xml_to_pdf),
        name='downloads.xml_to_pdf',
    )
    # multiply files download
    add_route(
        'POST',
        '/downloads/multi-archive',
        login_required()(downloads.multi_archive),
        name='downloads.multi_archive',
    )
    add_route(
        'GET',
        '/downloads/multi-archive/{archive_id}',
        downloads.download_multi_archive,
        name='downloads.download_multi_archive',
    )
    add_route(
        'POST',
        '/downloads/archived-documents',
        login_required()(downloads.download_archived_documents),
    )
    add_route(
        'GET',
        '/downloads/documents/{document_id}/reviews/history.{format:pdf|xlsx}',
        downloads.download_review_history,
    )

    add_route(
        method='GET',
        path='/downloads/actions-report/{file_id}',
        handler=events_views.download_actions_report_file_internal,
        name='downloads.actions_report',
    )


def setup_error_pages(app: web.Application) -> None:
    """Setup error pages for backend app."""
    add_route = add_route_factory(app)

    # Error pages
    add_route(
        'GET',
        '/error/403',
        static_route('error', status=403),
        name='errors.access_denied',
    )
    add_route(
        'GET',
        '/error/404',
        static_route('error', status=404),
        name='errors.not_found',
    )
    add_route(
        'GET',
        '/error/500',
        static_route('error', status=500),
        name='errors.server_error',
    )
    add_route(
        'GET',
        '/error/email-required',
        static_route('error', status=400),
        name='errors.email_required',
    )
    add_route('*', r'/error/{status_code:\d+}', static_route('error'))


def setup_internal_api(app: web.Application) -> None:
    """Setup internal API routes."""

    docs: OpenApiDocs | None = None
    if get_level() in (AppLevel.local, AppLevel.test, AppLevel.dev):
        docs = OpenApiDocs(
            title='Internal API',
            schema_path='/api/docs/internal/openapi.json',
            ui_path='/api/docs/internal/ui',
            description='API для фротенду',
            annotation_required=False,  # too hard to annotate all internal API for now
        )
        docs.add_routes(app)

    add_route = add_route_factory(app, check_prefix='/internal-api/', docs=docs)

    # GraphQL
    add_route('POST', '/internal-api/graphql', graph.query_graph, name='api.graphql')

    # Feedback and surveys
    add_route(
        'POST',
        '/internal-api/feedback',
        login_required()(feedbacks.add_feedback),
        name='api.feedbacks',
    )
    add_route(
        'GET',
        '/internal-api/surveys/document_ai',
        login_required()(feedbacks.get_ai_survey),
    )
    add_route(
        'POST',
        '/internal-api/surveys/document_ai',
        login_required()(feedbacks.add_ai_survey),
    )

    # Authentication

    # Phone 2FA (new names)
    add_route('POST', '/internal-api/2fa/phone/verify', auth.verify_phone_2fa)
    add_route('GET', '/internal-api/2fa/phone/hidden-phone', auth.get_hidden_phone)
    add_route('POST', '/internal-api/2fa/phone/resend', auth.resend_phone_2fa_code)

    # Email 2FA
    add_route('POST', '/internal-api/2fa/email/verify', auth.verify_email_2fa)
    add_route('GET', '/internal-api/2fa/email/hidden-email', auth.get_hidden_email)

    add_route(
        'POST',
        '/internal-api/roles/{role_id}/activate',
        auth.switch_role,
        name='api.auth.activate_role',
    )
    add_route(
        'PATCH',
        '/internal-api/companies/additional/configs',
        auth.update_company_config,
        name='api.auth.update_companies_additional_config',
    )

    # Identifying in other services
    add_route(
        'GET',
        '/internal-api/colbert/identify',
        auth.colbert_identify,
        name='api.auth.colbert_identify',
    )

    # Feature flags
    add_route(
        'GET',
        '/internal-api/flags',
        flags_views.get_flags,
    )

    # Registration
    add_route(
        'POST',
        '/internal-api/invite',
        registration.invite_user,
        name='api.registration.invite_new_user',
    )
    add_route(
        'POST',
        '/internal-api/invite/new-users',
        sa_registration.invite_new_users,
        name='invite.new_users',
    )
    add_route(
        'POST',
        '/internal-api/invite/contacts',
        registration.invite_unregistered_contacts,
        name='invite.contacts',
    )
    add_route(
        'POST',
        '/internal-api/registration/complete',
        registration.complete_registration,
        name='api.registration.complete_registration',
    )
    add_route(
        'POST',
        '/internal-api/registration/signers',
        registration.create_signer_role,
    )
    add_route(
        'POST',
        '/internal-api/registration/resend-confirmation-email',
        registration.resend_confirmation_email,
        name='api.registration.resend_confirmation_email',
    )
    add_route(
        'POST',
        '/internal-api/registration/source',
        registration.set_source,
        name='api.registration.set_source',
    )
    add_route(
        'POST',
        '/internal-api/registration/phone/verify',
        registration.verify_registration_phone,
    )
    add_route(
        'POST',
        '/internal-api/registration/phone',
        registration.update_registration_phone,
        name='api.registration.set_phone',
    )
    add_route(
        'GET',
        path='/internal-api/registration/sync-roles',
        handler=registration.sync_roles,
    )

    # Onboarding
    add_route(
        method='PATCH',
        path='/internal-api/onboarding',
        handler=profile.update_onboarding,
    )

    # Change profile data
    add_route(
        'POST',
        '/internal-api/profile',
        profile.update_user_profile,
        name='api.profile.update_user',
    )
    add_route(
        'POST',
        '/internal-api/profile/password',
        profile.update_password,
        name='api.profile.update_password',
    )
    add_route(
        'POST',
        '/internal-api/profile/phone',
        profile.update_phone,
        name='api.profile.update_phone',
    )
    add_route(
        'PATCH',
        '/internal-api/profile/trial-auto-enabled',
        profile.update_trial_auto_enabled,
        name='api.profile.update_trial_auto_enabled',
    )
    add_route(
        'POST',
        '/internal-api/profile/2fa',
        profile.update_2fa_state,
        name='api.profile.update_2fa_state',
    )
    add_route('POST', '/internal-api/profile/phone-auth', profile.update_phone_auth)

    add_route(
        'POST',
        '/internal-api/profile/subscription',
        profile.subscribe_to_esputnik,
        name='api.profile.subscribe_to_esputnik',
    )
    add_route(
        'POST',
        '/internal-api/profile/language',
        profile.update_language,
    )
    add_route(
        'POST',
        '/internal-api/profile/email-change/validate-start',
        profile.user_email_change__validate_start,
    )
    add_route(
        'POST',
        '/internal-api/profile/email-change/start',
        profile.user_email_change__start,
    )
    add_route(
        'POST',
        '/internal-api/profile/email-change/verify-email',
        profile.user_email_change__verify_email,
    )
    add_route(
        'POST',
        '/internal-api/profile/email-change/resend-verify-email',
        profile.user_email_change__resend_verify_email,
    )
    add_route(
        'POST',
        '/internal-api/profile/email-change/verify-password',
        profile.user_email_change__verify_password,
    )
    add_route(
        'POST',
        '/internal-api/profile/email-change/verify-2fa',
        profile.user_email_change__verify_2fa,
    )
    add_route(
        'POST',
        '/internal-api/profile/email-change/resend-2fa',
        profile.user_email_change__resend_2fa,
    )
    add_route(
        'POST',
        '/internal-api/profile/email-change/confirm',
        profile.user_email_change__confirm,
    )
    add_route(
        'POST',
        '/internal-api/profile/email-change/resend-confirm',
        profile.user_email_change__resend_confirmation,
    )
    add_route(
        'POST',
        '/internal-api/profile/email-change/cancel',
        profile.user_email_change__cancel,
    )
    add_route(
        'POST',
        '/internal-api/profile/email-change/check-contacts',
        profile.user_email_change__check_contacts,
    )
    add_route(
        'POST',
        '/internal-api/profile/email-change/update-contacts',
        profile.user_email_change__update_contacts,
    )
    add_route(
        'POST',
        '/internal-api/phone-usage',
        profile.update_phone_usage_info,
    )

    # Modify notification settings
    add_route(
        'GET',
        '/internal-api/notifications/unsubscribe/{jwt_token}',
        notifications.unsubscribe,
        name='api.notifications.unsubscribe',
    )

    add_route(
        'GET',
        '/internal-api/notifications/unregistered/unsubscriptions',
        notifications.unsubscribe_unregistered,
        name='api.notifications.unregistered.unsubscriptions',
    )
    add_route(
        'DELETE',
        '/internal-api/notifications/unregistered/unsubscriptions',
        notifications.cancel_unregistered_unsubscription,
        name='api.notifications.unregistered.cancel-unsubscriptions',
    )

    add_route(
        'PATCH',
        '/internal-api/notifications/triggers',
        trigger_notifications.update_trigger_notifications,
    )
    add_route(
        'POST',
        '/internal-api/notifications/popups',
        notifications.update_popups_data,
    )

    # Modify companies & roles
    add_route(
        'POST',
        '/internal-api/companies',
        profile.add_company,
        name='api.profile.add_company',
    )
    add_route('POST', '/internal-api/companies/diia', base_login_required()(profile.diia_request))
    add_route('POST', '/internal-api/companies/zakupki', profile.check_zakupki_company)
    add_route(
        'PATCH',
        '/internal-api/companies/{company_id}/configs',
        profile.sa_update_company_config_ui,
    )
    add_route(
        'PATCH',
        '/internal-api/companies/security/permissions',
        update_company_secure_permissions,
    )
    add_route(
        'PATCH',
        '/internal-api/companies/{edrpou}',
        profile.update_company,
        name='api.profile.update_company',
    )
    add_route(
        'DELETE',
        '/internal-api/roles/{role_id}',
        login_required()(profile.delete_role),
        name='api.profile.delete_role',
    )
    add_route(
        'PATCH',
        '/internal-api/roles/{role_id}',
        login_required()(profile.update_role),
        name='api.profile.update_role',
    )
    add_route(
        'POST',
        '/internal-api/roles/{role_id}/deny',
        profile.deny_role,
        name='api.profile.deny_role',
    )
    add_route(
        'POST',
        '/internal-api/roles/agree',
        profile.agree_on_roles,
        name='api.profile.agree_on_roles',
    )
    add_route(
        'POST',
        '/internal-api/tokens',
        auth.add_token,
        name='api.profile.add_token',
    )
    add_route(
        'DELETE',
        '/internal-api/tokens',
        auth.delete_token,
        name='api.profile.delete_token',
    )
    add_route(
        'POST',
        '/internal-api/roles/default-recipient',
        profile.update_default_recipient,
        name='api.profile.update_default_recipient',
    )

    # Billing
    add_route(
        'POST',
        '/internal-api/billing/bonuses',
        billing.add_bonus,
        name='api.billing.add_bonus',
    )
    add_route(
        'POST',
        '/internal-api/bills/{bill_id}/activate',
        billing.activate_bill_service,
        name='api.bills.activate_bill_service',
    )
    add_route(
        'DELETE',
        '/internal-api/billing/bonuses/{bonus_id}',
        billing.delete_bonus,
        name='api.billing.delete_bonus',
    )
    add_route(
        'PATCH',
        '/internal-api/billing/bonuses/{bonus_id}',
        billing.update_bonus,
        name='api.billing.update_bonus',
    )
    add_route(
        'POST',
        '/internal-api/billing/bonuses/activate-custom',
        billing.activate_custom_bonus,
        name='api.billing.activate_custom_bonus',
    )
    add_route(
        'POST',
        '/internal-api/billing/bonuses/cancel',
        billing.cancel_bonus,
        name='api.billing.cancel_bonus',
    )
    add_route(
        'POST',
        '/internal-api/billing/bonuses/mass-activate-custom',
        billing.activate_custom_bonuses_from_file,
        name='api.billing.activate_custom_bonuses_from_file',
    )
    add_route(
        'POST',
        '/internal-api/billing/debit/activate',
        billing.activate_debit,
        name='api.billing.activate_debit',
    )
    add_route(
        'POST',
        '/internal-api/billing/debit/cancel',
        billing.cancel_debit_handler,
        name='api.billing.cancel_debit',
    )
    add_route('POST', '/internal-api/billing/companies/rates', billing.add_company_rate)
    add_route(
        'POST',
        '/internal-api/billing/companies/rates/trials',
        billing.add_trial_company_rate,
    )
    add_route(
        'PATCH',
        '/internal-api/billing/companies/rates/{rate_id}/employee-amount',
        billing.increase_employees_amount,
        name='api.billing.increase_employees_amount',
    )
    add_route(
        'PATCH',
        '/internal-api/billing/companies/rates/{rate_id}',
        billing.update_company_rate,
    )
    add_route(
        'DELETE',
        '/internal-api/billing/companies/rates/{rate_id}',
        billing.delete_company_rate,
    )
    add_route(
        'GET',
        '/internal-api/billing/checkout/properties',
        billing.get_checkout_properties,
    )
    add_route(
        'GET',
        '/internal-api/bills/{bill_id}',
        billing.retrieve_bill,
        name='api.billing.retrieve_bill',
    )
    add_route(
        method='GET',
        path='/internal-api/bills/{bill_id}/html',
        handler=billing.retrieve_bill_html,
        name='api.billing.retrieve_bill_html',
    )
    add_route(
        'POST',
        '/internal-api/bills',
        billing.add_bill_from_web,
        name='api.billing.add_bill',
    )
    add_route(
        'GET',
        '/internal-api/bills/{bill_id}/email',
        billing.send_bill_email,
        name='api.billing.send_bill_email',
    )
    add_route(
        'GET',
        '/internal-api/bills/{bill_id}/pdf',
        billing.retrieve_bill_pdf,
        name='api.billing.retrieve_bill_pdf',
    )
    add_route(
        'GET',
        '/internal-api/billing/recommended-rate',
        billing.recommended_rate_handler,
        name='api.billing.recommended_rate',
    )

    # Remind & recover password
    add_route(
        'POST',
        '/internal-api/profile/password/remind',
        profile.remind_password,
        name='api.profile.remind_password',
    )
    add_route(
        'POST',
        '/internal-api/profile/password/recover',
        profile.recover_password,
        name='api.profile.recover_password',
    )

    # Uploads API
    add_route('POST', '/internal-api/documents', uploads.upload, name='uploads.upload')

    add_route(
        'POST', '/internal-api/indexation-status', login_required()(documents.indexing_status)
    )

    # Documents AI suggest/summary/extract etc
    add_route(
        'POST', '/internal-api/documents/suggest', documents_ai.document_meta_suggest_by_content
    )
    add_route(
        'POST',
        '/internal-api/documents/{document_id}/suggest',
        documents_ai.document_meta_suggest_by_id,
    )
    add_route(
        'POST',
        '/internal-api/documents/{document_id}/summary/suggest',
        documents_ai.document_summary,
    )
    add_route(
        'POST',
        '/internal-api/documents/structured-data/suggest',
        extract_structured_data_from_document,
    )

    # Documents API
    add_route(
        'DELETE',
        '/internal-api/documents/{document_id}',
        login_required()(documents.delete),
        name='api.documents.delete',
    )
    add_route(
        'PATCH',
        '/internal-api/documents/{document_id}',
        documents.update,
        name='api.documents.update',
    )
    add_route(
        'PATCH',
        '/internal-api/documents/{document_id}/accesses',
        documents.open_access,
    )
    add_route(
        'POST',
        '/internal-api/documents/{document_id}/change-recipient',
        documents.change_bilateral_recipient,
        name='api.documents.change_recipient',
    )
    add_route(
        'POST',
        '/internal-api/documents/{document_id}/send',
        sign_session_login_required(documents.send_document),
        name='api.documents.send',
    )
    add_route(
        'POST',
        '/internal-api/documents/{document_id}/xml-to-pdf',
        sign_session_login_required(downloads.create_xml_to_pdf),
        name='api.documents.create_xml_to_pdf',
    )
    add_route(
        method='POST',
        path='/internal-api/documents/{document_id}/office-to-pdf',
        handler=documents.convert_office_document_to_pdf,
    )
    add_route(
        'POST',
        '/internal-api/documents/{document_id}/children',
        login_required()(documents.add_children),
        name='api.documents.add_children',
    )
    add_route(
        'POST',
        '/internal-api/documents/{document_id}/child/{child_id}',
        login_required()(documents.add_child),
        name='api.documents.add_child',
    )
    add_route(
        'DELETE',
        '/internal-api/documents/{document_id}/child/{child_id}',
        login_required()(documents.delete_child),
        name='api.documents.delete_child',
    )
    add_route(
        'GET',
        '/internal-api/documents/{document_id}/hash',
        sign_session_login_required(documents.get_document_hash),
    )
    add_route(
        'GET',
        '/internal-api/documents/{document_id}/sign-summary',
        sign_session_login_required(documents.download_signatures_details),
    )

    # delete documents request
    add_route(
        'DELETE',
        '/internal-api/cancel-delete-requests',
        documents.cancel_delete_requests,
        name='api.documents.cancel_delete_requests',
    )
    add_route(
        'POST',
        '/internal-api/documents/delete-request',
        documents.create_delete_request_handler,
        name='api.documents.delete_request',
    )
    add_route(
        'POST',
        '/internal-api/documents/accept-delete-request',
        documents.accept_delete_request,
        name='api.documents.accept_delete_request',
    )
    add_route(
        'POST',
        '/internal-api/documents/reject-delete-request',
        documents.reject_delete_request,
        name='api.documents.reject_delete_request',
    )
    add_route(
        'POST',
        '/internal-api/documents/cancel-delete-vote',
        documents.cancel_delete_request_vote,
        name='api.documents.cancel_delete_request_vote',
    )
    # end delete documents request

    add_route(
        'POST',
        '/internal-api/documents/recipients/emails',
        login_required()(documents.find_recipients_emails),
    )

    # Comments API
    add_route(
        'POST',
        '/internal-api/documents/{document_id}/comments',
        sign_session_login_required(comments.add),
        name='api.comments.add',
    )

    # Signatures API
    add_route(
        'POST',
        '/internal-api/documents/{document_id}/signatures',
        sign_session_login_required(signatures.add),
        name='api.signatures.add',
    )
    add_route(
        'POST',
        '/internal-api/documents/{document_id}/reject',
        sign_session_login_required(documents_views.reject),
        name='api.signatures.reject',
    )
    add_route(
        'POST',
        '/internal-api/documents/reject',
        login_required()(documents_views.reject_bulk),
    )
    add_route(
        'POST',
        '/internal-api/signatures/empty/{signature_id}',
        signatures.update_empty,
        name='api.signatures.update_empty',
    )
    add_route(
        'POST',
        '/internal-api/signatures/certificates',
        signatures.update_certificate,
    )
    add_route(
        'POST',
        '/internal-api/documents/{document_id}/diia',
        sign_session_login_required(signatures.diia_request),
    )
    add_route(
        'POST',
        '/internal-api/signatures/operation-id',
        signatures.add_operation_id,
    )
    add_route(
        'POST',
        '/internal-api/signatures/get-serial-number',
        signatures.get_serial_number,
        name='api.signatures.get_serial_number',
    )
    # Keep that route on the bottom of the signatures routes, because it catches
    # all DELETE routes which start with /internal-api/signatures/
    add_route(
        'DELETE',
        '/internal-api/signatures/{operation_id}',
        signatures.delete_operation_id,
    )

    add_route(
        'GET',
        '/internal-api/kep/certificates',
        sign_session_base_login_required(kep_user_certificates),
    )

    # Proxy requests to CA Servers
    add_route('POST', '/internal-api/proxy', proxy.proxy_request, name='api.proxy')

    add_route(
        'POST',
        '/internal-api/proxy/ssid/{sign_session_id}',
        proxy.proxy_request,
        name='api.proxy_sign_session',
    )
    add_route('*', '/internal-api/proxy/signer', proxy.proxy_cloud_signer)
    add_route('*', '/internal-api/proxy/kep', sign_session_login_required(proxy.proxy_kep))

    # Contacts
    add_route(
        'POST',
        '/internal-api/contacts/sync',
        contacts.sync_contacts,
        name='api.contacts.sync',
    )
    add_route(
        'GET',
        '/internal-api/contacts/clear',
        contacts.clear,
        name='api.contacts.clear',
    )
    add_route(
        'POST',
        '/internal-api/contacts/upload',
        contacts.import_contacts,
        name='api.contacts.upload',
    )
    add_route(
        'PATCH',
        '/internal-api/contacts/{contact_id}',
        contacts.edit_contact,
        name='api.contacts.edit_contact',
    )
    add_route(
        'POST',
        '/internal-api/contacts/persons',
        contacts.create_contact_person,
        name='api.contacts.create_contact_person',
    )
    add_route(
        'PATCH',
        '/internal-api/contacts/persons/{contact_person_id}',
        contacts.edit_contact_person,
        name='api.contacts.edit_contact_person',
    )
    add_route(
        'DELETE',
        '/internal-api/contacts/persons/{contact_person_id}',
        contacts.delete_contact_person,
        name='api.contacts.delete_contact_person',
    )
    # contact dossier
    add_route(
        'GET',
        '/internal-api/contacts/dossier',
        contacts.get_youcontrol_contact_url,
        name='api.contacts.dossier',
    )

    # Reviews
    add_route(
        'POST',
        '/internal-api/reviews',
        login_required()(reviews.add_review_handler),
        name='api.reviews.add',
    )
    add_route(
        'POST',
        '/internal-api/reviews-batch',
        login_required()(reviews.add_reviews_batch_handler),
        name='api.reviews.add_reviews',
    )
    # todo: DO we need this? Unused in FE ?
    add_route(
        'DELETE',
        '/internal-api/{document_id}/reviews',
        login_required()(reviews.delete_review),
        name='api.reviews.delete',
    )
    # Tags
    add_route(
        'POST',
        '/internal-api/documents/tags',
        login_required()(tags.create_tags_for_documents),
        name='api.tags.create_tags',
    )
    add_route(
        'POST',
        '/internal-api/documents/tags/connections',
        login_required()(tags.connect_tags_and_documents),
        name='api.tags.connect_documents',
    )
    add_route(
        'DELETE',
        '/internal-api/documents/tags/connections',
        login_required()(tags.disconnect_documents_and_tags),
        name='api.tags.disconnect_documents',
    )

    # document_templates
    add_route(
        'POST',
        '/internal-api/documents/templates',
        document_automation_views.create_template,
        name='api.document_templates.create',
    )
    add_route(
        'PUT',
        '/internal-api/documents/templates/{template_id}',
        document_automation_views.update_template,
        name='api.document_templates.update',
    )
    add_route(
        'DELETE',
        '/internal-api/documents/templates/{template_id}',
        document_automation_views.delete_template,
        name='api.document_templates.delete',
    )
    add_route(
        'POST',
        '/internal-api/documents/{document_id}/templates/{template_id}',
        document_automation_views.save_assigned,
    )
    add_route(
        'PATCH',
        '/internal-api/documents/templates/automation/{automation_id}',
        document_automation_views.activate_automation,
        name='api.document_templates.activate_automation',
    )

    # document_required_fields
    add_route(
        'POST',
        '/internal-api/documents/required-field',
        documents_required_fields.create_document_required_field,
        name='api.document_required_fields.create',
    )
    add_route(
        'PATCH',
        '/internal-api/documents/required-field/{field_id}',
        documents_required_fields.update_document_required_field,
        name='api.document_required_fields.update',
    )
    add_route(
        'DELETE',
        '/internal-api/documents/required-field/{field_id}',
        documents_required_fields.delete_document_required_fields,
        name='api.document_required_fields.delete',
    )

    # document metadata
    add_route(
        'POST',
        '/internal-api/documents/fields/roles',
        login_required()(documents_fields.create_access),
        name='api.document_meta.create_accesses',
    )
    add_route(
        'DELETE',
        '/internal-api/documents/fields/roles',
        login_required()(documents_fields.delete_access),
        name='api.document_meta.delete_accesses',
    )
    add_route(
        'POST',
        '/internal-api/documents/fields',
        login_required()(documents_fields.create),
        name='api.document_meta.create',
    )
    add_route(
        'PATCH',
        '/internal-api/documents/fields/{field_id}',
        documents_fields.update,
        name='api.document_meta.update',
    )
    add_route(
        'DELETE',
        '/internal-api/documents/fields/{field_id}',
        documents_fields.delete,
        name='api.document_meta.delete',
    )
    add_route(
        'POST',
        '/internal-api/documents/fields/swap',
        documents_fields.swap,
        name='api.document_meta.swap',
    )

    add_route(
        'POST',
        '/internal-api/roles/tags',
        login_required()(tags.create_tags_for_roles),
        name='api.tags.create_tags_for_roles',
    )
    add_route(
        'POST',
        '/internal-api/roles/tags/connections',
        login_required()(tags.connect_tags_and_roles),
        name='api.tags.role_tags_connections',
    )
    add_route(
        'DELETE',
        '/internal-api/roles/tags/connections',
        login_required()(tags.disconnect_tags_and_roles),
        name='api.tags.role_connections',
    )
    add_route(
        'POST',
        '/internal-api/contacts/tags',
        login_required()(tags.create_tags_for_contacts),
    )
    add_route(
        'POST',
        '/internal-api/contacts/tags/connections',
        login_required()(tags.connect_tags_and_contacts),
    )
    add_route(
        'DELETE',
        '/internal-api/contacts/tags/connections',
        login_required()(tags.disconnect_tags_and_contacts),
    )

    # Sales
    add_route(
        'POST',
        '/internal-api/contact-manager',
        crm_views.contact_manager,
    )

    # Banner
    add_route(
        'POST',
        '/internal-api/banner/promo-kasa/update-count',
        banner_views.update_banner_promo_kasa_count,
    )
    add_route('POST', '/internal-api/banner', banner_views.create)
    add_route('PUT', '/internal-api/banner/{banner_id}', banner_views.update)

    # Actions
    add_route('POST', '/internal-api/actions/visits', actions.save_visit)

    # Report requests
    add_route(
        method='POST',
        path='/internal-api/document-actions/request-report',
        handler=events_views.create_document_actions_report_request_internal,
    )
    add_route(
        method='POST',
        path='/internal-api/user-actions/request-report',
        handler=events_views.create_user_actions_report_request_internal,
    )

    # Evopay
    add_route(
        'POST',
        '/internal-api/evopay/generate-page',
        billing.evopay_generate_page,
        name='api.evopay.generate_page',
    )

    # Document versions
    add_route(
        'POST',
        '/internal-api/document/{document_id}/version',
        login_required()(upload_versioned),
    )
    add_route(
        'DELETE',
        '/internal-api/document/{document_id}/version/{version_id}',
        login_required()(delete_versioned),
    )

    # Flows
    add_route(
        method='POST',
        path='/internal-api/flows/',
        handler=sign_session_login_required(flow.add_flows),
    )

    add_route(
        'GET',
        '/internal-api/registration/check-email',
        registration.check_email_registration,
    )

    # Groups
    add_route(
        'POST',
        '/internal-api/groups',
        login_required()(add_group_handler),
    )
    add_route(
        'PATCH',
        '/internal-api/groups/{group_id}',
        login_required()(update_group_handler),
    )
    add_route(
        'DELETE',
        '/internal-api/groups/{group_id}',
        login_required()(delete_group_handler),
    )
    add_route(
        'POST',
        '/internal-api/groups/{group_id}/members',
        login_required()(add_group_member_handler),
    )
    add_route(
        'POST',
        '/internal-api/groups/{group_id}/members/remove',
        login_required()(delete_group_member_handler),
    )

    # Drafts
    add_route(
        'POST',
        '/internal-api/drafts',
        login_required()(create_draft_create),
    )
    add_route(
        'POST',
        '/internal-api/drafts/{document_id}/version/{version}',
        login_required()(create_draft_from_versioned),
    )
    add_route(
        'POST',
        '/internal-api/drafts/templates/{template_id}',
        login_required()(create_draft_from_template),
    )
    add_route(
        'POST',
        '/internal-api/drafts/{draft_id}/document',
        login_required()(convert_draft_to_document),
    )
    add_route(
        'DELETE',
        '/internal-api/drafts/{draft_id}',
        login_required()(delete_draft),
    )
    add_route(
        'POST',
        '/internal-api/drafts/{draft_id}/version',
        login_required()(convert_draft_to_new_version),
    )
    add_route(
        'GET',
        '/internal-api/drafts/{draft_id}/content',
        sign_session_login_required(get_draft_content),
    )

    # Archive
    add_route(
        'POST',
        '/internal-api/archive/documents',
        login_required()(archive_documents),
    )
    add_route(
        'DELETE',
        '/internal-api/archive/documents',
        login_required()(unarchive_documents),
    )
    add_route(
        'POST',
        '/internal-api/archive/histored-documents',
        login_required()(upload_histored_document),
    )

    # Directories
    add_route(
        'POST',
        '/internal-api/directories',
        login_required()(directories.create_directory),
    )
    add_route(
        'POST',
        '/internal-api/directories/add-to-directory',
        login_required()(directories.add_children_to_directory),
    )
    add_route(
        'PATCH',
        '/internal-api/directories/{directory_id}',
        login_required()(directories.update_directory),
    )
    add_route(
        'POST',
        '/internal-api/delete-directories',
        login_required()(directories.delete_directories),
    )

    # Document categories
    add_route(
        'POST',
        '/internal-api/document-categories',
        login_required()(document_categories_views.create_internal_document_category),
    )
    add_route(
        'DELETE',
        '/internal-api/document-categories/{document_category_id}',
        login_required()(document_categories_views.delete_internal_document_category),
    )
    add_route(
        'PATCH',
        '/internal-api/document-categories/{document_category_id}',
        login_required()(document_categories_views.update_internal_document_category),
    )

    add_route(
        'POST',
        '/internal-api/billing/add-trial-when-employees-limit-reached',
        billing.add_trial_when_employees_limit_reached,
    )
    add_route(
        'POST',
        '/internal-api/billing/add-trial-when-used-paid-feature',
        billing.add_trial_when_used_paid_feature,
    )

    # ======================================
    # Templates
    # ======================================
    # Create template
    add_route(
        'POST',
        '/internal-api/templates',
        login_required()(templates_views.create_template),
    )
    add_route(
        'POST',
        '/internal-api/templates/document',
        login_required()(templates_views.copy_document_as_template_view),
    )
    add_route(
        'POST',
        '/internal-api/templates/upload',
        login_required()(templates_views.upload_template),
    )
    # Delete document template
    add_route(
        'DELETE',
        '/internal-api/templates/{template_id}',
        login_required()(templates_views.template_delete),
    )
    # Update document template
    add_route(
        'PATCH',
        '/internal-api/templates/{template_id}',
        login_required()(templates_views.template_update),
    )
    # Create a copy of document template with same content
    add_route(
        'POST',
        '/internal-api/templates/{template_id}/duplicate',
        login_required()(templates_views.template_duplicate),
    )
    # Add document template to favorites
    add_route(
        'POST',
        '/internal-api/templates/{template_id}/favorite',
        login_required()(templates_views.template_add_to_favorite),
    )
    # Remove document template from favorites
    add_route(
        'DELETE',
        '/internal-api/templates/{template_id}/favorite',
        login_required()(templates_views.template_delete_from_favorite),
    )
    # Get document template preview content (img)
    add_route(
        'GET',
        '/internal-api/templates/{template_id}/preview',
        login_required()(templates_views.template_preview_content),
        name='api.templates.preview_content',
    )

    # Create document from template with specified fields
    add_route(
        'POST',
        '/internal-api/templates/{template_id}/document',
        login_required()(templates_views.create_document_from_template),
    )

    # Document revoke
    app.router.add_route(
        'GET',
        '/internal-api/documents/revoke/{revoke_id}/content',
        sign_session_login_required(get_xml_document_content),
    )
    app.router.add_route(
        'POST',
        '/internal-api/documents/{document_id}/revoke',
        sign_session_login_required(create_document_revoke),
    )
    app.router.add_route(
        'POST',
        '/internal-api/documents/revoke/{revoke_id}/reject',
        sign_session_login_required(reject_document_revoke),
    )
    app.router.add_route(
        'POST',
        '/internal-api/documents/revoke/{revoke_id}/sign',
        sign_session_login_required(sign_document_revoke),
    )
    app.router.add_route(
        'POST',
        '/internal-api/documents/revoke/{revoke_id}/sign/diia',
        sign_session_login_required(diia_request_document_revoke),
    )
    add_route(
        'GET',
        '/internal-api/documents/revoke/{revoke_id}/signature/last',
        sign_session_login_required(last_internal_signature_document_revoke),
    )
    add_route(
        'POST',
        '/internal-api/csat',
        handler=csat_views.add_csat_feedback,
        name='api.csat_add_feedback',
    )


def setup_telegram_routes(app: web.Application) -> None:
    """Setup routes for Telegram bot."""
    add_route = add_route_factory(app)

    telegram_config = services.config.telegram
    telegram_token = telegram_config.token if telegram_config else ''

    # Embed token into route if it is set
    add_route(
        'POST',
        f'/telegram{telegram_token}',
        telegram.dispatcher,
        name='api.telegram.dispatcher',
    )


def setup_landing_routes(app: web.Application) -> None:
    """Setup landing routes.

    Landing is a special view function to handle landing user from external
    site, like Zakupki.prom.ua, to Vchasno project.
    """
    add_route = add_route_factory(app)

    add_route('GET', '/zk/{jwt_token}', landing.zk_landing, name='landing.zk_landing')

    # TODO: move to /internal-api/ path, because it more related to internal API, than to landing
    add_route(
        'POST',
        '/companies/registration/token',
        registration.generate_company_registration_token,
    )

    add_route(
        'POST',
        '/internal-api/check/company',
        login_required()(landing.check_company_internal),
        name='internal.check_company_internal',
    )
    add_route(
        'POST',
        '/internal-api/check/company/upload',
        login_required()(landing.check_company_upload_internal),
        name='internal.check_company_upload_internal',
    )

    # We allow checking if a company is registered in Vchasno for external systems
    add_route(
        'POST',
        '/api/v2/check/company',
        public_api_decorators.api_handler(landing.check_company_internal),
        name='api.check_company_internal',
    )
    add_route(
        'POST',
        '/api/v2/check/company/upload',
        public_api_decorators.api_handler(landing.check_company_upload_internal),
        name='api.check_company_upload_internal',
    )


def setup_sign_session_routes(app: web.Application) -> None:
    """Setup routes for sign session."""
    add_route = add_route_factory(app)

    add_route(
        'GET',
        '/sign-sessions/{sign_session_id}',
        sign_sessions.start,
        name='sign_sessions.start',
    )
    add_route(
        'POST',
        '/sign-sessions/{sign_session_id}/cancel',
        sign_sessions.cancel,
        name='sign_sessions.cancel',
    )
    add_route(
        'POST',
        '/sign-sessions/{sign_session_id}/finish',
        sign_sessions.finish,
        name='sign_sessions.finish',
    )
    add_route(
        'GET',
        '/sign-sessions/{sign_session_id}/{document_id}',
        sign_sessions.ui,
        name='sign_sessions.ui',
    )

    add_route(
        'POST',
        '/sign-sessions',
        login_required()(sign_sessions.create_sign_session),
    )


def setup_static_pages(app: web.Application) -> None:
    """Setup static pages for Vchasno."""
    add_route = add_route_factory(app)
    add_route('GET', '/', landing.index, name='main')
    add_route('GET', '/terms-of-use', static_route('agreement'), name='agreement')
    add_route('GET', '/app{tail:.*}', app_page_handler, name='app')
    add_route(
        'GET',
        '/auth{tail:.*}',
        redirect_to_app(static_route('auth')),
        name='auth',
    )
    add_route(
        'GET',
        '/canceled-subscription',
        static_route('canceled_subscription'),
        name='canceled_subscription',
    )
    add_route(
        'GET',
        '/bill-generation',
        static_route('bill_generation'),
        name='bill_generation',
    )
    add_route(
        'GET',
        '/internal-api/bill-generation/{token}',
        billing.ba_mapping_handler,
        name='ba_mapping_handler',
    )
    add_route(
        'GET',
        '/download-new-browser',
        static_route('download_new_browser'),
        name='download_new_browser',
    )
    add_route('GET', '/invalid-ip', static_route('invalid_ip'), name='invalid_ip')
    add_route(
        'GET',
        '/invalid-token',
        static_route('invalid_token'),
        name='invalid_token',
    )
    add_route('GET', '/landing', static_route('landing'), name='landing')
    add_route(
        method='GET',
        path='/login',
        handler=redirect_route('/auth/login'),
        name='login',
    )
    add_route(
        'GET',
        '/password/recover',
        redirect_route('/auth/password/recover'),
        name='recover_password',
    )
    add_route('GET', '/password/remind', redirect_route('/auth/password/remind'))
    add_route('GET', '/privacy-policy', static_route('privacy'), name='privacy')
    add_route('GET', '/public-offer', static_route('offer'), name='offer')
    add_route('GET', '/rates', landing.rates_redirect, name='rates')
    add_route('GET', '/recoverpass', redirect_route('/auth/password/recover'))
    add_route('GET', '/registration', redirect_route('/auth/registration'))
    add_route('GET', '/remindpass', redirect_route('/auth/password/remind'))
    add_route(
        'GET',
        '/use-of-bonuses',
        static_route('use_of_bonuses'),
        name='use_of_bonuses',
    )
    add_route(
        'GET',
        '/unsubscribe',
        static_route('unsubscribe'),
        name='unsubscribe_page',
    )

    # viewers
    add_route('GET', '/viewer', redirect_route('/pdf-viewer'))
    add_route(
        'GET',
        '/pdf-viewer',
        static_route('pdf_viewer', allow_framed_=True),
        name='pdf_viewer',
    )
    add_route(
        'GET',
        '/xml-viewer',
        static_route('txt_xml_viewer', allow_framed_=True),
        name='xml_viewer',
    )
    add_route(
        'GET',
        '/template-builder',
        static_route('template_builder', allow_framed_=True),
        name='template_builder',
    )
    add_route(
        'GET',
        '/office-viewer',
        static_route('office_viewer', allow_framed_=True),
        name='office_viewer',
    )
    add_route(
        'GET',
        '/txt-viewer',
        static_route('txt_xml_viewer', allow_framed_=True),
        name='txt_viewer',
    )
    add_route(
        'GET',
        '/image-viewer',
        static_route('image_viewer', allow_framed_=True),
        name='image_viewer',
    )
    add_route(
        method='GET',
        path='/activate-trial-from-esputnik-campaign',
        handler=billing.activate_trial_from_esputnik_campaign,
        name='activate_trial_from_esputnik_campaign',
    )


def setup_mobile_routes(app: web.Application) -> None:
    """Setup routes for mobile app."""
    add_route = add_route_factory(app, check_prefix='/mobile-api/v1')

    # Auth
    add_route(
        'POST',
        '/mobile-api/v1/auth/login',
        mobile_views.login,
    )
    add_route(
        'POST',
        '/mobile-api/v1/auth/refresh',
        mobile_views.refresh,
    )
    add_route(
        'POST',
        '/mobile-api/v1/auth/logout',
        mobile_views.logout,
    )
    add_route(
        'POST',
        '/mobile-api/v1/auth/register',
        mobile_views.register,
    )
    add_route(
        'PATCH',
        '/mobile-api/v1/auth/change-registration-email',
        mobile_views.change_registration_email,
    )
    add_route(
        'GET',
        '/mobile-api/v1/auth/check-email-exists',
        mobile_views.check_email_registration,
    )
    add_route(
        'POST',
        '/mobile-api/v1/auth/resend-confirmation-email',
        mobile_views.resend_confirmation_email,
    )
    add_route(
        'POST',
        '/mobile-api/v1/companies/diia',
        mobile_base_login_required()(profile.diia_request),
    )

    # Misc
    add_route(
        'POST',
        '/mobile-api/v1/auth/firebase',
        mobile_views.set_firebase_id,
    )

    # External providers
    add_route(
        method='POST',
        path='/mobile-api/v1/auth/providers/google',
        handler=mobile_views.google_auth,
    )
    add_route(
        method='POST',
        path='/mobile-api/v1/auth/providers/microsoft',
        handler=mobile_views.microsoft_auth,
    )
    add_route(
        method='POST',
        path='/mobile-api/v1/auth/providers/apple',
        handler=mobile_views.apple_auth,
    )

    # Auth.2FA
    add_route(
        'POST',
        '/mobile-api/v1/auth/verify-2fa',
        mobile_views.verify_2fa,
    )
    add_route(
        'POST',
        '/mobile-api/v1/auth/resend-2fa',
        mobile_views.resend_2fa,
    )
    add_route(
        'GET',
        '/mobile-api/v1/auth/2fa/hidden-phone',
        mobile_views.get_hidden_phone,
    )

    # GraphQL
    add_route(
        'POST',
        '/mobile-api/v1/graphql',
        mobile_views.graphql,
    )

    # Document view
    add_route(
        'GET',
        '/mobile-api/v1/downloads/{document_id}',
        mobile_login_required()(downloads.viewer_signed),
    )
    add_route(
        'GET',
        '/mobile-api/v1/downloads/{document_id}/print',
        mobile_login_required()(downloads.viewer_print),
    )
    add_route(
        'GET',
        '/mobile-api/v1/downloads/{document_id}/original',
        mobile_login_required()(downloads.original),
    )
    add_route(
        'GET',
        '/mobile-api/v1/viewer/{document_id}/view',
        mobile_views.view_document,
    )
    add_route(
        'POST',
        '/mobile-api/v1/documents/{document_id}/send',
        mobile_login_required()(documents.send_document),
    )

    # Reviews
    add_route(
        'POST',
        '/mobile-api/v1/reviews',
        mobile_login_required()(reviews.add_review_handler),
    )
    add_route(
        'DELETE',
        '/mobile-api/v1/reviews/{document_id}',
        mobile_login_required()(reviews.delete_review),
    )
    add_route(
        'POST',
        '/mobile-api/v1/reviews-batch',
        mobile_login_required()(reviews.add_reviews_batch_handler),
    )

    # Feedback
    add_route(
        'POST',
        '/mobile-api/v1/feedback',
        mobile_login_required()(feedbacks.add_feedback),
    )

    # Uploads
    add_route(
        'POST',
        '/mobile-api/v1/documents',
        mobile_views.upload,
    )
    add_route(
        'POST',
        '/mobile-api/v1/indexation-status',
        mobile_login_required()(documents.indexing_status),
    )
    add_route(
        'POST',
        '/mobile-api/v1/documents/find-recipients-emails',
        mobile_login_required()(documents.find_recipients_emails),
    )

    # Comments
    add_route(
        'POST',
        '/mobile-api/v1/documents/{document_id}/comments',
        mobile_login_required()(comments.add),
    )

    # Signatures
    add_route(
        'POST',
        '/mobile-api/v1/documents/{document_id}/signatures',
        mobile_login_required()(signatures.add),
    )
    add_route(
        'POST',
        '/mobile-api/v1/documents/{document_id}/reject',
        mobile_login_required()(documents_views.reject),
    )
    add_route(
        'POST',
        '/mobile-api/v1/documents/{document_id}/diia',
        mobile_login_required()(signatures.diia_request),
    )
    add_route(
        'GET',
        '/mobile-api/v1/kep/certificates',
        mobile_login_required()(kep_user_certificates),
    )

    # KEP GraphQL proxy
    add_route(
        'POST',
        '/mobile-api/v1/kep/graphql-proxy',
        mobile_base_login_required()(kep_graphql_proxy),
    )

    # Notifications
    add_route(
        'GET',
        '/mobile-api/v1/notifications',
        mobile_views.list_mobile_notifications,
    )
    add_route(
        'PATCH',
        '/mobile-api/v1/notifications/mark-as-seen',
        mobile_views.mark_all_notifications_as_seen,
    )
    add_route(
        'GET',
        '/mobile-api/v1/notifications/count',
        mobile_views.count_mobile_notifications,
    )
    add_route(
        'PATCH',
        '/mobile-api/v1/notifications/{notification_id}',
        mobile_views.update_mobile_notification_status,
    )

    # HRS mobile proxy routes
    add_route(
        '*',
        '/mobile-api/v1/hrs/proxy/{path:.*}',
        mobile_login_required()(proxy.hrs_mobile_proxy),
    )


def setup_analytics_routes(app: web.Application) -> None:
    """Setup routes for analytics."""
    add_route = add_route_factory(app, check_prefix='/analytics/')

    add_route(
        'GET',
        '/analytics/email/tracking/{token}',
        analytics.email_tracking,
        name='analytics.email.tracking',
    )

    add_route(
        'POST',
        '/analytics/cabinet/event',
        analytics.add_cabinet_event,
        name='analytics.cabinet.event',
    )


def setup_editor(app: web.Application) -> None:
    """Setup editor routes."""
    if not services.config.collabora:
        return

    add_route = add_route_factory(app, check_prefix='/editor')

    add_route(
        'POST',
        '/editor/',
        editor.initiate_edit_session,
    )

    # WOPI routes
    add_route('GET', '/editor/session/{id}', editor.file_metadata, name='editor.get_file')
    add_route(
        'GET',
        '/editor/session/{id}/contents',
        editor.file_content,
    )
    add_route(
        'POST',
        '/editor/session/{id}/contents',
        editor.file_save,
    )
