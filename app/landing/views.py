from http import HTTPStatus

from aiohttp import web

from app.auth import concierge
from app.auth.constants import USER_APP_KEY
from app.auth.db import is_user_email_exists, select_base_user, select_roles
from app.auth.decorators import redirect_to_app
from app.auth.types import BaseUser, User
from app.contacts.constants import ACCOUNTANT_EMAIL_KEY
from app.contacts.sync.utils import fetch_contacts
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.landing.schemas import Landing<PERSON>ur<PERSON>UserR<PERSON>ponse, LandingRoleResponse
from app.landing.utils import check_company, check_company_upload
from app.landing.validators import (
    CheckCompanySchema,
    IpSchema,
    validate_company_check_upload,
    validate_zk_landing,
)
from app.lib import utm_params, validators
from app.lib.datetime_utils import ONE_DAY_DELTA
from app.lib.helpers import validate_rate_limit
from app.lib.integrations import private_integration
from app.lib.redirects import redirect, redirect_outside
from app.lib.static import static_route
from app.openapi.decorators import openapi_docs, openapi_docs_ignore
from app.services import services
from app.tokens.utils import generate_jwt_token

landing_static = static_route('landing')


@redirect_to_app
async def index(request: web.Request) -> web.StreamResponse:
    """Redirect to landing URL or return landing page HTML from static files"""

    # Ugly, temporary hack to redirect to auth page when landing is broken
    if get_flag(FeatureFlags.ENABLE_REDIRECT_TO_AUTH_FOR_LANDING):
        return redirect_outside('https://edo.vchasno.ua/auth/')

    landing_url: str | None = services.config.app.landing_url
    if landing_url:
        return redirect_outside(landing_url)

    # return landing.html from static files
    return await landing_static(request)


async def rates_redirect(_: web.Request) -> web.StreamResponse:
    """Redirect to rates page hosted outside the service."""
    return redirect_outside(services.config.app.rates_url)


@redirect_to_app
async def zk_landing(request: web.Request) -> web.Response:
    """Land user from Zakupki profile to Vchasno.

    If EDRPOU already has user registered at Vchasno - redirect to login page,
    if not and EDRPOU exists in Zakupki API - redirect to registration page.

    In case of wrong JWT token from ZK or empty result from ZK API - redirect
    to main page.
    """
    async with request.app['db'].acquire() as conn:
        payload = await validate_zk_landing(request, conn, request.match_info['jwt_token'])

    client = request.app['client_session']
    config = services.config
    sync_config = config.sync_contacts_api
    if not sync_config:
        return redirect('main')

    zk_api = sync_config.e40283641
    contacts = await fetch_contacts(
        client=client,
        url=zk_api.url,
        chunk=[payload['edrpou']],
        config=zk_api.model_dump(),
    )
    if not contacts:
        return redirect('main')

    async with request.app['db'].acquire() as conn:
        for contact in contacts:
            email = contact.get(ACCOUNTANT_EMAIL_KEY)
            if email and not await is_user_email_exists(conn, email):
                payload['email'] = email
                break

    get_params = utm_params.pass_utm_params(
        request,
        default=utm_params.ZK_LANDING,
        token=generate_jwt_token(payload, config.tokens.private_key),
    )

    return redirect('auth', get=get_params, tail='/registration')


async def check_company_internal(request: web.Request, user: User) -> web.Response:
    """
    Check if edrpou presents in a system

    NOTE: This handler is actually not related to the landing page because we use it for making
    request from our frontend and third-party system via public API. But it reuses the same logic
    as the landing page handler and is placed in the same file for convenience.
    """
    valid_data = validators.validate_pydantic(
        CheckCompanySchema, await validators.validate_json_request(request)
    )
    return await check_company(request, valid_data.edrpou, user)


async def check_company_upload_internal(request: web.Request, user: User) -> web.Response:
    """
    Get information about companies from a list of edrpou sourced from a file

    NOTE: This handler is actually not related to the landing page because we use it for making
    request from our frontend and third-party system via public API. But it reuses the same logic
    as the landing page handler and is placed in the same file for convenience.
    """

    upload_size: int = request.content_length or 0
    validated = validate_company_check_upload(
        data=await validators.validate_post_request(request), content_length=upload_size
    )
    return await check_company_upload(request, validated)


@openapi_docs_ignore('todo: add docs')
@private_integration(integration='landing')
async def check_company_integration(request: web.Request) -> web.Response:
    """
    Check if edrpou presents in system
    Copy function 'check_company_internal()', with a different authorization system
    Used for landing wordpress integration
    """
    user: User | BaseUser | None = request[USER_APP_KEY]

    payload = await validators.validate_json_request(request)

    client_ip = (validators.validate_pydantic(IpSchema, payload)).ip
    await validate_rate_limit(
        key=f'landing:check_company:{client_ip}',
        limit=30,
        delta=ONE_DAY_DELTA,
    )
    valid_data = validators.validate_pydantic(CheckCompanySchema, payload)

    return await check_company(request, valid_data.edrpou, user)


@openapi_docs_ignore('todo: add docs')
@private_integration(integration='landing')
async def check_company_upload_integration(request: web.Request) -> web.Response:
    """
    Get information about companies from list of edrpou sourced from file
    Copy function 'check_company_upload_internal()', with different authorization system
    Used for landing wordpress integration
    """
    upload_size: int = request.content_length or 0

    payload = await validators.validate_post_request(request)
    client_ip = (validators.validate_pydantic(IpSchema, payload)).ip
    await validate_rate_limit(
        key=f'landing:check_company_upload:{client_ip}',
        limit=3,
        delta=ONE_DAY_DELTA,
    )

    validated = validate_company_check_upload(data=payload, content_length=upload_size)
    return await check_company_upload(request, validated)


@openapi_docs(
    summary=_('Отримати інформацію про користувача'),
    tags=['landing'],
)
@private_integration(integration='landing')
async def get_current_user(request: web.Request) -> web.Response:
    """
    This endpoint is used to get the current user's email and roles. It should be requested
    by the landing page backend (WordPress) to get the user info for the current user.
    The current user is determined by the concierge cookie attached to the request.

    1. The landing page frontend makes request to the WordPress backend to get the current user
       https://vchasno.ua -> https://vchasno.ua/wp-admin/admin-ajax.php?action=...

    2. Browser attaches to this request "vchasno_auth" cookie (concierge cookie) because cookie
       is set for the .vchasno.ua domain (root and all subdomains)

    3. The WordPress backend makes request to the EDO backend with integration token and
       the concierge cookie attached:
       GET /api/integrations/landing/current-user HTTP/1.1
       X-Integration-Token: <token>
       Cookie: vchasno_auth=<cookie>

    4. Nginx performs "auth_request" to concierge frontend server and attaches the auth headers
       to the incoming request

    5. The EDO backend receives the request with the auth headers and extracts the user info
       from the headers

    Flow:
     → Landing page → WordPress backend → Nginx (with Concierge) → EDO backend
    """

    # This endpoint should be included in the list of allowed paths in the nginx config
    # for concierge to pass the auth headers to the backend
    concierge_user = concierge.get_concierge_user()
    if not concierge_user or not concierge_user.is_authenticated or not concierge_user.user_id:
        return web.Response(status=HTTPStatus.NO_CONTENT)

    async with services.db.acquire() as conn:
        user = await select_base_user(conn, user_id=concierge_user.user_id)
        if not user:
            return web.Response(status=HTTPStatus.NO_CONTENT)

        roles = await select_roles(conn, user_id=user.id, only_active=True)

    response = LandingCurrentUserResponse(
        email=user.email,
        roles=[
            LandingRoleResponse(
                company_edrpou=role.company_edrpou,
                company_name=role.company_name,
                is_last=role.id == user.last_role_id,
            )
            for role in roles
        ],
    )
    return web.json_response(response.to_api(), status=HTTPStatus.OK)
