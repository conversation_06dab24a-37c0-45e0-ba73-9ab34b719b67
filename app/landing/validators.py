import csv

import jwt
import pydantic
from aiohttp import web
from aiohttp.web_request import FileField

from api.errors import Code, Error
from app.auth.db import exists_company_by_edrpou
from app.landing.types import ValidatedDataCompanyCheck
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection
from app.lib.helpers import (
    csv_define_delimiter,
    csv_detect_encoding,
    get_file_extension,
)
from app.lib.redirects import redirect
from app.lib.types import (
    DataDict,
    DataMapping,
)
from app.lib.validators import validate_edrpou
from app.lib.xlsx import XLSXReader
from app.services import services
from app.uploads.constants import MB


class CheckCompanySchema(pydantic.BaseModel):
    edrpou: pv.EDRPOU


class IpSchema(pydantic.BaseModel):
    ip: pv.IP


async def validate_zk_landing(request: web.Request, conn: DBConnection, token: str) -> DataDict:
    """Validate JWT token from Zakupki.

    And check that EDRPOU has not yet registered.
    """
    config = services.config.sync_contacts_api
    if not config:
        raise redirect('main')

    key = config.e40283641.secret_key
    main_redirect = redirect('main')

    try:
        payload = dict(jwt.decode(token, key))
    except (ValueError, jwt.InvalidTokenError):
        raise main_redirect

    edrpou = payload.get('edrpou')
    if not edrpou:
        raise main_redirect

    if await exists_company_by_edrpou(conn, edrpou):
        raise redirect('auth', tail='/login')

    return payload


def _validate_csv_file(valid_file: FileField) -> ValidatedDataCompanyCheck:
    # Counters for feedback
    count_rows = 0
    empty_rows = 0
    invalid_row_numbers = []
    has_header = False

    # Valid data
    edrpous = set()

    # Detect encoding
    file_content = valid_file.file.read()
    encoding = csv_detect_encoding(file_content)
    file_data = file_content.decode(encoding)
    delimiter = csv_define_delimiter(file_data)

    csv_reader = csv.reader(file_data.splitlines(), delimiter=delimiter)
    for count_rows, row in enumerate(csv_reader, 1):
        # Ignore empty lines, generated on Mac during Excel -> CSV conversion.
        if not any(row):
            empty_rows += 1
            continue

        # skip first row if edrpou value is header
        if count_rows == 1 and validators.validate_edrpou(row[0]) is None:
            has_header = True
            continue

        edrpou = validate_edrpou(row[0])

        if not edrpou:
            invalid_row_numbers.append(count_rows)
            continue

        edrpous.add(edrpou)

    if not edrpous:
        raise Error(Code.empty_upload_csv)

    return ValidatedDataCompanyCheck(
        edrpous,
        rows_total=count_rows - has_header - empty_rows,
        rows_invalid=len(invalid_row_numbers),
        invalid_row_numbers=invalid_row_numbers,
    )


def _validate_xlsx_file(valid_file: FileField) -> ValidatedDataCompanyCheck:
    # Counters for feedback
    count_rows = 0
    empty_rows = 0
    invalid_row_numbers = []
    has_header = False

    # Valid data
    edrpous = set()

    # Detect encoding
    file_content = valid_file.file.read()

    reader = XLSXReader(file=file_content)
    for count_rows, row in enumerate(reader, 1):
        raw_value = row[0].value

        # Excel may have empty rows (usually in the end of file see DOC-4461)
        if raw_value is None:
            invalid_row_numbers.append(count_rows)
            continue

        # numbers are converted to float
        try:
            value = str(int(raw_value))
        except ValueError:
            value = str(raw_value)

        # skip first row if edrpou value is header
        if count_rows == 1 and validators.validate_edrpou(value) is None:
            has_header = True
            continue

        edrpou = validate_edrpou(value)

        if not edrpou:
            invalid_row_numbers.append(count_rows)
            continue

        edrpous.add(edrpou)

    if not edrpous:
        raise Error(Code.empty_upload_csv)

    return ValidatedDataCompanyCheck(
        edrpous,
        rows_total=count_rows - has_header - empty_rows,
        rows_invalid=len(invalid_row_numbers),
        invalid_row_numbers=invalid_row_numbers,
    )


def validate_client_ip(data: DataMapping) -> str:
    return validators.validate_ip(data.get('ip', ''))


def validate_company_check_upload(
    data: DataMapping, content_length: int
) -> ValidatedDataCompanyCheck:
    allowed_extensions = ['.csv', '.xlsx']
    max_file_size = 5

    file_item = data.get('file')

    if not (
        isinstance(file_item, FileField)
        and get_file_extension(file_item.filename) in allowed_extensions
    ):
        raise Error(
            Code.invalid_file_extension,
            details={'allowed_extensions': ', '.join(allowed_extensions)},
        )

    if content_length / MB > max_file_size:
        raise Error(Code.max_file_size, details={'max_file_size': max_file_size})

    file_extension = get_file_extension(file_item.filename)

    if file_extension == '.csv':
        return _validate_csv_file(file_item)
    if file_extension == '.xlsx':
        return _validate_xlsx_file(file_item)

    raise NotImplementedError
