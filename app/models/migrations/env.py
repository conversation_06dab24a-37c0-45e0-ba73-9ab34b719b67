import logevo

with logevo.main_context():
    import logging

    from alembic import context
    from alembic.script.revision import ResolutionError
    from alembic.util import CommandError
    from sqlalchemy import engine_from_config, pool

    # this is the Alembic Config object, which provides
    # access to the values within the .ini file in use.
    from app.config import read_app_config

    # Import model metadata and use it as target metadata for revisions
    from app.models import metadata

    # Import all models used in Vchasno project
    from api.private.super_admin import tables
    from api.downloads import tables
    from app.actions import tables
    from app.auth import tables
    from app.banner import tables
    from app.billing import tables
    from app.comments import tables
    from app.contacts import tables
    from app.document_antivirus import tables
    from app.document_automation import tables
    from app.document_versions import tables
    from app.documents import tables
    from app.documents_fields import tables
    from app.documents_required_fields import tables
    from app.esputnik import tables
    from app.flow import tables
    from app.mobile import tables
    from app.notifications import tables
    from app.registration import tables
    from app.registration import tables
    from app.reviews import tables
    from app.sign_sessions import tables
    from app.signatures import tables
    from app.tags import tables
    from app.tokens import tables
    from app.feedbacks import tables
    from app.document_categories import tables
    from app.trigger_notifications import tables
    from app.groups import tables
    from app.analytics import tables
    from app.drafts import tables
    from app.delayed_task import tables
    from app.archive import tables
    from app.documents_ai import tables
    from app.templates import tables
    from app.directories import tables
    from app.document_revoke import tables
    from app.csat import tables

    logger = logging.getLogger('alembic migrations')

    config = context.config

    # When migrating testing databases, we need to render the DB URL.
    test_db_num = 0
    if getattr(context.config.cmd_opts, 'x', None):
        x_opts = config.cmd_opts.x
        if len(x_opts) == 1 and x_opts[0].startswith('db_num='):
            test_db_num = x_opts[0][len('db_num=') :]

    app_config_file = config.get_main_option('config_file')
    app_config = read_app_config(public_filename=app_config_file, test_db_num=test_db_num)

    # Read `sqlalchemy.url` from app config
    db_url = app_config.db.url
    config.set_main_option('sqlalchemy.url', db_url)

    target_metadata = metadata

    # other values from the config, defined by the needs of env.py,
    # can be acquired:
    # my_important_option = config.get_main_option("my_important_option")
    # ... etc.

    def include_object(object, name, type_, reflected, compare_to):
        # Ignore hermes_* tables in migration
        if type_ == 'table' and name.startswith('hermes_'):
            return False

        # Ignore old tables (del after dropping tables from DB)
        if type_ == 'table' and name in (
            'user_actions_2022',
            'user_actions',
            'document_actions',
            'document_actions_reports',
            'temp_s3_migration',
        ):
            return False

        # Ignore pro_actions table in migration
        if type_ == 'table' and name == 'pro_actions':
            return False

        # Temporary ignore signatures.key and signatures.stamp columns in migration
        # https://tabula-rasa.atlassian.net/browse/DOC-5997
        if type_ == 'column' and name in ('key', 'stamp') and object.table.name == 'signatures':
            return False

        return True

    def _run_migrations(_context):
        with _context.begin_transaction():
            try:
                _context.run_migrations()
            except CommandError as e:
                if isinstance(e.__cause__, ResolutionError):
                    logger.exception('Suppressed alembic migration error')
                else:
                    raise e

    def run_migrations_offline():
        """Run migrations in 'offline' mode.

        This configures the context with just a URL
        and not an Engine, though an Engine is acceptable
        here as well.  By skipping the Engine creation
        we don't even need a DBAPI to be available.

        Calls to context.execute() here emit the given string to the
        script output.

        """
        url = config.get_main_option('sqlalchemy.url')
        context.configure(
            url=url,
            target_metadata=target_metadata,
            literal_binds=True,
            include_schemas=True,
            include_object=include_object,
        )
        _run_migrations(context)

    def run_migrations_online():
        """Run migrations in 'online' mode.

        In this scenario we need to create an Engine
        and associate a connection with the context.

        """

        # Set
        # - lock_timeout to 3 seconds
        # - statement_timeout to 60 seconds
        connectable = engine_from_config(
            config.get_section(config.config_ini_section),
            prefix='sqlalchemy.',
            poolclass=pool.NullPool,
        )

        with connectable.connect() as connection:
            connection.execute('set lock_timeout to 3000;')
            connection.execute('set statement_timeout to 60000;')
            context.configure(
                connection=connection,
                target_metadata=target_metadata,
                compare_type=True,  # notice column type changing
                include_schemas=True,
                transaction_per_migration=True,
                include_object=include_object,
            )
            _run_migrations(context)

    if context.is_offline_mode():
        run_migrations_offline()
    else:
        run_migrations_online()
