"""[vchasno_users was removed]

Revision ID: 00a1ad547bb2
Revises: 6f8bc6aaa0f4
Create Date: 2024-12-05 14:28:16.596384

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '00a1ad547bb2'
down_revision = '6f8bc6aaa0f4'
branch_labels = None
depends_on = None


def upgrade():
    pass


def downgrade():
    pass
