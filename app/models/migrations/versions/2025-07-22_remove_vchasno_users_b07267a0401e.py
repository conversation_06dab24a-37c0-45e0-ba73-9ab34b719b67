"""Remove vchasno_users table

Revision ID: b07267a0401e
Revises: a46b6935081a
Create Date: 2025-07-22 19:14:26.857559

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b07267a0401e'
down_revision = 'a46b6935081a'
branch_labels = None
depends_on = None


def upgrade():
    op.execute('DROP TABLE IF EXISTS vchasno_users;')

def downgrade():
    pass