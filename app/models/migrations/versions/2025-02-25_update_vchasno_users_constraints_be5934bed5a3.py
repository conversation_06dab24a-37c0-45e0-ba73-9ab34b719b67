"""[vchasno_users was removed]

Revision ID: be5934bed5a3
Revises: 452f7b76091f
Create Date: 2025-02-25 14:08:16.401696

"""

import logging

from alembic import op
import sqlalchemy as sa

from app.config import get_level

# revision identifiers, used by Alembic.
revision = 'be5934bed5a3'
down_revision = '452f7b76091f'
branch_labels = None
depends_on = None


def upgrade():
    pass


def downgrade():
    pass
