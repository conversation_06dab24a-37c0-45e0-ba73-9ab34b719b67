"""[vchasno_users was removed]

Revision ID: 10e2b593a612
Revises: e930b67ab97d
Create Date: 2024-11-26 18:38:57.888523

"""

import citext
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '10e2b593a612'
down_revision = 'e930b67ab97d'
branch_labels = None
depends_on = None


def upgrade():
    pass

def downgrade():
    pass
