"""add_csat_feedback_table

Revision ID: 8675a73bebad
Revises: a46b6935081a
Create Date: 2025-07-24 00:07:10.407263

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app.csat.enums import CsatActionType
from app.models.types import SoftEnum

# revision identifiers, used by Alembic.
revision = '8675a73bebad'
down_revision = 'a46b6935081a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'csat_surveys',
        sa.Column(
            'id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False
        ),
        sa.Column('user_id', sa.String(length=64), nullable=False),
        sa.Column('company_id', postgresql.UUID(), nullable=False),
        sa.Column('type', SoftEnum(CsatActionType), nullable=False),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column('estimate', sa.SmallInteger(), nullable=True),
        sa.Column('feedback', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='NO ACTION'),
        sa.PrimaryKeyConstraint('id'),
    )
    op.create_index(
        'index_csat_user_id',
        'csat_surveys',
        ['user_id', sa.text('date_created DESC')],
        unique=False,
    )
    op.create_index(op.f('ix_csat_surveys_user_id'), 'csat_surveys', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_csat_surveys_user_id'), table_name='csat_surveys')
    op.drop_index('index_csat_user_id', table_name='csat_surveys')
    op.drop_table('csat_surveys')
    # ### end Alembic commands ###
