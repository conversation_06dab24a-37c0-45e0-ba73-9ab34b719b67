import pydantic

import app.lib.validators_pydantic as pv
from app.auth.types import User
from app.lib import validators
from app.lib.database import DBConnection
from app.lib.types import DataDict
from app.trigger_notifications.db import select_trigger_notifications
from app.trigger_notifications.enums import TriggerNotificationStatus
from app.trigger_notifications.types import UpdateTriggerNotificationsOptions


class UpdateTriggerNotificationsSchema(pydantic.BaseModel):
    ids: pv.SoftList[pv.UUID]


async def validate_update_trigger_notifications(
    conn: DBConnection, raw_data: DataDict, user: User
) -> UpdateTriggerNotificationsOptions:
    """
    Validate ability to update trigger notifications.
    Note: This validator just ignore bad values instead of raising an error, because
    an update works for multiple notifications and one bad notification can not be
    a barrier for updating others.
    """
    data = validators.validate_pydantic(UpdateTriggerNotificationsSchema, raw_data)
    ids = data.ids

    # currently support update notification status to seen
    status = TriggerNotificationStatus.seen

    notifications = await select_trigger_notifications(
        conn=conn, ids=ids, status=TriggerNotificationStatus.new, role_id=user.role_id
    )
    new_ids = [notification.id for notification in notifications]
    return UpdateTriggerNotificationsOptions(ids=new_ids, status=status)
