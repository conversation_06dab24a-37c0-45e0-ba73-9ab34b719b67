import typing as t

import pydantic
from aiohttp import web

from api.errors import InvalidRequest
from app.auth.types import User
from app.cloud_signer.types import SignHashCtx
from app.documents.utils import get_document_hash
from app.i18n import _
from app.lib import validators
from app.lib import validators_pydantic as pv


class CloudSignerCreateSessionSchema(pydantic.BaseModel):
    client_id: pv.UUID
    duration: t.Annotated[int, pydantic.Field(ge=60, le=30 * 24 * 3600)]  # 1 minute to 30 days
    use_refresh_token: pv.BoolNullToFalse = False


class CloudSignerCheckSessionSchema(pydantic.BaseModel):
    auth_session_id: str


class CloudSignerRefreshSessionSchema(pydantic.BaseModel):
    auth_session_id: str
    refresh_token: str


class CloudSignerSignHashSchema(pydantic.BaseModel):
    auth_session_token: str | None = None
    access_token: str | None = None
    client_id: pv.UUID
    document_id: pv.UUID
    password: str


async def validate_cloud_signer_ips_whitelist(request: web.Request, user: User) -> None:
    # TODO validate IP (X-Forwarded-For header) (DOC-4940)
    return None


async def validate_sessions_create(
    request: web.Request, user: User
) -> CloudSignerCreateSessionSchema:
    data = await validators.validate_json_request(request)

    await validate_cloud_signer_ips_whitelist(request, user)

    return validators.validate_pydantic(CloudSignerCreateSessionSchema, data)


async def validate_sessions_refresh(
    request: web.Request, user: User
) -> CloudSignerRefreshSessionSchema:
    data = await validators.validate_json_request(request)

    await validate_cloud_signer_ips_whitelist(request, user)

    return validators.validate_pydantic(CloudSignerRefreshSessionSchema, data)


async def validate_sessions_check(
    request: web.Request, user: User
) -> CloudSignerCheckSessionSchema:
    data = await validators.validate_json_request(request)

    await validate_cloud_signer_ips_whitelist(request, user)

    return validators.validate_pydantic(CloudSignerCheckSessionSchema, data)


async def validate_sessions_sign_hash(request: web.Request, user: User) -> SignHashCtx:
    data = await validators.validate_json_request(request)

    await validate_cloud_signer_ips_whitelist(request, user)

    schema = validators.validate_pydantic(CloudSignerSignHashSchema, data)
    document_id = schema.document_id

    document_hash = await get_document_hash(user, document_id)

    if auth_session_token := schema.auth_session_token:
        token = auth_session_token
        headers = {'X-Cloud-Signer-AuthSession': auth_session_token}
    elif access_token := schema.access_token:
        token = access_token
        headers = {'X-Cloud-Signer-AccessAuthSession': access_token}
    else:
        raise InvalidRequest(
            reason=_(
                'При підписанні документа потрібно вказати auth_session_token або access_token'
            ),
        )

    return SignHashCtx(
        document_id=document_id,
        data={
            'authSessionToken': token,
            'clientId': schema.client_id,
            'password': schema.password,
            'hash': document_hash,
        },
        headers=headers,
    )
