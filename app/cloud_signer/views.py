import logging
from http import HTTPStatus

from aiohttp import web

from api.public.decorators import cloud_signer_api_handler
from app.auth.types import User
from app.cloud_signer import utils, validators

logger = logging.getLogger(__name__)


@cloud_signer_api_handler
async def cloud_signer_sessions_create(request: web.Request, user: User) -> web.Response:
    validated_data = await validators.validate_sessions_create(request, user)
    data = {
        'clientId': validated_data.client_id,
        'duration': validated_data.duration,
        'useRefreshToken': validated_data.use_refresh_token,
    }
    resp = await utils.cloud_signer_encrypted_request(
        request,
        url_path='/api/sessions/create',
        data=data,
    )
    return web.json_response(resp)


@cloud_signer_api_handler
async def cloud_signer_sessions_refresh(request: web.Request, user: User) -> web.Response:
    validated_data = await validators.validate_sessions_refresh(request, user)
    data = {
        'authSessionId': validated_data.auth_session_id,
        'refreshToken': validated_data.refresh_token,
    }
    resp = await utils.cloud_signer_encrypted_request(
        request=request,
        url_path='/api/sessions/refresh',
        data=data,
    )
    return web.json_response(resp)


@cloud_signer_api_handler
async def cloud_signer_sessions_refresh_check(request: web.Request, user: User) -> web.Response:
    validated_data = await validators.validate_sessions_check(request, user)
    data = {'authSessionId': validated_data.auth_session_id}
    resp = await utils.cloud_signer_encrypted_request(
        request=request,
        url_path='/api/sessions/refresh/check',
        data=data,
    )
    return web.json_response(resp)


@cloud_signer_api_handler
async def cloud_signer_sessions_check(request: web.Request, user: User) -> web.Response:
    validated_data = await validators.validate_sessions_check(request, user)
    data = {'authSessionId': validated_data.auth_session_id}
    resp = await utils.cloud_signer_encrypted_request(
        request,
        url_path='/api/sessions/check',
        data=data,
    )
    return web.json_response(resp)


@cloud_signer_api_handler
async def cloud_signer_sessions_sign_document(request: web.Request, user: User) -> web.Response:
    ctx = await validators.validate_sessions_sign_hash(request, user)

    log_extra = {
        'user_id': user.id,
        'role_id': user.role_id,
        'company_id': user.company_id,
        'document_id': ctx.document_id,
    }
    logger.info('Cloud signer session sign-document request', extra=log_extra)

    resp = await utils.cloud_signer_encrypted_request(
        request,
        url_path='/api/sessions/sign-hash',
        data=ctx.data,
        headers=ctx.headers,
    )
    signature = resp.get('signature')
    if signature:
        await utils.add_cloud_signer_signature(
            request,
            user,
            document_id=ctx.document_id,
            signature=signature,
        )
        return web.json_response(status=HTTPStatus.CREATED)

    logger.info(
        'Cloud signer session sign-document error',
        extra={
            **log_extra,
            'error_code': resp.get('errorCode'),
        },
    )
    return web.json_response(text='Cloud sign error', status=HTTPStatus.BAD_REQUEST)
