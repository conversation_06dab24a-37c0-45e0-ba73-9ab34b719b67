from __future__ import annotations

import logging
import sys
import typing as t
from collections import defaultdict
from collections.abc import Iterable
from dataclasses import dataclass
from datetime import datetime, timedelta
from operator import eq, ge, lt, ne

from elasticmagic import Bool, Exists, Field, Nested, Sort
from elasticmagic.attribute import AttributedField
from elasticmagic.expression import Prefix, QueryExpression
from elasticmagic.ext.asyncio import AsyncSearchQuery
from hiku.types import Mapping

from api.graph.constants import (
    DEFAULT_DOCUMENTS_DIRECTION,
    DEFAULT_DOCUMENTS_ORDER,
    DEFAULT_DOCUMENTS_SORT_DATE,
)
from api.graph.low_level.utils import _parse_new_condition
from api.public.types import ListDocumentsOptions, ListIncomingDocumentsOptions
from app.auth.types import AuthUser, User
from app.auth.utils import (
    can_user_view_all_company_documents,
    is_admin,
)
from app.banner.enums import BannerActivityPeriod
from app.directories.schemas import DirectoriesListSearchOptions
from app.document_categories.types import PublicDocumentCategory
from app.documents.enums import DocumentAccessLevel, DocumentReviewState, FirstSignBy
from app.documents_fields.enums import DocumentFieldType
from app.documents_fields.types import DocumentsField
from app.es.constants import (
    ES_SLOW_DURATION_LIMIT,
    INDEXATOR_ARCHIVE_KEY,
    INDEXATOR_KEY,
    INDEXATOR_LISTING_KEY,
    INDEXATOR_SLOW_KEY,
    INDEXATOR_TEMP_KEY,
)
from app.es.enums import ESQuerySource
from app.es.models.document import Document, Viewer
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.groups.db import select_group_members
from app.lib import tracking
from app.lib.datetime_utils import (
    parse_local_datetime,
    to_utc_datetime,
    utc_now,
)
from app.lib.enums import (
    DocumentFolder,
    DocumentListOrder,
    DocumentListSortDate,
    DocumentStatus,
    ListDirection,
    enum_values,
)
from app.lib.es import AsyncIndex
from app.lib.helpers import to_json
from app.lib.types import DataDict, Redis
from app.lib.validators import validate_left_datetime, validate_right_datetime
from app.services import services

DocumentSearchParameter = tuple[DocumentsField, str]
DocumentSearchParameters = list[DocumentSearchParameter]

logger = logging.getLogger(__name__)

UPLOADED = DocumentStatus.uploaded.value
READY_TO_BE_SIGNED = DocumentStatus.ready_to_be_signed.value
SENT = DocumentStatus.sent.value
SIGNED = DocumentStatus.signed.value
SIGNED_AND_SENT = DocumentStatus.signed_and_sent.value
REJECT = DocumentStatus.reject.value
REVOKED = DocumentStatus.revoked.value
APPROVED = DocumentStatus.approved.value
FINISHED = DocumentStatus.finished.value
FLOW = DocumentStatus.flow.value
HISTORED = DocumentStatus.histored.value

FIRST_STATUSES = [UPLOADED, READY_TO_BE_SIGNED, SENT, SIGNED]
LAST_STATUSES = [SIGNED_AND_SENT, REJECT, APPROVED, FINISHED, FLOW, REVOKED]

FIRST_SIGN_BY_OWNER = Document.first_sign_by == FirstSignBy.owner.value
FIRST_SIGN_BY_RECIPIENT = Document.first_sign_by == FirstSignBy.recipient.value

FUZZY_PARAMETERS = {'fuzziness': 'auto', 'minimum_should_match': '100%'}


has_recipient_edrpou = Bool.must_not(eq(Document.recipient_edrpou, None))
is_not_finished_or_rejected = Bool.must_not(Document.status.in_([FINISHED, REJECT, REVOKED]))
is_waiting_for_sign_by_owner = Document.pending_owner_signatures != 0
is_waiting_for_sign_by_recipient = Document.pending_recipient_signatures != 0
with_recipient_edrpou_or_internal = Bool.should(
    has_recipient_edrpou,
    eq(Document.is_internal, True),
)
is_current_flow_filter = Document.is_current_flow.term(True)


def _build_viewer_dates_filter(user: AuthUser | User) -> QueryExpression:
    """
    Build a filter for accessing document viewing dates based on user roles and permissions.

    Handles different document visibility rules for private and extended documents.
    """

    # That viewer_dates without role_ids contains date when company get first access to document
    _company_access = ~Exists(Document.viewer_dates.role_ids)

    # Date when current role ID get access to document
    _role_access = Document.viewer_dates.role_ids.term(user.role_id)

    # For the most privileged users, we show date when company get access to document
    if is_admin(user.user_role) or can_user_view_all_company_documents(user):
        return _company_access

    # For the least privileged users, we show date when current role ID get access to document
    if not user.can_view_document and not user.can_view_private_document:
        return _role_access

    is_private_filter = Document.is_private.term(True)
    is_extended_filter = Bool.should(Document.is_private.term(False), Document.is_private.missing())

    if not user.can_view_document and user.can_view_private_document:
        return Bool.should(
            # Priority: company access for private documents
            Bool.must(is_private_filter, _company_access),
            # Second priority: direct access ONLY for documents that don't qualify above
            Bool.must(is_extended_filter, _role_access),
        )

    if user.can_view_document and not user.can_view_private_document:
        return Bool.should(
            # Priority: company access for non-private documents
            Bool.must(is_extended_filter, _company_access),
            # Second priority: direct access ONLY for documents that don't qualify above
            Bool.must(is_private_filter, _role_access),
        )

    # Should never reach here as all permission combinations are covered
    logger.info(
        msg='Unexpected user permissions in _build_viewer_dates_filter',
        extra={
            'user_email': user.email,
            'user_role': user.user_role,
            'user_can_view_document': user.can_view_document,
            'user_can_view_private_document': user.can_view_private_document,
        },
    )
    raise ValueError('Unexpected user permissions in _build_viewer_dates_filter')


def build_access_filter(user: AuthUser | User) -> Bool:
    filters = [Document.access_edrpou == user.company_edrpou]

    # ===== Access to all company documents =====
    # Admin can see all documents in the company without any limitations
    # User has all permissions required to see all documents in the company, like admin
    if can_user_view_all_company_documents(user):
        return Bool.must(*filters)

    # ==== Limited access to documents ====
    direct_access_filter = Document.viewer_roles == user.role_id

    can_view_extended = user.can_view_document
    can_view_private = user.can_view_private_document

    # User can see only documents with direct access
    if not can_view_extended and not can_view_private:
        filters.append(direct_access_filter)
        return Bool.must(*filters)

    # User that can view all private + documents with direct access
    if not can_view_extended and can_view_private:
        filters.append(
            Bool.should(
                direct_access_filter,
                Document.is_private.term(True),
            )
        )
        return Bool.must(*filters)

    # User can view all extended (non-private) documents + documents with direct access
    if can_view_extended and not can_view_private:
        filters.append(
            Bool.should(
                direct_access_filter,
                Document.is_private.term(False),
                # by default document is not private, so when field not exists (older documents),
                # we assume that document is not private
                Document.is_private.missing(),
            )
        )
        return Bool.must(*filters)

    # If we reach this point, it means that something unexpected happened because
    # we have already checked all possible combinations of user permissions
    logger.info(
        msg='Unexpected user permissions',
        extra={
            'user_email': user.email,
            'user_role': user.user_role,
            'user_can_view_document': can_view_extended,
            'user_can_view_private_document': can_view_private,
        },
    )
    raise ValueError('Unexpected user permissions')


def build_is_document_one_sign_filter() -> Bool:
    return Bool.should(
        Bool.must(FIRST_SIGN_BY_OWNER, Document.expected_recipient_signatures == 0),
        Bool.must(FIRST_SIGN_BY_RECIPIENT, Document.expected_owner_signatures == 0),
    )


def build_is_document_multi_sign_filter() -> Bool:
    return Bool.should(
        Bool.must(FIRST_SIGN_BY_OWNER, Document.expected_recipient_signatures != 0),
        Bool.must(FIRST_SIGN_BY_RECIPIENT, Document.expected_owner_signatures != 0),
    )


def build_all_signatures_filter(has_all_signatures: bool) -> Bool:
    """
    Building filter for filtering documents that has all signatures from
    owner or all from contragent side. `has_all_signatures` - can revert this filter.
    """
    filter_owner = Document.pending_owner_signatures == 0
    filter_recipient = Document.pending_recipient_signatures == 0

    return Bool.should(
        Bool.must(
            FIRST_SIGN_BY_OWNER,
            filter_owner if has_all_signatures else Bool.must_not(filter_owner),
        ),
        Bool.must(
            FIRST_SIGN_BY_RECIPIENT,
            filter_recipient if has_all_signatures else Bool.must_not(filter_recipient),
        ),
    )


def build_wait_my_sign_filter(user: AuthUser | User) -> Bool:
    """
    Comments base on info from:
    https://evocompany.atlassian.net/wiki/spaces/vchasno/pages/*********
    """
    edrpou = user.company_edrpou

    is_not_signed_by_current_user = Bool.must_not(Document.signed_roles == user.role_id)

    is_review_required_for_current_company = Bool.must_not(eq(Document.is_review_required, True))

    is_assigned_for_sign_for_current_user = Bool.must(Document.next_signer_roles == user.role_id)

    is_versioned_filter = Bool.must(
        # is document versioned
        ne(Document.last_version_uploaded_by_edrpou, None),
        Bool.should(
            Bool.must(
                # if latest version uploaded by current company
                Document.last_version_uploaded_by_edrpou == edrpou,
                # and signed by recipient company
                # and expected signatures by current company
                Bool.should(
                    Bool.must(
                        Document.owner_edrpou == edrpou,
                        Document.pending_recipient_signatures == 0,
                        Document.pending_owner_signatures != 0,
                    ),
                    Bool.must(
                        Document.owner_edrpou != edrpou,
                        Document.pending_owner_signatures == 0,
                        Document.pending_recipient_signatures != 0,
                    ),
                ),
            ),
            Bool.must(
                # if latest version uploaded by recipient company
                Document.last_version_uploaded_by_edrpou != edrpou,
                # and expected signatures by current company
                Bool.should(
                    Bool.must(
                        Document.owner_edrpou == edrpou,
                        Document.pending_owner_signatures != 0,
                    ),
                    Bool.must(
                        Document.owner_edrpou != edrpou,
                        Document.pending_recipient_signatures != 0,
                    ),
                ),
            ),
        ),
    )

    owner_filter = Bool.must(
        Document.owner_edrpou == edrpou,
        ne(Document.is_multilateral, True),
        Bool.should(
            # if versioned
            is_versioned_filter,
            # if not versioned
            Bool.must(
                eq(Document.last_version_uploaded_by_edrpou, None),
                Bool.should(
                    Bool.must(
                        FIRST_SIGN_BY_OWNER,
                        Bool.should(
                            Document.status.in_([UPLOADED, READY_TO_BE_SIGNED, SIGNED]),
                            Bool.must(
                                Document.status == APPROVED,
                                Document.expected_recipient_signatures == 0,
                            ),
                        ),
                    ),
                    Bool.must(
                        FIRST_SIGN_BY_RECIPIENT,
                        Document.status.in_([SIGNED_AND_SENT, APPROVED]),
                    ),
                ),
            ),
        ),
        is_waiting_for_sign_by_owner,
        is_assigned_for_sign_for_current_user,
        is_not_signed_by_current_user,
        is_review_required_for_current_company,
        is_not_finished_or_rejected,
        with_recipient_edrpou_or_internal,
    )

    recipient_filter = Bool.must(
        Document.owner_edrpou != edrpou,
        ne(Document.is_multilateral, True),
        Bool.should(
            # if versioned
            is_versioned_filter,
            # if not versioned
            Bool.must(
                eq(Document.last_version_uploaded_by_edrpou, None),
                Bool.should(
                    Bool.must(
                        FIRST_SIGN_BY_OWNER,
                        Document.status.in_([SIGNED_AND_SENT, APPROVED]),
                    ),
                    Bool.must(
                        FIRST_SIGN_BY_RECIPIENT,
                        Document.status.in_([SENT, SIGNED]),
                    ),
                ),
            ),
        ),
        Bool.should(
            is_assigned_for_sign_for_current_user,
            Bool.must(
                Document.current_recipient_emails == user.email,
                ~Exists(Document.next_signer_roles),
            ),
        ),
        is_waiting_for_sign_by_recipient,
        is_not_signed_by_current_user,
        is_review_required_for_current_company,
        is_not_finished_or_rejected,
        with_recipient_edrpou_or_internal,
    )

    multilateral_filter = Bool.must(
        eq(Document.is_multilateral, True),
        Document.status.not_in_([REJECT, REVOKED]),
        is_assigned_for_sign_for_current_user,
        is_current_flow_filter,
        is_not_signed_by_current_user,
        is_review_required_for_current_company,
    )

    return Bool.should(owner_filter, recipient_filter, multilateral_filter)


def build_folder_filter(user: AuthUser | User, folder: int) -> QueryExpression | None:
    if folder == DocumentFolder.inbox.value:
        filter_ = Bool.should(
            Bool.must(FIRST_SIGN_BY_OWNER, Document.owner_edrpou != user.company_edrpou),
            Bool.must(
                FIRST_SIGN_BY_RECIPIENT,
                Document.owner_edrpou != user.company_edrpou,
                Document.status.in_(FIRST_STATUSES),
            ),
            Bool.must(
                FIRST_SIGN_BY_RECIPIENT,
                Document.owner_edrpou == user.company_edrpou,
                Document.status.in_(LAST_STATUSES),
            ),
        )
        return Bool.must(filter_, eq(Document.is_internal, False))
    if folder == DocumentFolder.outgoing.value:
        filter_ = Bool.should(
            Bool.must(FIRST_SIGN_BY_OWNER, Document.owner_edrpou == user.company_edrpou),
            Bool.must(
                FIRST_SIGN_BY_RECIPIENT,
                Document.owner_edrpou == user.company_edrpou,
                Document.status.in_(FIRST_STATUSES),
            ),
            Bool.must(
                FIRST_SIGN_BY_RECIPIENT,
                Document.owner_edrpou != user.company_edrpou,
                Document.status.in_(LAST_STATUSES),
            ),
        )
        return Bool.must(filter_, eq(Document.is_internal, False))
    if folder == DocumentFolder.internal.value:
        return eq(Document.is_internal, True)
    if folder == DocumentFolder.not_internal.value:
        return eq(Document.is_internal, False)
    if folder == DocumentFolder.wait_my_sign.value:
        return build_wait_my_sign_filter(user)
    return None


def build_categories_filter(categories: t.Sequence[int]) -> Bool:
    """
    Filter documents by categories. Multiple categories combined with logical OR operator.
    """

    if PublicDocumentCategory.other.value in categories:
        return Bool.should(
            Bool.must(Document.category.in_(categories)),
            Bool.must(eq(Document.category, None)),
        )
    return Bool.must(Document.category.in_(categories))


def build_query_filter(query: str) -> Bool:
    return Bool.must(Document.all_text.match(query, minimum_should_match='100%'))


def build_tags_filter(without_tags: bool, tags_ids: list[str]) -> t.Sequence[QueryExpression]:
    if without_tags:
        return [
            ~Exists(Document.tag_ids),
        ]

    return [Document.tag_ids == tag_id_ for tag_id_ in tags_ids]


def build_amount_filter(
    amount_eq: int | None = None,
    amount_gte: int | None = None,
    amount_lte: int | None = None,
) -> list[QueryExpression]:
    filters = []
    if amount_eq is not None:
        filters.append(Document.amount == amount_eq)
        return filters
    if amount_gte is not None:
        filters.append(Document.amount >= amount_gte)
    if amount_lte is not None:
        filters.append(Document.amount <= amount_lte)
    return filters


def build_dates_filter(
    date_field: AttributedField,
    date_from: datetime | None = None,
    date_to: datetime | None = None,
    user: AuthUser | User | None = None,
) -> list[QueryExpression]:
    only_document_date_fields = [
        Document.date_review_approved.get_field_name(),
        Document.date_finished.get_field_name(),
    ]
    if user is not None and date_field.get_field_name() not in only_document_date_fields:
        # build specific filters by Document.viewer_dates
        return _build_document_viewers_dates_filter(
            date_field=date_field,
            user=user,
            date_from=date_from,
            date_to=date_to,
        )
    return _build_document_dates_filter(
        date_field=date_field,
        date_from=date_from,
        date_to=date_to,
    )


def _build_document_dates_filter(
    date_field: AttributedField,
    date_from: datetime | None = None,
    date_to: datetime | None = None,
) -> list[QueryExpression]:
    filters = []
    if date_from:
        filters.append(date_field >= date_from)

    if date_to:
        filters.append(date_field < date_to)

    return filters


def _build_document_viewers_dates_filter(
    date_field: AttributedField,
    user: AuthUser | User,
    date_from: datetime | None = None,
    date_to: datetime | None = None,
) -> list[QueryExpression]:
    filters = []
    field_name = date_field.get_field_name()
    is_filter_by_date_created = Viewer.date_created.get_field_name() == field_name

    if date_from and is_filter_by_date_created:
        filters.append(
            Bool.must(
                date_field >= date_from,
                Nested(Document.viewer_dates, _build_viewer_dates_filter(user)),
            )
        )
    elif date_from:
        filters.append(
            Nested(
                Document.viewer_dates,
                Bool.must(
                    _build_viewer_dates_filter(user),
                    date_field >= date_from,
                ),
            )
        )

    if date_to and is_filter_by_date_created:
        filters.append(
            Bool.must(
                date_field < date_to,
                Nested(Document.viewer_dates, _build_viewer_dates_filter(user)),
            )
        )
    elif date_to:
        filters.append(
            Nested(
                Document.viewer_dates,
                Bool.must(_build_viewer_dates_filter(user), date_field < date_to),
            )
        )

    return filters


def build_conditions_filters(user: AuthUser | User, conditions: list[str]) -> Bool:
    """
    More flexible way to filter documents. It allows to combine in
    different forms folder, status and other filters. All parts of condition
    will be combined with `AND` operator and all conditions will be combined
    with `OR` operator.

    For filtering, there is used a small query language. The format is

    `{folder}{status}{is_3p}{is_one}{is_one_sign}{has_all_signatures}
    {current_company_flow}`,
    where:
        folder - code from enum DocumentFolder
        status - code from enum DocumentStatus
        is_3p:
            0 - if need filter not 3p documents
            1 - if need filter 3p documents
            2 - if not need filter on this field
        is_one_sign:
            0 - if need build multi sign filter
            1 - if need build one sign filter
            2 - if not need filtering on this field
        has_all_signatures:
            0 - if need filter documents without all signatures
            1 - if need filter documents with all signatures
            2 - if not need filtering on this field
        current_company_flow:
            0 - if recipient's flow should be signed
            1 - if current company's flow should be signed
            2 - if not need filtering on this field
    Parameters `folder`, `is_3p`, `is_one_sign`, `has_all_signatures`,
    `current_company_flow` are optional.

    Valid examples:
        60027001121, 60017007100, 60027003010, 60017000101, 600070102220
    """

    edrpou = user.company_edrpou
    assert edrpou, 'Expected user.company_edrpou'

    filters_map: defaultdict[int, list[Bool]] = defaultdict(list)

    for condition in conditions:
        (
            folder,
            status,
            is3p,
            is_one_sign,
            has_all_signatures,
            current_company_flow,
        ) = _parse_new_condition(condition)

        if folder is None or status is None:
            continue

        # Basic filter
        status_filters: list[QueryExpression] = []
        status_filters = _apply_status_filters(
            filters=status_filters,
            options={
                'folderId': folder,
                'statusId': status,
                'isArchived': status == DocumentStatus.histored.value,
            },
            user=user,
        )

        # Filter for 3p documents
        if is3p is not None:
            sign_by = FirstSignBy.recipient.value if is3p else FirstSignBy.owner.value
            status_filters.append(Bool.must(Document.first_sign_by == sign_by))

        # Filter one or multiple signatures to finish
        if is_one_sign:
            status_filters.append(build_is_document_one_sign_filter())
        elif is_one_sign is False:
            status_filters.append(build_is_document_multi_sign_filter())

        # Filter documents that have all signatures
        if has_all_signatures is not None:
            signatures_filter = build_all_signatures_filter(has_all_signatures)
            status_filters.append(signatures_filter)

        # Filter current flow order
        if current_company_flow:
            status_filters.append(is_current_flow_filter)
        elif current_company_flow is False:
            status_filters.append(Bool.must_not(is_current_flow_filter))

        filters_map[folder].append(Bool.must(*status_filters))

    filters = [
        Bool.must(build_folder_filter(user, folder), Bool.should(*additional_filters))
        for folder, additional_filters in filters_map.items()
    ]
    return Bool.should(*filters)


async def build_reviews_filter(
    user: AuthUser | User, folder: DocumentReviewState
) -> QueryExpression | None:
    if folder in (
        DocumentReviewState.approved,
        DocumentReviewState.rejected,
        DocumentReviewState.pending,
    ):
        return Document.review_statuses.term(folder.value)
    if folder == DocumentReviewState.from_me:
        return Document.review_requests_from_roles == user.role_id
    if folder == DocumentReviewState.wait_my_review:
        group_ids = []
        if user.role_id:
            async with services.db_readonly.acquire() as conn:
                member_of_groups = await select_group_members(
                    conn=conn,
                    role_ids=[user.role_id],
                    is_deleted=False,  # Only get active group memberships
                )
                group_ids = [member.group_id for member in member_of_groups]
        return Bool(
            must=[
                Bool.should(
                    Document.review_requests_to_roles == user.role_id,
                    Document.review_requests_to_groups.in_(group_ids),
                )
            ]
        )
    if folder == DocumentReviewState.with_my_review:
        return Document.reviewed_role_ids == user.role_id
    if folder == DocumentReviewState.without_any:
        # Documents without any reviews (requests or manual)
        return Bool.must(
            ~Exists(Document.review_requests_to_roles),
            ~Exists(Document.reviewed_role_ids),
        )
    return None


def build_sort_script(
    *, direction: str, edrpou: str | None, owner_field: Field, recipient_field: Field
) -> DataDict:
    return {
        '_script': {
            'type': 'string',
            'script': {
                'lang': 'painless',
                'source': """
                    if (doc['owner_edrpou'].value == params.edrpou) {
                        if (doc[params.recipient_field].size() == 0) {
                            params.missing_value
                        } else {
                            doc[params.recipient_field].value
                        }
                    } else {
                        doc[params.owner_field].value
                    }
                """.strip(),
                'params': {
                    'edrpou': edrpou,
                    'owner_field': owner_field,
                    'recipient_field': recipient_field,
                    'missing_value': chr(sys.maxunicode) if direction == 'asc' else '',
                },
            },
            'order': direction,
        }
    }


def _get_document_sort_direction(options: DataDict) -> ListDirection:
    try:
        return ListDirection(options['direction'])
    except Exception:
        return ListDirection(DEFAULT_DOCUMENTS_DIRECTION)


def _build_order_by_field(options: DataDict) -> list[Sort]:
    field: DocumentsField = options['order_field']
    direction = _get_document_sort_direction(options).value

    secondary_sort = Sort(Document.seqnum, order=direction)

    if field.type_ in (DocumentFieldType.text, DocumentFieldType.enum):
        sort = Sort(
            Document.parameters_text.value,
            order=direction,
            **_nested_sort_params(
                nested_path=Document.parameters_text,
                nested_filter=Document.parameters_text.field_id == field.id_,
            ),
        )
    elif field.type_ == DocumentFieldType.number:
        sort = Sort(
            Document.parameters_number.value,
            order=direction,
            **_nested_sort_params(
                nested_path=Document.parameters_number,
                nested_filter=Document.parameters_number.field_id == field.id_,
            ),
        )
    elif field.type_ == DocumentFieldType.date:
        sort = Sort(
            Document.parameters_date.value,
            order=direction,
            **_nested_sort_params(
                nested_path=Document.parameters_date,
                nested_filter=Document.parameters_date.field_id == field.id_,
            ),
        )
    else:
        return [secondary_sort]

    return [sort, secondary_sort]


def build_order(
    user: AuthUser | User,
    options: DataDict,
    direction: ListDirection,
) -> list[DataDict | Sort]:
    edrpou = user.company_edrpou
    direction_str = direction.value

    if options.get('order_field'):
        return _build_order_by_field(options)

    order = options['order']
    if order not in enum_values(DocumentListOrder):
        order = DEFAULT_DOCUMENTS_ORDER

    orders = [
        Sort(
            Document.viewer_dates.date_created,
            order=direction_str,
            **_nested_sort_params(
                nested_path=Document.viewer_dates,
                nested_filter=_build_viewer_dates_filter(user),
            ),
        ),
        Sort(Document.seqnum, order=direction_str),
    ]

    sort_date = options.get('sortDate') or DEFAULT_DOCUMENTS_SORT_DATE
    filter_by_viewers_date = sort_date == DocumentListSortDate.date_listing.value
    filter_by_date_created = sort_date == DocumentListSortDate.date_created.value
    filter_by_date_document = sort_date == DocumentListSortDate.date_document.value
    filter_by_date_finished = sort_date == DocumentListSortDate.date_finished.value
    if order == DocumentListOrder.date.value and filter_by_date_created:
        orders = [
            Sort(Document.date_created, order=direction_str),
            Sort(Document.seqnum, order=direction_str),
        ]
    elif order == DocumentListOrder.date.value and filter_by_date_finished:
        orders = [
            Sort(Document.date_finished, order=direction_str),
            Sort(
                Document.viewer_dates.date_created,
                order=direction_str,
                **_nested_sort_params(
                    nested_path=Document.viewer_dates,
                    nested_filter=_build_viewer_dates_filter(user),
                ),
            ),
            Sort(Document.seqnum, order=direction_str),
        ]
    elif order == DocumentListOrder.date.value and filter_by_date_document:
        orders = [
            Sort(
                Document.viewer_dates.date_document,
                order=direction_str,
                **_nested_sort_params(
                    nested_path=Document.viewer_dates,
                    nested_filter=_build_viewer_dates_filter(user),
                ),
            ),
            Sort(Document.seqnum, order=direction_str),
        ]
    elif order == DocumentListOrder.date.value and filter_by_viewers_date:
        orders = [
            Sort(
                Document.viewer_dates.date_created,
                order=direction_str,
                **_nested_sort_params(
                    nested_path=Document.viewer_dates,
                    nested_filter=_build_viewer_dates_filter(user),
                ),
            ),
            Sort(Document.seqnum, order=direction_str),
        ]
    elif order == DocumentListOrder.number.value:
        orders = [Sort(Document.number, order=direction_str)]
    elif order == DocumentListOrder.title.value:
        orders = [Sort(Document.display_title, order=direction_str)]
    elif order == DocumentListOrder.seqnum.value:
        orders = [Sort(Document.seqnum, order=direction_str)]
    elif order == DocumentListOrder.edrpou.value:
        if edrpou:
            orders = [
                build_sort_script(
                    direction=direction_str,
                    edrpou=edrpou,
                    owner_field=Document.owner_edrpou,
                    recipient_field=Document.recipient_edrpou,
                )
            ]
    elif order == DocumentListOrder.company_email.value:
        orders = [
            build_sort_script(
                direction=direction_str,
                edrpou=edrpou,
                owner_field=Document.owner_email,
                recipient_field=Document.recipient_email,
            )
        ]
    elif order == DocumentListOrder.company_name.value:
        orders = [
            build_sort_script(
                direction=direction_str,
                edrpou=edrpou,
                owner_field=Document.owner_company_name,
                recipient_field=Document.recipient_company_name,
            )
        ]
    elif order == DocumentListOrder.amount.value:
        orders = [Sort(Document.amount, order=direction_str)]

    return orders


def build_query_user_email_filter(queries: list[str], user: AuthUser | User) -> Bool:
    edrpou = user.company_edrpou
    owner_email_expressions = []
    other_expressions = []
    for query in queries:
        owner_email_expressions.extend(
            [
                Document.owner_email == query,
                Document.owner_email.suggest.match(query=query, **FUZZY_PARAMETERS),
            ]
        )
        other_expressions.extend(
            [
                Document.recipients_emails == query,
                Document.recipients_emails.suggest.match(query=query, **FUZZY_PARAMETERS),
                Document.signer_emails == query,
                Document.signer_emails.suggest.match(query=query, **FUZZY_PARAMETERS),
            ]
        )
    return Bool.should(
        Bool.must(
            Document.owner_edrpou == edrpou,
            Bool.should(*owner_email_expressions),
        ),
        *other_expressions,
    )


def build_query_title_filter(queries: list[str]) -> QueryExpression:
    return Bool.should(*[Prefix(Document.display_title, query) for query in queries])


def build_query_number_filter(queries: list[str]) -> QueryExpression:
    expressions = []
    for query in queries:
        expressions.extend(
            [
                Document.number == query,
                Document.number.suggest.match(query=query, minimum_should_match='100%'),
            ]
        )
    return Bool.should(*expressions)


def build_query_company_name_filter(queries: list[str]) -> QueryExpression:
    expressions = []
    for query in queries:
        expressions.extend(
            [
                Document.recipients_company_names == query,
                Document.recipients_company_names.suggest.match(query, **FUZZY_PARAMETERS),
                Document.owner_company_name.suggest.match(query, **FUZZY_PARAMETERS),
            ]
        )
    return Bool.should(*expressions)


def build_query_company_edrpou_filter(queries: list[str]) -> QueryExpression:
    expressions = []
    for query in queries:
        expressions.extend(
            [
                Document.recipients_edrpous == query,
                Document.recipients_edrpous.suggest.match(query, minimum_should_match='100%'),
                Document.owner_edrpou == query,
                Document.owner_edrpou.suggest.match(query, minimum_should_match='100%'),
            ]
        )
    return Bool.should(*expressions)


def build_query_tags_filter(queries: list[str]) -> QueryExpression:
    expressions = []
    for query in queries:
        expressions.extend(
            [
                Document.tag_names == query,
                Document.tag_names.suggest.match(query, **FUZZY_PARAMETERS),
            ]
        )
    return Bool.should(*expressions)


def build_document_parameter_filter_for_es_field(
    es_field: Field,
    field: DocumentsField,
    raw_value: str | None,
) -> QueryExpression:
    """
    Append document parameters filter to existing ES filters during build.

    If `value` is an empty string, filter the following way:
        - Add documents without given field attached.
        - Add documents without filled value of a given field.
    """

    # When value is None, we try to find fields where value is filled yet
    if not raw_value:
        return Bool.must_not(
            Nested(
                path=es_field,
                query=Bool.must(es_field.field_id == field.id_),
            )
        )

    # For text field we use a prefix query to simplify search for our clients
    if field.type_ == DocumentFieldType.text:
        return Nested(
            path=es_field,
            query=Bool.must(
                es_field.field_id == field.id_,
                Prefix(es_field.value, raw_value),
            ),
        )

    # Most of our clients operate in the Kyiv time zone. When they search by date, for example,
    # 2023-10-01, they expect to find documents with timestamps between 00:00:00 and
    # 23:59:59 of the 2023-10-01 in Kyiv time. However, since we store dates in UTC in
    # Elasticsearch, we are first calculating the date range in the Kyiv time zone and then
    # converting it to UTC to have proper range in UTC. For our previous example with 2023-10,
    # the UTC range should be:
    #  - dt_from = "2023-09-30 21:00:00 (UTC)"
    #  - dt_to = "2023-10-01 20:59:59 (UTC)".
    if field.type_ == DocumentFieldType.date:
        dt = parse_local_datetime(raw_value)  # parse datetime in any TZ and convert it to Kyiv TZ
        dt_from = to_utc_datetime(dt.replace(hour=0, minute=0, second=0, microsecond=0))
        dt_to = to_utc_datetime(dt.replace(hour=23, minute=59, second=59, microsecond=999999))

        return Nested(
            path=es_field,
            query=Bool.must(
                es_field.field_id == field.id_,
                es_field.value >= dt_from,
                es_field.value < dt_to,
            ),
        )

    if field.type_ == DocumentFieldType.number:
        return Nested(
            path=es_field,
            query=Bool.must(
                es_field.field_id == field.id_,
                es_field.value == raw_value,
            ),
        )

    if field.type_ == DocumentFieldType.enum:
        return Nested(
            path=es_field,
            query=Bool.must(
                es_field.field_id == field.id_,
                es_field.value == raw_value,
            ),
        )

    t.assert_never(field.type_)


def build_document_parameters_filter(queries: DocumentSearchParameters) -> QueryExpression:
    """
    Build document parameters filters for elastic query.
    Fields are of 4 types - Text, Enum (similar to text), Number and Date
    """
    field_mapping = {
        DocumentFieldType.text: Document.parameters_text,
        DocumentFieldType.enum: Document.parameters_text,
        DocumentFieldType.number: Document.parameters_number,
        DocumentFieldType.date: Document.parameters_date,
    }

    _filters = []

    for field, raw_value in queries:
        es_field = field_mapping[field.type_]
        query = build_document_parameter_filter_for_es_field(
            es_field=es_field,
            field=field,
            raw_value=raw_value,
        )
        _filters.append(query)

    if not _filters:
        return Bool()

    # Just in case, let's make previous filtering logic under the flag to be able
    # to quickly revert behavior if needed
    if get_flag(FeatureFlags.USE_LEGACY_DOCUMENT_PARAMETERS_FILTER):
        return Bool.must(*_filters)

    return Bool.should(*_filters)


def _apply_listing_search_filters(
    filters: list[QueryExpression], options: DataDict, user: AuthUser | User
) -> list[QueryExpression]:
    if search_query := options.get('search'):
        filters.append(build_query_filter(search_query))

    titles = options.get('search_titles')
    if titles:
        filters.append(build_query_title_filter(titles))

    numbers = options.get('search_numbers')
    if numbers:
        filters.append(build_query_number_filter(numbers))

    company_names = options.get('search_company_names')
    if company_names:
        filters.append(build_query_company_name_filter(company_names))

    company_edrpous = options.get('search_company_edrpous')
    if company_edrpous:
        filters.append(build_query_company_edrpou_filter(company_edrpous))

    tags = options.get('search_tags')
    if tags:
        filters.append(build_query_tags_filter(tags))

    user_emails = options.get('search_user_emails')
    if user_emails:
        filters.append(build_query_user_email_filter(user_emails, user))

    parameters = options.get('search_parameters')
    if parameters:
        filters.append(build_document_parameters_filter(parameters))

    return filters


def _apply_first_sign_by_filters(
    filters: list[QueryExpression], options: DataDict
) -> list[QueryExpression]:
    if first_sign_by := options.get('firstSignBy'):
        if first_sign_by == FirstSignBy.recipient.value:
            filters.append(FIRST_SIGN_BY_RECIPIENT)
        if first_sign_by == FirstSignBy.owner.value:
            filters.append(FIRST_SIGN_BY_OWNER)

        logger.warning(
            msg='ES listing option searchFirstSignBy is not valid',
            extra={'search_option': first_sign_by},
        )

    return filters


def _apply_conditions2_filters(
    filters: list[QueryExpression],
    options: DataDict,
    user: AuthUser | User,
) -> list[QueryExpression]:
    if conditions := (options.get('conditions2') or [options.get('condition2')]):
        filters.append(
            build_conditions_filters(user=user, conditions=list(filter(None, conditions)))
        )

    return filters


def _apply_access_level_filters(
    filters: list[QueryExpression], options: DataDict
) -> list[QueryExpression]:
    access_level = options.get('accessLevel')
    if access_level:
        if access_level == DocumentAccessLevel.private.value:
            filters.append(Document.is_private.term(True))
        if access_level == DocumentAccessLevel.extended.value:
            pass  # not implemented, because there is no need for it yet

    return filters


def _apply_date_listing_selector(
    es_query: AsyncSearchQuery,
    user: AuthUser | User,
) -> AsyncSearchQuery:
    """
    Use inner_hits to pull the user's last-viewed timestamp for each document

    What it tells ElasticSearch to do:
    1. Bool.should + minimum_should_match=0 — run this nested query if you can, but don't
       exclude documents if it not matches
    2. Nested + Bool.must — to do filter among the "viewer_dates" nested field
    3. Inner hits — return which "viewer_dates" nested models matched the previous filter
    4. Size: 1 — get only first date. "_build_viewer_dates_filter" should properly sort or filter
       to have the target date first
    """

    # For nested queries, we can't check document-level fields like is_private
    # So we use a simple filter that allows both company and role access
    _company_access = ~Exists(Document.viewer_dates.role_ids)
    _role_access = Document.viewer_dates.role_ids.term(user.role_id)

    # Allow both company access (role_ids=None) and direct role access
    nested_filter = Bool.should(_company_access, _role_access)

    query = Bool(
        should=[
            Nested(
                path=Document.viewer_dates,
                query=Bool.must(nested_filter),
                inner_hits={
                    # this is a custom name to identify the inner hits, but for simplicity we
                    # give it the same name as the nested field name
                    'name': 'viewer_dates',
                    'size': 1,
                    '_source': [Document.viewer_dates.date_created],
                },
            )
        ],
        minimum_should_match=0,
    )
    return es_query.query(query)


async def build_listing_query(
    user: AuthUser | User,
    es: AsyncIndex,
    options: DataDict,
    calc_count: bool = False,
    with_listing_date: bool = False,
) -> AsyncSearchQuery:
    filters = [build_access_filter(user)]

    # document ids, is_root, category
    filters = _apply_direct_document_filters(filters=filters, options=options, user=user)

    # document folder, review folder
    filters = await _apply_folder_filters(filters=filters, options=options, user=user)

    # document directory (aka "Папка в архіві")
    filters = _apply_document_directory_filters(filters=filters, options=options)

    # filters build from search_query user inputs
    filters = _apply_listing_search_filters(filters=filters, options=options, user=user)

    # Various bool filters like is*, has*
    filters = _apply_bool_filters(filters=filters, options=options, user=user)

    filters = _apply_tags_filters(filters=filters, options=options)
    filters = _apply_status_filters(filters=filters, options=options, user=user)
    filters = _apply_date_filters(filters=filters, options=options, user=user)
    filters = _apply_amount_filters(filters, options)
    filters = _apply_invalid_signatures_filters(filters, options)

    filters = _apply_archived_filters(filters, options)

    filters = _apply_first_sign_by_filters(filters, options)

    filters = _apply_conditions2_filters(filters, options, user)

    filters = _apply_access_level_filters(filters, options)

    sort_direction = _get_document_sort_direction(options)
    if cursor := options.get('seqnumOffset'):
        if sort_direction == ListDirection.desc:
            filters.append(lt(Document.seqnum, cursor))
        else:
            filters.append(ge(Document.seqnum, cursor))

    filters = list(filter(None, filters))

    es_query = es.search_query().filter(Bool.must(*filters)).with_routing(user.company_edrpou)

    if calc_count:
        return es_query

    if with_listing_date and get_flag(FeatureFlags.ENABLE_LISTING_DATE_FROM_ES):
        es_query = _apply_date_listing_selector(es_query, user)

    return (
        es_query.source(Document.document_id)
        .limit(options['limit'])
        .offset(options['offset'])
        .order_by(*build_order(user, options, direction=sort_direction))
    )


def _apply_invalid_signatures_filters(
    filters: list[QueryExpression],
    options: DataDict,
) -> list[QueryExpression]:
    if invalid_signed_role_ids := options.get('invalidSignedRoles'):
        # Filter by roles who have invalid signatures on documents
        filters.append(
            Document.signed_invalid_roles.in_(invalid_signed_role_ids),
        )

    return filters


def _apply_archived_filters(
    filters: list[QueryExpression],
    options: DataDict,
) -> list[QueryExpression]:
    is_archived = options.get('isArchived', False)

    if is_archived:
        filters.append(Document.is_archived.term(True))
    else:
        filters.append(
            Bool.should(
                Document.is_archived.term(False),
                ~Exists(Document.is_archived),
            )
        )

    return filters


def _apply_amount_filters(
    filters: list[QueryExpression],
    options: DataDict,
) -> list[QueryExpression]:
    amount_eq, amount_gte, amount_lte = (
        options.get('amountEq'),
        options.get('amountGte'),
        options.get('amountLte'),
    )
    if any((amount_eq, amount_gte, amount_lte)):
        amount_filter = build_amount_filter(
            amount_eq=amount_eq,
            amount_gte=amount_gte,
            amount_lte=amount_lte,
        )
        filters.extend(amount_filter)

    return filters


def _apply_date_filters(
    filters: list[QueryExpression],
    options: DataDict,
    user: AuthUser | User,
) -> list[QueryExpression]:
    sort_date = options.get('sortDate') or DEFAULT_DOCUMENTS_SORT_DATE
    date_field = {
        DocumentListSortDate.date_listing.value: Document.viewer_dates.date_created,
        DocumentListSortDate.date_created.value: Document.date_created,
        DocumentListSortDate.date_finished.value: Document.date_finished,
    }.get(sort_date, Document.viewer_dates.date_document)

    gte = options.get('gte')
    lte = options.get('lte')
    if gte or lte:
        dates_filter = build_dates_filter(
            date_field=date_field,
            date_from=gte and validate_left_datetime(gte),
            date_to=lte and validate_right_datetime(lte),
            user=user,
        )
        filters.extend(dates_filter)

    if options.get('hasDateDelivered') is False:
        filters.append(
            Bool.must(
                ~Exists(Document.date_delivered),
                Bool.should(
                    Bool.must(build_is_document_one_sign_filter(), Document.status == FINISHED),
                    Bool.must(
                        build_is_document_multi_sign_filter(),
                        Document.status.in_([SIGNED_AND_SENT, SENT]),
                    ),
                ),
            )
        )

    return filters


def _apply_bool_filters(
    filters: list[QueryExpression],
    options: DataDict,
    user: AuthUser | User,
) -> list[QueryExpression]:
    if options.get('isWaitMySign'):
        filters.append(build_wait_my_sign_filter(user))

    if options.get('hasComments'):
        filters.append(Document.has_comments.term(True))

    if options.get('isMyInvalidSigned'):
        filters.append(Document.is_own_invalid_signed.term(True))

    if options.get('isPartnerInvalidSigned'):
        filters.append(Document.is_partner_invalid_signed.term(True))

    return filters


def _apply_document_directory_filters(
    filters: list[QueryExpression],
    options: DataDict,
) -> list[QueryExpression]:
    """
    That filters apply to documents list when resolving
    allArchiveItems (filter by directories and documents at the same time)
    """
    is_archived = options.get('isArchived')
    # Filter by directories only in company archive
    if not is_archived:
        return filters

    # If provided document filter-options that don't supported by directories,
    # ignore directory filters at all.
    non_empty_options = {k: v for k, v in options.items() if v is not None and v != '' and v != []}
    try:
        directory_options = DirectoriesListSearchOptions(**non_empty_options)
    except Exception:
        # just in-case check, that document-search options always contain required fields
        logger.exception('Failed doc-directory search parameters')
        return filters

    if directory_options.has_extra_search_options or directory_options.search:
        return filters

    # That filters apply during search documents belonged to specific directory
    # (for root directory Document.directory_id mustn't exist)
    if directory_id := directory_options.parent_id:
        filters.append(Document.directory_id.term(directory_id))
    else:
        filters.append(~Exists(Document.directory_id))
    return filters


async def _apply_folder_filters(
    filters: list[QueryExpression],
    options: DataDict,
    user: AuthUser | User,
) -> list[QueryExpression]:
    folder_ids = []
    if folder := options.get('folderId'):
        folder_ids = [folder]
    elif folders := options.get('folderIds'):
        folder_ids = folders

    if folder_ids:
        filters.append(Bool.should(*[build_folder_filter(user, folder) for folder in folder_ids]))

    review_folder = options.get('reviewFolder')
    if review_folder:
        filters.append(await build_reviews_filter(user, review_folder))

    return filters


def _apply_tags_filters(
    filters: list[QueryExpression],
    options: DataDict,
) -> list[QueryExpression]:
    without_tags = options.get('withoutTags')
    tags_ids = [options['tag']] if options.get('tag') else options.get('tags')
    if without_tags or tags_ids:
        filters.extend(
            build_tags_filter(
                without_tags=bool(without_tags),
                tags_ids=t.cast(list[str], tags_ids),
            )
        )
    return filters


def _apply_direct_document_filters(
    filters: list[QueryExpression],
    options: DataDict,
    user: AuthUser | User,
) -> list[QueryExpression]:
    if not user.show_child_documents:
        filters.append(Document.is_root.term(True))

    if options.get('ids'):
        options['limit'] = len(options['ids'])
        filters.append(Document.document_id.in_(options['ids']))

    if options.get('sign_session_documents_ids') is not None:
        documents_ids: list[str] = options['sign_session_documents_ids']
        filters.append(Document.document_id.in_(documents_ids))

    categories = options.get('categories')
    if categories is not None:
        filters.append(build_categories_filter(categories))

    return filters


def _apply_status_filters(
    filters: list[QueryExpression],
    options: DataDict,
    user: AuthUser | User,
) -> list[QueryExpression]:
    folder = options.get('folderId')
    status = options.get('statusId')
    statuses = options.get('statusIds')

    # Histored documents are available only at arhive page
    if not options.get('isArchived'):
        filters.append(Bool.must(Document.status != HISTORED))

    if folder == DocumentFolder.inbox.value and status == SIGNED_AND_SENT:
        filters.append(
            Bool.should(
                Bool.must(FIRST_SIGN_BY_OWNER, Document.status == SIGNED_AND_SENT),
                Bool.must(
                    FIRST_SIGN_BY_RECIPIENT,
                    Document.owner_edrpou != user.company_edrpou,
                    Document.status == SENT,
                ),
                Bool.must(
                    FIRST_SIGN_BY_RECIPIENT,
                    Document.owner_edrpou == user.company_edrpou,
                    Document.status == SIGNED_AND_SENT,
                ),
            )
        )
    elif status:
        filters.append(Bool.must(Document.status == status))
    elif statuses:
        filters.append(Bool.must(Document.status.in_(statuses)))

    return filters


async def build_incoming_api_query(
    *, es: AsyncIndex, user: User, options: ListIncomingDocumentsOptions
) -> AsyncSearchQuery:
    date_created_filters = build_dates_filter(
        date_field=Document.date_created,
        date_from=options.date_created_from,
        date_to=options.date_created_to,
    )

    date_document_filters = build_dates_filter(
        date_field=Document.date_document,
        date_from=options.date_document_from,
        date_to=options.date_document_to,
    )

    date_sent_filters = build_dates_filter(
        date_field=Document.viewer_dates.date_created,
        date_from=options.date_sent_from,
        date_to=options.date_sent_to,
        user=user,
    )

    date_finished_filters = build_dates_filter(
        date_field=Document.date_finished,
        date_from=options.date_finished_from,
        date_to=options.date_finished_to,
    )

    date_review_approved_filters = build_dates_filter(
        date_field=Document.date_review_approved,
        date_from=options.date_review_approved_from,
        date_to=options.date_review_approved_to,
    )

    amount_filter = build_amount_filter(
        amount_eq=options.amount_eq,
        amount_gte=options.amount_gte,
        amount_lte=options.amount_lte,
    )

    filters = [
        build_access_filter(user),
        Document.owner_edrpou != options.edrpou,
        *date_created_filters,
        *date_document_filters,
        *date_sent_filters,
        *date_finished_filters,
        *date_review_approved_filters,
        *amount_filter,
    ]

    if options.extension:
        filters.append(Document.extension == options.extension)

    if options.ids:
        filters.append(Document.document_id.in_(options.ids))

    if options.categories:
        filters.append(build_categories_filter(options.categories))

    if options.tags_ids or options.not_tagged:
        tags_filter = build_tags_filter(
            without_tags=bool(options.not_tagged),
            tags_ids=options.tags_ids or [],
        )
        filters.extend(tags_filter)
    if options.documents_fields:
        filters.append(build_document_parameters_filter(options.documents_fields))

    if options.edrpou_owners:
        filters.append(Document.owner_edrpou.in_(options.edrpou_owners))

    if options.statuses:
        filters.append(Document.status.in_(options.statuses))

    if options.cursor and options.cursor.isnumeric():
        cursor = int(options.cursor)
        filters.append(lt(Document.seqnum, cursor))

    if options.processed is True:
        filters.append(Document.is_processed.term(True))
    elif options.processed is False:
        filters.append(
            Bool.should(
                Document.is_processed.term(False),
                ~Exists(Document.is_processed),
            )
        )

    if options.is_archived:
        filters.append(Document.is_archived.term(True))
    elif options.is_archived is False:
        filters.append(
            Bool.should(
                Document.is_archived.term(False),
                ~Exists(Document.is_archived),
            )
        )

    if folder_ := options.review_state:
        review_conditions = await build_reviews_filter(user, folder_)
        if review_conditions:
            filters.append(review_conditions)

    es_query = (
        es.search_query()
        .with_routing(user.company_edrpou)
        .source(Document.document_id)
        .filter(Bool.must(*filters))
        .order_by(Sort(Document.seqnum, order='desc'))
    )

    return es_query


async def build_outgoing_api_query(  # noqa: C901 need refactoring
    *, es: AsyncIndex, user: User, options: ListDocumentsOptions
) -> AsyncSearchQuery:
    date_document_filters = build_dates_filter(
        date_field=Document.date_document,
        date_from=options.date_document_from,
        date_to=options.date_document_to,
    )

    date_created_filters = build_dates_filter(
        date_field=Document.date_created,
        date_from=options.date_from,
        date_to=options.date_to,
    )

    date_finished_filters = build_dates_filter(
        date_field=Document.date_finished,
        date_from=options.date_finished_from,
        date_to=options.date_finished_to,
    )

    date_rejected_filters = build_dates_filter(
        date_field=Document.date_finished,
        date_from=options.date_rejected_from,
        date_to=options.date_rejected_to,
    )

    date_review_approved_filters = build_dates_filter(
        date_field=Document.date_review_approved,
        date_from=options.date_review_approved_from,
        date_to=options.date_review_approved_to,
    )

    if date_rejected_filters:
        date_rejected_filters.append(
            Bool.should(
                Document.status.term(DocumentStatus.reject.value),
                Document.status.term(DocumentStatus.revoked.value),
            )
        )

    amount_filters = build_amount_filter(
        amount_eq=options.amount_eq,
        amount_gte=options.amount_gte,
        amount_lte=options.amount_lte,
    )

    filters = [
        build_access_filter(user),
        Document.owner_edrpou == options.edrpou,
        *date_document_filters,
        *amount_filters,
        *date_created_filters,
        *date_finished_filters,
        *date_rejected_filters,
        *date_review_approved_filters,
    ]

    if options.is_delivered is True:
        filters.append(Exists(Document.date_delivered))
    elif options.is_delivered is False:
        filters.append(~Exists(Document.date_delivered))

    if options.recipient_edrpou:
        filters.append(
            Bool.should(
                Document.recipients_edrpous == options.recipient_edrpou,
                Document.recipient_edrpou == options.recipient_edrpou,
            )
        )

    if options.categories:
        filters.append(build_categories_filter(options.categories))

    if options.extension:
        filters.append(Document.extension == options.extension)

    if options.number:
        filters.append(Document.number == options.number)

    if options.ids:
        filters.append(Document.document_id.in_(options.ids))

    if options.tags_ids or options.not_tagged:
        tags_filters = build_tags_filter(
            without_tags=bool(options.not_tagged),
            tags_ids=options.tags_ids or [],
        )
        filters.extend(tags_filters)

    if options.vendor_id:
        filters.append(Document.vendor_id == options.vendor_id)

    if options.vendor_str:
        filters.append(Document.vendor == options.vendor_str)

    if options.has_changed:
        filters.append(Document.has_changed == options.has_changed)

    if options.documents_fields:
        filters.append(build_document_parameters_filter(options.documents_fields))

    if options.statuses:
        filters.append(Document.status.in_(options.statuses))

    if options.is_archived:
        filters.append(Document.is_archived.term(True))
    elif options.is_archived is False:
        filters.append(
            Bool.should(
                Document.is_archived.term(False),
                ~Exists(Document.is_archived),
            )
        )

    if options.is_internal:
        filters.append(Document.is_internal.term(True))
    elif options.is_internal is False:
        filters.append(Document.is_internal.term(False))

    if folder_ := options.review_state:
        review_conditions = await build_reviews_filter(user, folder_)
        if review_conditions:
            filters.append(review_conditions)

    if options.cursor and options.cursor.isnumeric():
        cursor = int(options.cursor)
        filters.append(lt(Document.seqnum, cursor))

    es_query = (
        es.search_query()
        .with_routing(user.company_edrpou)
        .source(Document.document_id)
        .filter(Bool.must(*filters))
        .order_by(Sort(Document.seqnum, order='desc'))
    )

    return es_query


async def send_to_indexator(
    redis: Redis | None = None,
    document_ids: Iterable[str] | None = None,
    to_slow_queue: bool = False,
    to_listing: bool = False,
    to_archive: bool = False,
    to_temp: bool = False,
) -> str | None:
    """
    We have fast and slow queues. The fast queue should handle
    requests from the web, and the slow — from the API and workers
    """

    redis = redis or services.redis

    if not document_ids:
        return None

    logger.info(
        'Send to indexator',
        extra={
            'document_ids': document_ids,
            'to_slow_queue': to_slow_queue,
            'to_listing': to_listing,
            'to_archive': to_archive,
        },
    )

    key = INDEXATOR_KEY
    if to_slow_queue:
        key = INDEXATOR_SLOW_KEY
    if to_temp:
        key = INDEXATOR_TEMP_KEY
    if to_listing:
        key = INDEXATOR_LISTING_KEY
    if to_archive:
        key = INDEXATOR_ARCHIVE_KEY

    mapping: Mapping[str, int] = {document_id: 1 for document_id in document_ids}

    try:
        await redis.zadd(key, mapping)
    except Exception:
        logger.exception('Error sending to indexator', extra={'key': key})

    return key


def _nested_sort_params(nested_path: str, nested_filter: QueryExpression | None) -> DataDict:
    """
    Generate nested sort params for ES query

    Since ES 6.1 nested_path and nested_filter are deprecated in favor of nested
    object.
    https://www.elastic.co/guide/en/elasticsearch/reference/6.6/search-request-sort.html#nested-sorting
    """
    if nested_filter:
        return {
            'nested': {
                'path': nested_path,
                'filter': nested_filter,
            }
        }
    return {
        'nested': {
            'path': nested_path,
        }
    }


async def fetch_es(query: AsyncSearchQuery, source: ESQuerySource) -> t.Any:
    with tracking.es_get_documents.time(source=source.value) as metric:
        result = await query.get_result()

    if metric.duration > ES_SLOW_DURATION_LIMIT:
        logger.info(
            '[ES] Slow query',
            extra={
                'duration': metric.duration,
                'source': source.value,
                'query': to_json(await query.to_dict()),
            },
        )
    return result


async def fetch_es_document_ids(query: AsyncSearchQuery, source: ESQuerySource) -> list[str]:
    """
    Fetches the Document IDs from Elasticsearch based on the given query and source.

    Parameters:
        query (AsyncSearchQuery): The async search query object.
        source (ESQuerySource): The Enum source of the query.

    Returns:
        list[str]: A list of document IDs retrieved from Elasticsearch.
    """
    result = await fetch_es(query=query, source=source)
    return [h.document_id for h in result.hits]


async def fetch_es_documents_list(
    query: AsyncSearchQuery, source: ESQuerySource
) -> tuple[int, list[str]]:
    """
    Fetches count and Document IDs from Elasticsearch based on the given query and source.

    Parameters:
        query (AsyncSearchQuery): The async search query object.
        source (ESQuerySource): The Enum source of the query.

    Returns:
        int: Records count
        list[str]: A list of document IDs retrieved from Elasticsearch.
    """
    result = await fetch_es(query=query, source=source)
    return result.total, [h.document_id for h in result.hits]


async def count_es_documents(query: AsyncSearchQuery, source: ESQuerySource) -> int:
    """
    This method counts the number of documents returned by an ElasticSearch query.

    Parameters:
        query: An instance of the AsyncSearchQuery class representing the query to be executed.
        source: An instance of the ESQuerySource enum representing the source of the query.

    Return Type:
        int: Count of the documents returned by the query.

    """
    with tracking.es_get_documents.time(source=source.value) as metric:
        result = await query.count()

    if metric.duration > ES_SLOW_DURATION_LIMIT:
        logger.info(
            '[ES] Slow query',
            extra={
                'duration': metric.duration,
                'source': source.value,
                'count_query': to_json(await query.to_dict()),
            },
        )
    return result


async def count_company_documents_sent(company_edrpou: str) -> int:
    filters = [
        Document.status >= DocumentStatus.sent.value,
        Document.owner_edrpou == company_edrpou,
        Document.access_edrpou == company_edrpou,
    ]
    es_query = (
        services.es.documents.search_query()
        .filter(Bool.must(*filters))
        .with_routing(company_edrpou)
    )
    return await count_es_documents(es_query, source=ESQuerySource.worker)


@dataclass(frozen=True)
class RawESHit:
    hit: dict[str, t.Any]

    @property
    def source(self) -> dict[str, t.Any]:
        return self.hit.get('_source', {})

    def get_field(self, name: str) -> t.Any:
        """Get field from _source"""
        return self.source.get(name)

    def inner_hits(self, name: str) -> list[RawESHit]:
        # INFO: Inner hits here can have more than one key, each of which has structure of
        # RawESResult, so you can call it several times with different names if a query has
        # multiple inner hits
        raw_result = self.hit.get('inner_hits', {}).get(name, {})

        result = RawESSearchResult(raw=raw_result)
        return result.hits

    def inner_hit_field(self, path: str) -> str | None:
        """
        If it is expected to have only one inner hit, you can use this method to simplify
        extracting the field by path. Path should be in format: {inner_hit_name}.{field_name}

        -> .inner_hits.{hit_name}.hits.hits[0]._source.{field_name}
        """

        assert path.count('.') == 1, 'Inner hit path should single dot separated name'

        hit_name, field_name = path.split('.')
        assert hit_name and field_name, 'Inner hit name and field name should be provided'

        inner_hits = self.inner_hits(name=hit_name)
        if not inner_hits:
            return None

        return inner_hits[0].get_field(name=field_name)


@dataclass(frozen=True)
class RawESSearchResult:
    """
    This class a bit similar to the elasticmagic SearchResult class, but with a focus on
    extracting inner hits from the results.

    Example of the ES hit structure for viewer_dates:

    (RawESResult)
    └─ hits
       └─ hits [
          ├─ { (RawESHit)
          │    _source: {
          │       document_id: "10000000-0000-0000-0000-000000000002"
          │       ...other Document's fields…
          │    }
          │    inner_hits:
          │      listing_date: <RawESResult>
          │       └─ hits: { hits: [<RawESHit>, ...] }
          │ }
          └─ ... other hits (Document)
       ]
    """

    raw: dict[str, t.Any]

    @property
    def hits(self) -> list[RawESHit]:
        return [RawESHit(hit=hit) for hit in self.raw.get('hits', {}).get('hits', [])]


async def count_sent_outgoing_documents_elastic(
    company_edrpou: str | None,
    activity_period: BannerActivityPeriod | None,
) -> int:
    """
    Count documents sent by a company in the given activity period using Elasticsearch.
    If activity_period is None, count all documents.
    """
    if company_edrpou is None:
        return 0

    filters = [
        Document.access_edrpou.term(company_edrpou),
        Document.owner_edrpou.term(company_edrpou),
        Document.status.not_in_([UPLOADED, READY_TO_BE_SIGNED]),
    ]

    if activity_period:
        days = activity_period.days_in_period()
        since = utc_now() - timedelta(days=days)
        filters.extend(build_dates_filter(Document.date_created, date_from=since))

    es_query = (
        services.es.documents.search_query()
        .filter(Bool.must(*filters))
        .with_routing(company_edrpou)
    )
    r = await count_es_documents(es_query, source=ESQuerySource.api)
    return r


async def count_incoming_company_documents(
    is_signed: bool,
    company_edrpou: str | None,
    activity_period: BannerActivityPeriod | None,
) -> int:
    """
    Count incoming documents signed by a company in the given activity period using Elasticsearch.
    """
    if company_edrpou is None:
        return 0
    filters = [
        # Document is not owned by the company (incoming)
        Document.owner_edrpou != company_edrpou,
        Document.access_edrpou.term(company_edrpou),
        Document.is_signed.term(is_signed),
    ]

    if activity_period:
        days = activity_period.days_in_period()
        since = utc_now() - timedelta(days=days)
        if is_signed:
            filters.extend(build_dates_filter(Document.date_latest_sign, date_from=since))
        else:
            # Get date_latest_sign to filter activity period,
            # but in case where we looking for not signed documents,
            # with no any signatures, date_latest_sign = None,
            # so apply activity period filter to date_created
            filters.append(
                Bool.should(
                    # Prefer date_latest_sign if it exists
                    Bool.must(
                        Exists(Document.date_latest_sign),
                        *build_dates_filter(Document.date_latest_sign, date_from=since),
                    ),
                    # Fallback to date_created if date_latest_sign does not exist
                    Bool.must(
                        ~Exists(Document.date_latest_sign),
                        *build_dates_filter(Document.date_created, date_from=since),
                    ),
                )
            )

    es_query = (
        services.es.documents.search_query()
        .filter(Bool.must(*filters))
        .with_routing(company_edrpou)
    )
    return await count_es_documents(es_query, source=ESQuerySource.api)
