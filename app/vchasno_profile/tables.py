import sqlalchemy as sa

from app.models import columns, metadata
from app.vchasno_profile.enums import VchasnoProfileSyncEntityType

NAME_LENGTH = 64


# Table to record a list of entities that should be synced with external systems.
# We don't care about the actual order of the events, here we just instruct the system
# to sync those entities.
vchasno_profile_sync_table = sa.Table(
    'vchasno_profile_sync',
    metadata,
    columns.IntID(),
    columns.SoftEnum('entity_type', enum_=VchasnoProfileSyncEntityType),
    columns.Text('entity_id'),
    columns.DateCreated(),
)
