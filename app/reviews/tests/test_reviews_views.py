import uuid
from http import HTTPStatus

import pytest
import sqlalchemy as sa
import ujson

from api.public.tests.common import DATA_PATH, GROUP, ROLE
from app.auth.types import User
from app.billing.db import update_billing_company_config
from app.document_versions import utils as document_versions_utils
from app.document_versions.enums import DocumentVersionType, VersionReviewFlow
from app.document_versions.tests.utils import prepare_document_version
from app.document_versions.types import ValidatedVersionDelete, ValidatedVersionUpload
from app.document_versions.utils import (
    add_document_content_version,
    delete_latest_version,
    get_latest_document_version_available_for_company,
    get_version_count,
)
from app.documents.enums import FirstSignBy
from app.documents.tests.test_documents_views import TEST_RECIPIENT_EDRPOU, TEST_RECIPIENT_EMAIL
from app.documents.types import Document
from app.groups.db import update_group_members
from app.groups.utils import remove_group
from app.lib.enums import DocumentStatus, Source, User<PERSON><PERSON>
from app.lib.helpers import to_json
from app.models import select_all, select_one
from app.reviews.db import (
    insert_review_request,
    select_review_requests,
    select_review_status_by_document_id,
    select_reviews,
    select_reviews_for_history,
    select_reviews_statuses,
    select_reviews_statuses_by_documents_ids,
)
from app.reviews.enums import ReviewRequestStatus, ReviewStatus, ReviewType
from app.reviews.tables import review_request_table, review_setting_table, review_table
from app.reviews.tests.utils import prepare_review_status_db
from app.reviews.types import ReviewsInfoCtx, ReviewStatusDB
from app.services import services
from app.tests.common import (
    API_V2_REVIEWS_REQUESTS_URL,
    DELETE_REVIEW_URL_TMPL,
    REVIEW_URL,
    REVIEWS_URL,
    TEST_COMPANY_EDRPOU_2,
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    UPDATE_DOCUMENT_URL,
    UPLOAD_DOCUMENT_URL,
    fetch_graphql,
    get_document,
    prepare_auth_headers,
    prepare_batch_reviews,
    prepare_client,
    prepare_delete_review,
    prepare_document_data,
    prepare_document_upload,
    prepare_flow_item,
    prepare_form_data,
    prepare_group,
    prepare_review,
    prepare_review_requests,
    prepare_user_data,
    request_document_update,
    select_review_state,
    set_company_config,
    sign_and_send_document,
)

PENDING_REVIEW = ReviewStatus.pending
APPROVED_REVIEW = ReviewStatus.approved

TEST_UUID_1 = 'ea36d57f-8b35-4254-aee8-fe7be22b9899'
TEST_UUID_2 = 'b699dadb-5ecb-4558-9b70-3531a5aa34bf'
TEST_UUID_3 = 'ed2e597a-8376-4ae3-854e-d4a738f16952'
TEST_UUID_4 = 'b3185df5-dd7d-4883-b99d-5b1ca2755d62'
TEST_UUID_5 = '76701027-a5da-46d6-bbca-e6dbbcda08af'

REVIEWS_AND_REQUEST_QUERY = (
    '{ document(id: "document_id") { '
    'reviewRequests { id documentId fromRoleId toRoleId status order }'
    'reviews { id documentId roleId type groupId }'
    'reviewStatus'
    '} }'
)


@pytest.mark.parametrize(
    'user_data, review_type, expected_mails',
    [
        pytest.param(
            {
                'can_receive_reviews': True,
                'can_receive_rejects': True,
                'can_receive_review_process_finished': True,
                'can_receive_review_process_finished_assigner': True,
            },
            ReviewType.approve,
            # there is no email about single review approve,
            # only about when all reviews are approved
            0,
            id='approve',
        ),
        pytest.param(
            {
                'can_receive_reviews': False,
                'can_receive_rejects': True,  # only this is checked for review reject
                'can_receive_review_process_finished': False,
                'can_receive_review_process_finished_assigner': False,
            },
            ReviewType.reject,
            1,  # we send notification about each review reject
            id='reject',
        ),
        pytest.param(
            {
                'can_receive_reviews': False,
                'can_receive_rejects': False,
                'can_receive_review_process_finished': False,
                'can_receive_review_process_finished_assigner': False,
            },
            ReviewType.reject,
            0,
            id='reject_can_not_receive',
        ),
    ],
)
async def test_add_review_notifications(
    aiohttp_client,
    review_type,
    expected_mails,
    mailbox,
    user_data: dict[str, bool],
):
    app, client, user = await prepare_client(
        aiohttp_client,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        **user_data,
    )
    another_user = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email='<EMAIL>',
        **user_data,
    )

    doc = await prepare_document_data(app, user)

    await prepare_review_requests(client, doc, initiator=user, reviewers=[user], is_required=True)
    assert len(mailbox) == 0

    # Add second user as reviewer
    await prepare_review_requests(
        client, doc, user, reviewers=[user, another_user], is_required=True
    )
    mailbox.clear()

    await prepare_review(client, user=user, document=doc, review_type=review_type.value)
    assert len(mailbox) == expected_mails

    async with app['db'].acquire() as conn:
        await update_billing_company_config(
            conn=conn,
            company_id=user.company_id,
            config={'allow_ordered_reviews': True},
        )

        reviews = await select_reviews(
            conn, company_edrpou=user.company_edrpou, document_ids=[doc.id]
        )
        assert len(reviews) == 1
        review = reviews[0]
    assert review.type == review_type


@pytest.mark.parametrize(
    'user_role_id, reviewer_role_id',
    [(TEST_UUID_1, TEST_UUID_1), (TEST_UUID_1, TEST_UUID_2)],
)
async def test_delete_review(aiohttp_client, user_role_id, reviewer_role_id):
    """User can only delete his own review."""
    app, client, user = await prepare_client(aiohttp_client, role_id=user_role_id)
    doc = await prepare_document_data(app, user)
    if user_role_id != reviewer_role_id:
        reviewer = await prepare_user_data(app, email='<EMAIL>', role_id=reviewer_role_id)
    else:
        reviewer = user
    await prepare_review(client, document=doc, user=reviewer, review_type=ReviewType.approve)

    url = DELETE_REVIEW_URL_TMPL.format(document_id=doc.id)
    response = await client.delete(url, headers=prepare_auth_headers(user))
    assert response.status == HTTPStatus.OK

    async with services.db.acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[doc.id], with_cancellations=True)
    if user_role_id == reviewer_role_id:
        assert len(reviews) == 1
        assert not reviews[0].type
    else:
        assert len(reviews) == 2
        assert {reviews[0].type, reviews[1].type} == {ReviewType.approve, None}


@pytest.mark.parametrize(
    'from_role_id, to_role_id, is_parallel, is_admin',
    [
        # assigner is able to cancel both parallel and ordered RR
        (TEST_UUID_1, TEST_UUID_3, False, False),
        (TEST_UUID_1, TEST_UUID_3, True, False),
        # admin is able to cancel both parallel and ordered coworker's RR
        (TEST_UUID_2, TEST_UUID_3, False, True),
        (TEST_UUID_2, TEST_UUID_3, True, True),
    ],
)
async def test_delete_review_requests(
    aiohttp_client, from_role_id, to_role_id, is_parallel, is_admin
):
    app, client, user = await prepare_client(aiohttp_client, role_id=TEST_UUID_1, is_admin=is_admin)
    await prepare_user_data(app, email='<EMAIL>', role_id=TEST_UUID_2)
    user2 = await prepare_user_data(app, email='<EMAIL>', role_id=TEST_UUID_3)
    doc = await prepare_document_data(app, user)

    async with services.db.acquire() as conn:
        await insert_review_request(
            conn,
            {
                'order': None if is_parallel else 1,
                'document_id': doc.id,
                'from_role_id': from_role_id,
                'to_role_id': to_role_id,
            },
        )
    response = await client.delete(
        API_V2_REVIEWS_REQUESTS_URL.format(document_id=doc.id),
        headers=prepare_auth_headers(user),
        json={'user_to_email': user2.email},
    )
    assert response.status == HTTPStatus.NO_CONTENT
    async with services.db.acquire() as conn:
        review_request = await select_one(conn, review_request_table.select())
    assert review_request.status == ReviewRequestStatus.deleted


async def test_cancel_review_notification(aiohttp_client, mailbox):
    app, client, user = await prepare_client(aiohttp_client)
    doc = await prepare_document_data(app, user)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='<EMAIL>')
    user4 = await prepare_user_data(app, email='<EMAIL>')
    group = await prepare_group(app, 'test1', user, [user4])

    await prepare_review_requests(
        client, doc, user, reviewers=[user, user2, user3, group], is_ordered=True
    )
    assert len(mailbox) == 0

    # Add approval review , expected email to next user in review-requests
    await prepare_review(
        client=client,
        document=doc,
        user=user,
        review_type=ReviewType.approve,
    )
    assert len(mailbox) == 1
    assert mailbox[-1]['Subject'] == 'Ви отримали документ на погодження'
    assert mailbox[-1]['To'] == '<EMAIL>'

    await prepare_review(
        client=client,
        document=doc,
        user=user2,
        review_type=ReviewType.reject,
    )
    assert len(mailbox) == 2
    assert mailbox[-1]['Subject'] == 'Документ, надісланий вами на погодження, було відхилено'
    assert mailbox[-1]['To'] == user.email
    mailbox.clear()

    await prepare_review(
        client=client,
        document=doc,
        user=user3,
        review_type=ReviewType.reject,
    )
    # Excepted no new emails
    assert len(mailbox) == 0


async def test_reset_review_status_after_cancellation(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    doc = await prepare_document_data(app, user)

    await prepare_review_requests(client, doc, user, reviewers=[user])

    async with app['db'].acquire() as conn:
        review_request = await select_one(conn, review_request_table.select())
        assert review_request.document_id == doc.id
        assert review_request.from_role_id == user.role_id
        assert review_request.status == ReviewRequestStatus.active

        status = await select_review_status_by_document_id(
            conn=conn,
            company_edrpou=user.company_edrpou,
            document_id=doc.id,
            version_id=None,
        )
        assert status.status == ReviewStatus.pending
        assert status.document_id == doc.id
        assert status.edrpou == user.company_edrpou
        assert status.is_last is True
        assert status.next_review_requests == [review_request.id]

    response = await client.delete(
        API_V2_REVIEWS_REQUESTS_URL.format(document_id=doc.id),
        headers=prepare_auth_headers(user),
        json={'user_to_email': user.email},
    )
    assert response.status == HTTPStatus.NO_CONTENT

    async with app['db'].acquire() as conn:
        review_request = await select_one(conn, review_request_table.select())
        assert review_request.status == ReviewRequestStatus.deleted

        status = await select_review_status_by_document_id(
            conn=conn,
            company_edrpou=user.company_edrpou,
            document_id=doc.id,
            version_id=None,
        )
        assert not status


async def test_change_document_review_status_with_groups(aiohttp_client):
    async def assert_review_status(expected_status: ReviewStatus) -> None:
        data = await fetch_graphql(client, query, prepare_auth_headers(user1))
        assert data['document']['reviewStatus'] == expected_status.value

    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    group = await prepare_group(app, 'test1', user1, [user1])

    document = await prepare_document_data(app, user1)
    query = REVIEWS_AND_REQUEST_QUERY.replace('document_id', document.id)

    await prepare_review(client, user=user1, document=document, review_type=ReviewType.approve)
    await assert_review_status(ReviewStatus.approved)

    await prepare_review_requests(client, document, user1, reviewers=[user1, user2])
    await assert_review_status(ReviewStatus.pending)

    await prepare_review(client, user=user2, document=document, review_type=ReviewType.reject)
    await assert_review_status(ReviewStatus.rejected)

    await prepare_review(client, user=user2, document=document, review_type=ReviewType.approve)
    await assert_review_status(ReviewStatus.approved)

    await prepare_review_requests(client, document, user1, reviewers=[user1, user2, group])
    await assert_review_status(ReviewStatus.pending)

    await prepare_review(client, user=user1, document=document, review_type=ReviewType.approve)
    await assert_review_status(ReviewStatus.approved)


async def test_update_review_setting(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    coworker = await prepare_user_data(app, email='<EMAIL>')
    doc = await prepare_document_data(app, user)

    # initiator can update review settings
    await prepare_review_requests(client, doc, user, reviewers=[], is_required=True)

    async with services.db.acquire() as conn:
        settings = await select_all(conn, review_setting_table.select())
        assert len(settings) == 1
        assert settings[0].is_required

    # company admin can update review settings too
    await prepare_review_requests(client, doc, initiator=coworker, reviewers=[])

    async with services.db.acquire() as conn:
        settings = await select_all(conn, review_setting_table.select())
        assert len(settings) == 1
        assert not settings[0].is_required


@pytest.mark.parametrize('can_receive_reviews, ', [True, False])
async def test_review_flow_emails(
    aiohttp_client,
    can_receive_reviews,
    mailbox,
    telegrambox,
):
    app, client, user = await prepare_client(
        aiohttp_client,
        can_receive_reviews=can_receive_reviews,
        can_receive_inbox=can_receive_reviews,
    )
    coworker1 = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=user.company_edrpou,
        can_receive_reviews=can_receive_reviews,
        can_receive_inbox=can_receive_reviews,
    )
    coworker2 = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou=user.company_edrpou,
        can_receive_reviews=can_receive_reviews,
        can_receive_inbox=can_receive_reviews,
    )

    document = await prepare_document_upload(
        app,
        client,
        user=user,
        reviewers_ids=[user.role_id, coworker1.role_id],
        signer_roles=[user.role_id, coworker2.role_id],
        expected_owner_signatures=2,
        is_required_review=True,
    )
    async with app['db'].acquire() as conn:
        review_status = await select_review_status_by_document_id(
            conn=conn,
            company_edrpou=user.company_edrpou,
            document_id=document.id,
            version_id=None,
        )
        review_requests = await select_review_requests(
            conn=conn,
            company_id=user.company_id,
            document_ids=[document.id],
        )
    assert review_status is not None
    assert review_status.is_required is True
    assert review_status.status == ReviewStatus.pending
    assert review_status.is_last is True
    assert sorted(review_status.next_review_requests) == sorted([r.id for r in review_requests])

    if can_receive_reviews:
        # Notification about review request
        assert len(mailbox) == 1
        assert len(telegrambox) == 1
        # uploader will not receive email about review
        assert {mail['To'] for mail in mailbox} == {'<EMAIL>'}
    else:
        assert len(mailbox) == 0

    mailbox.clear()
    telegrambox.clear()

    response = await client.post(
        REVIEW_URL,
        headers=prepare_auth_headers(user),
        json={'document_id': document.id, 'type': ReviewType.approve.value},
    )
    assert response.status == HTTPStatus.CREATED
    assert len(mailbox) == 0
    assert len(telegrambox) == 0

    response = await client.post(
        REVIEW_URL,
        headers=prepare_auth_headers(coworker1),
        json={'document_id': document.id, 'type': ReviewType.approve.value},
    )
    assert response.status == HTTPStatus.CREATED
    if can_receive_reviews:
        # Notification about signing to signers
        assert len(telegrambox) == 2
        assert len(mailbox) == 2
        assert {mail['To'] for mail in mailbox} == {
            '<EMAIL>',
            # that user is assigner of a sign process and signer at the same time. We should
            # email him/her as well, because a review process can be long enough (IRL) and
            # that user can forget about the document
            '<EMAIL>',
        }
    else:
        assert len(mailbox) == 0


@pytest.mark.slow
@pytest.mark.parametrize('is_multilateral', [True, False])
@pytest.mark.parametrize('use_batch', [True, False])
@pytest.mark.parametrize('can_receive_reviews', [True, False])
async def test_reviews_for_documents_general(
    aiohttp_client,
    can_receive_reviews,
    mailbox,
    use_batch,
    is_multilateral,
):
    """
    Big test
    """

    # Data preparing
    app, client, uploader = await prepare_client(
        aiohttp_client, can_receive_reviews=can_receive_reviews
    )
    admin = await prepare_user_data(
        app,
        email='<EMAIL>',
        can_receive_reviews=can_receive_reviews,
    )
    coworker1 = await prepare_user_data(
        app,
        email='<EMAIL>',
        user_role=UserRole.user.value,
        can_view_document=False,
        can_receive_reviews=can_receive_reviews,
    )
    coworker2 = await prepare_user_data(
        app,
        email='<EMAIL>',
        user_role=UserRole.user.value,
        can_view_document=False,
        can_receive_reviews=can_receive_reviews,
    )

    recipient1 = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou='00000102',
        user_role=UserRole.user.value,
        can_view_document=False,
        can_receive_reviews=can_receive_reviews,
    )
    recipient2 = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou='00000102',
        user_role=UserRole.user.value,
        can_view_document=False,
        can_receive_reviews=can_receive_reviews,
    )

    another_recipient = await prepare_user_data(
        app,
        email='<EMAIL>',
        company_edrpou='00000103',
        user_role=UserRole.user.value,
        can_view_document=False,
        can_receive_reviews=can_receive_reviews,
    )

    async def prepare_multilateral_document():
        document = await prepare_document_data(
            app,
            uploader,
            expected_owner_signatures=0,
            status_id=DocumentStatus.uploaded.value,
            is_multilateral=True,
            another_owners=[coworker1],  # coworker1 will have access to document
        )

        await prepare_flow_item(
            app,
            document_id=document.id,
            receivers=[uploader.email],
            edrpou=uploader.company_edrpou,
        )
        await prepare_flow_item(
            app,
            document_id=document.id,
            receivers=[coworker1.email],
            edrpou=uploader.company_edrpou,
        )

        await prepare_flow_item(
            app,
            document_id=document.id,
            receivers=[recipient1.email],
            edrpou=recipient1.company_edrpou,
        )
        await prepare_flow_item(
            app,
            document_id=document.id,
            receivers=[another_recipient.email],
            edrpou=another_recipient.company_edrpou,
        )
        return document

    async def prepare_bilateral_document():
        return await prepare_document_data(
            app,
            uploader,
            expected_owner_signatures=0,
            status_id=DocumentStatus.uploaded.value,
            is_multilateral=False,
            another_recipients=[recipient1],
            another_owners=[coworker1],  # coworker1 will have access to document
        )

    if is_multilateral:
        document = await prepare_multilateral_document()
    else:
        document = await prepare_bilateral_document()

    async with app['db'].acquire() as conn:
        # Add comment for recipients
        async def add_review_request(user, reviewers, status):
            await prepare_review_requests(
                client, document, user, reviewers=reviewers, expected_status=status
            )

        # Add comment for recipients
        async def add_review(user, status, use_batch, review_status=None):
            if not use_batch:
                await prepare_review(
                    client=client,
                    document=document,
                    user=user,
                    review_type=ReviewType.approve,
                    expected_status=status,
                )
            else:
                await prepare_batch_reviews(
                    client=client,
                    documents=[document],
                    user=user,
                    review_type=ReviewType.approve.value,
                    expected_status=status,
                )
            if status == HTTPStatus.CREATED:
                review = await select_review_status_by_document_id(
                    conn=conn,
                    company_edrpou=user.company_edrpou,
                    document_id=document.id,
                    version_id=None,
                )
                assert review.status == review_status, (review_status, review.status)

        async def delete_review(user, reviewer, status):
            review = await select_one(
                conn,
                (
                    sa.select([review_table.c.id]).where(
                        sa.and_(
                            review_table.c.role_id == reviewer.role_id,
                            review_table.c.document_id == document.id,
                        )
                    )
                ),
            )
            assert review is not None, f'Review not found for user {reviewer.email}'

            await prepare_delete_review(
                client, user=user, document_id=document.id, expected_status=status
            )

        # * uploader, coworker1 and admin have access to document
        # * recipient1 has access to document
        # * another_recipient has access to document

        # TESTs:

        # Coworker doesn't have access to document,
        # so can not create review or review request
        await add_review_request(coworker2, [coworker2, uploader], HTTPStatus.FORBIDDEN)
        assert len(mailbox) == 0
        await add_review(coworker2, HTTPStatus.FORBIDDEN, use_batch)
        assert len(mailbox) == 0
        assert len(mailbox) == 0

        # Other users from first company with document access can add review to document
        await add_review_request(uploader, [coworker1], HTTPStatus.OK)
        assert len(mailbox) == 1 * can_receive_reviews
        mailbox.clear()
        await add_review_request(uploader, [coworker1, admin], HTTPStatus.OK)
        assert len(mailbox) == 1 * can_receive_reviews
        mailbox.clear()
        await add_review(uploader, HTTPStatus.CREATED, use_batch, PENDING_REVIEW)
        await add_review(admin, HTTPStatus.CREATED, use_batch, PENDING_REVIEW)
        await delete_review(admin, admin, HTTPStatus.OK)
        await add_review(coworker1, HTTPStatus.CREATED, use_batch, PENDING_REVIEW)
        await add_review(admin, HTTPStatus.CREATED, use_batch, APPROVED_REVIEW)
        await delete_review(uploader, uploader, HTTPStatus.OK)
        assert len(mailbox) == 0

        # User without access can't create review request for self
        await add_review_request(recipient2, [recipient2], HTTPStatus.FORBIDDEN)
        await add_review(recipient2, HTTPStatus.FORBIDDEN, use_batch)
        await add_review(recipient1, HTTPStatus.CREATED, use_batch, APPROVED_REVIEW)
        assert len(mailbox) == 0

        if not is_multilateral:
            # Third company does not have access to document at all
            await add_review_request(another_recipient, [recipient1], HTTPStatus.FORBIDDEN)
            await add_review_request(another_recipient, [recipient2], HTTPStatus.FORBIDDEN)
            await add_review_request(another_recipient, [another_recipient], HTTPStatus.FORBIDDEN)
            assert len(mailbox) == 0
        else:
            # Third company can't create review request for another company
            await add_review_request(another_recipient, [recipient1], HTTPStatus.NOT_FOUND)
            await add_review_request(another_recipient, [recipient2], HTTPStatus.NOT_FOUND)
            assert len(mailbox) == 0
            # But user from third company with access to document can create review
            await add_review(another_recipient, HTTPStatus.CREATED, use_batch, APPROVED_REVIEW)

        # User with access from second company can create review request for coworker
        # without access
        await add_review_request(recipient1, [recipient1, recipient2], HTTPStatus.OK)
        assert len(mailbox) == 1 * can_receive_reviews
        # And then user that was without access can add review to document
        await add_review(recipient2, HTTPStatus.CREATED, use_batch, APPROVED_REVIEW)


async def test_upload_documents_with_reviews_groups(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    coworker_1 = await prepare_user_data(app, email='<EMAIL>')

    group = await prepare_group(app, 'test', user, [user, coworker_1])

    params = {
        'reviewers': [
            {
                'id': group.id,
                'type': ReviewsInfoCtx.EntityType.group,
            },
            {
                'id': coworker_1.role_id,
                'type': ReviewsInfoCtx.EntityType.role,
            },
        ],
        'is_required': True,
        'parallel_review': False,
    }

    data = prepare_form_data(DATA_PATH / 'file.xml')
    data.add_field('params', value=to_json(params), content_type='application/json')

    response = await client.post(
        UPLOAD_DOCUMENT_URL,
        data=data,
        headers=prepare_auth_headers(user),
    )
    document_id = (await response.json())['documents'][0]['id']
    assert response.status == HTTPStatus.CREATED

    response = await client.post(
        REVIEW_URL,
        json={'document_id': document_id, 'type': ReviewType.reject.value},
        headers=prepare_auth_headers(coworker_1),
    )
    assert response.status == HTTPStatus.CREATED
    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document_id])
        # two cause as user and as group
        assert len(reviews) == 2
        assert reviews[0].type == reviews[1].type == ReviewType.reject
        assert {reviews[0].group_id, reviews[1].group_id} == {group.id, None}

    # Second review in group (from user) (FORBIDDEN), cause group review is "one of"
    response = await client.post(
        REVIEW_URL,
        json={'document_id': document_id, 'type': ReviewType.approve.value},
        headers=prepare_auth_headers(user),
    )
    assert response.status == HTTPStatus.FORBIDDEN
    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document_id])
        assert len(reviews) == 2


async def test_delete_reviews_with_groups(aiohttp_client):
    """
    Test delete a few review requests (as user and as group)
     using app.reviews.views.delete_review
    """
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test', user1, [user1])

    reviewers = [
        {'id': user1.role_id, 'type': ROLE},
        {'id': user2.role_id, 'type': ROLE},
        {'id': group1.id, 'type': GROUP},
    ]
    document = await prepare_document_upload(
        app,
        client,
        user=user1,
        reviewers=reviewers,
        parallel_review=True,
        is_required_review=True,
    )

    # Approve as group and user1
    await prepare_review(client, document=document, user=user1, review_type=ReviewType.approve)

    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document.id], with_cancellations=False)
        assert len(reviews) == 2
        for review in reviews:
            assert review.type == ReviewType.approve

    await prepare_delete_review(client, user=user1, document_id=document.id)

    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document.id], with_cancellations=False)
        assert len(reviews) == 0


async def test_parallel_add_reviews(aiohttp_client):
    """Test FULL JOIN in requests and reviews"""
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test', user1, [user1])

    params = {
        'reviews_settings': {
            'reviewers': [
                {
                    'id': group1.id,
                    'type': ReviewsInfoCtx.EntityType.group,
                },
                {
                    'id': user2.role_id,
                    'type': ReviewsInfoCtx.EntityType.role,
                },
            ],
            'is_required': True,
            'is_ordered': False,
        },
    }
    document = await prepare_document_data(app, user1)

    # Add reviewers
    response = await client.patch(
        UPDATE_DOCUMENT_URL.format(document_id=document.id),
        json=params,
        headers=prepare_auth_headers(user1),
    )
    assert response.status == HTTPStatus.OK

    # Approve as group
    response = await client.post(
        REVIEW_URL,
        data=ujson.dumps({'document_id': document.id, 'type': ReviewType.approve.value}),
        headers=prepare_auth_headers(user1),
    )
    assert response.status == HTTPStatus.CREATED

    # Approve as second user
    response = await client.post(
        REVIEW_URL,
        data=ujson.dumps({'document_id': document.id, 'type': ReviewType.approve.value}),
        headers=prepare_auth_headers(user2),
    )
    assert response.status == HTTPStatus.CREATED

    # Approve as user without review_request
    response = await client.post(
        REVIEW_URL,
        data=ujson.dumps({'document_id': document.id, 'type': ReviewType.approve.value}),
        headers=prepare_auth_headers(user3),
    )
    assert response.status == HTTPStatus.CREATED

    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document.id])
        assert len(reviews) == 3
        for review in reviews:
            assert review.type == ReviewType.approve

    # Cancel review as user3 without review_request:
    response = await client.post(
        REVIEW_URL,
        data=ujson.dumps({'document_id': document.id, 'type': ReviewType.approve.value}),
        headers=prepare_auth_headers(user3),
    )
    assert response.status == HTTPStatus.CREATED

    response = await client.post(
        REVIEW_URL,
        data=ujson.dumps({'document_id': document.id, 'type': None}),
        headers=prepare_auth_headers(user3),
    )
    assert response.status == HTTPStatus.CREATED

    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document.id], with_cancellations=False)
        assert len(reviews) == 2
        for review in reviews:
            assert review.type == ReviewType.approve


async def test_add_second_review_from_group_member(aiohttp_client):
    """TEST REVIEW ORDERED"""
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='<EMAIL>')
    user4 = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test', user1, [user1, user2])

    document = await prepare_document_data(app, user1)

    # Add reviewers
    await prepare_review_requests(
        client,
        document,
        user1,
        reviewers=[group1, user3],
        is_required=True,
        is_ordered=True,
    )

    await prepare_review(client, user=user1, document=document, review_type=ReviewType.approve)
    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document.id])
        assert len(reviews) == 1
        for review in reviews:
            assert review.type == ReviewType.approve

    # Try to add second review group (from user2). Forbidden
    await prepare_review(
        client,
        user=user2,
        document=document,
        review_type=ReviewType.reject,
        expected_status=HTTPStatus.FORBIDDEN,
    )
    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document.id])
        assert len(reviews) == 1
        assert reviews[0].type == ReviewType.approve

    # Cancel review from user1 (as group)
    await prepare_review(client, user=user1, document=document, review_type=None)
    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document.id], with_cancellations=True)
        assert len(reviews) == 1
        for review in reviews:
            assert review.type is None

    # Add new review from user2 (as group)
    await prepare_review(client, user=user2, document=document, review_type=ReviewType.reject)

    # Add new review from user3
    await prepare_review(client, user=user3, document=document, review_type=ReviewType.reject)

    # Try to add second review group (from user4)
    # Forbidden cause can't add review without request in ordered reviews
    await prepare_review(
        client,
        user=user4,
        document=document,
        review_type=ReviewType.reject,
        expected_status=HTTPStatus.FORBIDDEN,
    )

    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document.id])
        assert len(reviews) == 2
        for review in reviews:
            assert review.type == ReviewType.reject


async def test_update_review_add_approve(aiohttp_client):
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')

    document = await prepare_document_data(app, user1)
    group1 = await prepare_group(app, 'test', user1, [user1])
    group2 = await prepare_group(app, 'test2', user1, [user1])

    # Add reviewers
    await prepare_review_requests(
        client,
        document=document,
        initiator=user1,
        reviewers=[group1, user1, user2, group2],
        is_required=True,
        is_ordered=True,
    )

    await prepare_review(client, user=user1, document=document, review_type=ReviewType.reject)
    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document.id])
        assert len(reviews) == 3  # 3 cause as user and as group1 and group2
        assert reviews[0].type == reviews[1].type, reviews[2].type == ReviewType.reject
        assert {reviews[0].group_id, reviews[1].group_id, reviews[2].group_id} == {
            group1.id,
            group2.id,
            None,
        }

    await prepare_review(client, user=user2, document=document, review_type=ReviewType.approve)
    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document.id])
        assert len(reviews) == 4

    # Cancel reviews
    await prepare_review(client, user=user1, document=document, review_type=None)

    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document.id])

        for review in reviews:
            if review.role_id == user1.role_id:
                assert review.type is None
                assert review.user_email == user1.email
            if review.role_id == user2.role_id:
                assert review.type == ReviewType.approve
                assert review.user_email == user2.email

    await prepare_review(client, user=user1, document=document, review_type=ReviewType.approve)
    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document.id])
        assert len(reviews) == 4
        for review in reviews:
            assert review.type == ReviewType.approve


async def test_update_review_add_approve_parallel(aiohttp_client):
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='c1@evo')

    group1 = await prepare_group(app, 'test', user1, [user1])
    group2 = await prepare_group(app, 'test2', user1, [user1])

    params = {
        'reviews_settings': {
            'reviewers': [
                {
                    'id': group1.id,
                    'type': ReviewsInfoCtx.EntityType.group,
                },
                {
                    'id': user1.role_id,
                    'type': ReviewsInfoCtx.EntityType.role,
                },
                {
                    'id': user2.role_id,
                    'type': ReviewsInfoCtx.EntityType.role,
                },
                {
                    'id': group2.id,
                    'type': ReviewsInfoCtx.EntityType.group,
                },
            ],
            'is_required': True,
            'is_ordered': True,
        },
    }
    document = await prepare_document_data(app, user1)

    # Add reviewers
    response = await client.patch(
        UPDATE_DOCUMENT_URL.format(document_id=document.id),
        json=params,
        headers=prepare_auth_headers(user1),
    )
    assert response.status == HTTPStatus.OK

    response = await client.post(
        REVIEW_URL,
        json={'document_id': document.id, 'type': ReviewType.reject.value},
        headers=prepare_auth_headers(user1),
    )
    assert response.status == HTTPStatus.CREATED
    async with app['db'].acquire() as conn:
        for review in await select_reviews(conn, document_ids=[document.id]):
            assert review.type == ReviewType.reject


async def test_deleted_group_as_reviewer(aiohttp_client):
    """
    Group is_deleted = True:
        - if group already in reviewers and have review, and we update reviewers - OK
        - if try add deleted group in reviewers - FORBIDDEN
    The same validation with deactivated roles:
     func - validate_coworkers_roles_ids_with_reviews
     tests - test_update_reviewers_with_deactivated_role,
             test_update_reviewers_with_deactivated_role_and_approve_review
    """
    app, client, user1 = await prepare_client(aiohttp_client)
    coworker_1 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test1', user1, [user1])
    group2 = await prepare_group(app, 'test2', user1, [user1])

    params = {
        'reviewers': [
            {
                'id': group1.id,
                'type': GROUP,
            },
            {
                'id': coworker_1.role_id,
                'type': ReviewsInfoCtx.EntityType.role,
            },
        ],
        'is_required': True,
    }

    data = prepare_form_data(DATA_PATH / 'file.xml')
    data.add_field('params', value=to_json(params), content_type='application/json')

    response = await client.post(
        UPLOAD_DOCUMENT_URL,
        data=data,
        headers=prepare_auth_headers(user1),
    )
    assert response.status == HTTPStatus.CREATED
    document_id = (await response.json())['documents'][0]['id']
    document = await get_document(document_id=document_id)

    await prepare_review(client, user=user1, document=document, review_type=ReviewType.approve)
    await prepare_review(client, user=coworker_1, document=document, review_type=ReviewType.approve)

    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document.id])
        assert len(reviews) == 2
        for review in reviews:
            assert review.type == ReviewType.approve
    await prepare_review_requests(client, document, user1, reviewers=[group1, coworker_1, user3])
    await prepare_review_requests(
        client,
        document,
        user1,
        reviewers=[coworker_1],
        expected_status=HTTPStatus.FORBIDDEN,
    )

    async with app['db'].acquire() as conn:
        await remove_group(conn=conn, id=group1.id, deleted_by=group1.created_by)
        await remove_group(conn=conn, id=group2.id, deleted_by=group2.created_by)

    # Update reviewers. Ignore deleted group which already have review - OK
    await prepare_review_requests(client, document, user1, reviewers=[group1, coworker_1, user3])
    # Try to add deleted group in reviewers - FORBIDDEN
    await prepare_review_requests(
        client,
        document=document,
        initiator=user1,
        reviewers=[group1, coworker_1, user3, group2],
        expected_status=HTTPStatus.FORBIDDEN,
    )


async def test_replace_review_requests_signer_notification(aiohttp_client, mailbox):
    app, client, owner = await prepare_client(aiohttp_client)
    user1 = await prepare_user_data(app, role_id=TEST_UUID_1, email='<EMAIL>')
    user2 = await prepare_user_data(app, role_id=TEST_UUID_2, email='<EMAIL>')
    user3 = await prepare_user_data(app, role_id=TEST_UUID_3, email='<EMAIL>')

    document = await prepare_document_data(app=app, owner=owner)
    resp = await request_document_update(
        client,
        user=owner,
        document=document,
        signers=[owner, user1],
    )
    assert resp.status == HTTPStatus.OK
    # One notification to signer (owner notification skip cause his is assigner)
    assert len(mailbox) == 1
    assert mailbox[0]['To'] == user1.email
    assert mailbox[0]['Subject'] == 'Ви отримали документ на підпис'
    mailbox.clear()

    await prepare_review_requests(
        client, document, owner, reviewers=[user2, user3], is_required=True
    )
    assert len(mailbox) == 2  # two notifications to reviewers
    assert {m['To'] for m in mailbox} == {user2.email, user3.email}
    assert {m['Subject'] for m in mailbox} == {'Ви отримали документ на погодження'}
    mailbox.clear()

    await prepare_review_requests(client, document, owner, reviewers=[user2, user3])
    # One notification to signers (owner notification skip cause his is assigner)
    assert len(mailbox) == 1
    assert {m['To'] for m in mailbox} == {user1.email}
    assert {m['Subject'] for m in mailbox} == {'Ви отримали документ на підпис'}
    mailbox.clear()

    await prepare_review_requests(
        client, document, owner, reviewers=[user2, user3], is_required=True
    )
    assert len(mailbox) == 0
    state = await select_review_state(app, document, owner)
    assert state.status == ReviewStatus.pending

    # Approve review, by first user,
    await prepare_review(client, document=document, user=user2, review_type='approve')
    assert len(mailbox) == 0  # no notification for parallel review

    await prepare_review_requests(client, document, owner, reviewers=[user2], is_required=True)
    state = await select_review_state(app, document, owner)
    assert state.status == ReviewStatus.approved
    # One notification to signers (owner notification skip cause his is assigner)
    assert len(mailbox) == 1
    assert {m['To'] for m in mailbox} == {user1.email}
    assert {m['Subject'] for m in mailbox} == {'Ви отримали документ на підпис'}


@pytest.mark.parametrize('parallel_review', [True, False])
async def test_deactivate_group_member_and_add_review(aiohttp_client, parallel_review):
    """
    Test case where group in review, and user deleted from this group try to add review:
    if parallel_review=True:
        OK. Added review from user (not as group)

    if parallel_review=False:
        Forbidden. Can't ddd review from group (as deleted user)
    """
    app, client, user1 = await prepare_client(aiohttp_client)
    deactivated_user = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test1', user1, [user1, deactivated_user])

    async with app['db'].acquire() as conn:
        await update_group_members(
            conn=conn,
            where_group_ids=[group1.id],
            where_role_ids=[deactivated_user.role_id],
            deleted_by=user1.role_id,
        )
    params = {
        'reviewers': [
            {
                'id': group1.id,
                'type': GROUP,
            },
        ],
        'is_required': True,
        'parallel_review': parallel_review,
    }

    data = prepare_form_data(DATA_PATH / 'file.xml')
    data.add_field('params', value=to_json(params), content_type='application/json')

    response = await client.post(
        UPLOAD_DOCUMENT_URL,
        data=data,
        headers=prepare_auth_headers(user1),
    )
    assert response.status == HTTPStatus.CREATED
    document_id = (await response.json())['documents'][0]['id']
    document = await get_document(document_id=document_id)

    expected_status = HTTPStatus.CREATED if parallel_review else HTTPStatus.FORBIDDEN

    await prepare_review(
        client,
        user=deactivated_user,
        document=document,
        review_type=ReviewType.approve,
        expected_status=expected_status,
    )


async def test_delete_single_review(aiohttp_client):
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')

    document1 = await prepare_document_upload(
        app, client, user=user1, reviewers=[{'id': user1.role_id, 'type': ROLE}]
    )
    await prepare_review(client, user=user1, document=document1, review_type=ReviewType.approve)
    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document1.id], with_cancellations=False)
        assert len(reviews) == 1
        assert reviews[0].type == ReviewType.approve
        assert reviews[0].role_id == user1.role_id
        assert reviews[0].user_email == user1.email

    # Try to delete review tby user2 (Status OK, but not update)
    await prepare_delete_review(client, user=user2, document_id=document1.id)
    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document1.id], with_cancellations=False)
        assert len(reviews) == 1
        assert reviews[0].type == ReviewType.approve
        assert reviews[0].role_id == user1.role_id
        assert reviews[0].user_email == user1.email
    # Delete review by user1
    await prepare_delete_review(client, user=user1, document_id=document1.id)
    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document1.id], with_cancellations=False)
        assert not reviews


async def test_delete_few_reviews_parallel(aiohttp_client):
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test1', user1, [user1])
    reviewers = [
        {'id': user1.role_id, 'type': ROLE},
        {'id': user2.role_id, 'type': ROLE},
        {'id': group1.id, 'type': GROUP},
    ]

    document1 = await prepare_document_upload(app, client, user=user1, reviewers=reviewers)
    await prepare_review(client, user=user1, document=document1, review_type=ReviewType.approve)
    await prepare_review(client, user=user2, document=document1, review_type=ReviewType.approve)
    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document1.id], with_cancellations=False)
        assert len(reviews) == 3
        for review in reviews:
            assert review.type == ReviewType.approve

    await prepare_delete_review(client, user=user1, document_id=document1.id)
    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document1.id], with_cancellations=False)
        assert len(reviews) == 1
        assert reviews[0].type == ReviewType.approve
        assert reviews[0].role_id == user2.role_id
        assert reviews[0].user_email == user2.email


async def test_add_review_batch(aiohttp_client):
    """
    Success test for '/internal-api/reviews-batch'
    Add review from group as user1 to document1 and add review as user1 to document2
    """
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test1', user1, [user1, user2])

    document1 = await prepare_document_upload(
        app,
        client,
        user=user1,
        reviewers=[
            {'id': group1.id, 'type': GROUP},
            {'id': user2.role_id, 'type': ROLE},
        ],
        is_required_review=True,
    )

    document2 = await prepare_document_upload(app, client, user=user1)
    data = [
        {'document_id': document1.id, 'type': ReviewType.approve.value},
        {'document_id': document2.id, 'type': ReviewType.approve.value},
    ]
    response = await client.post(
        REVIEWS_URL,
        json=data,
        headers=prepare_auth_headers(user1),
    )
    assert response.status == HTTPStatus.MULTI_STATUS

    async with app['db'].acquire() as conn:
        [review1] = await select_reviews(conn, document_ids=[document1.id])
        assert review1.group_id == group1.id
        assert review1.role_id == user1.role_id
        assert review1.user_email == user1.email

        [review2] = await select_reviews(conn, document_ids=[document2.id])
        assert review2.group_id is None
        assert review2.role_id == user1.role_id
        assert review2.user_email == user1.email


@pytest.mark.parametrize('parallel_review', [True, False])
async def test_add_review_batch_multiple(aiohttp_client, parallel_review):
    """
    Add reviews from group as user1 and as single reviewer
    """
    app, client, user1 = await prepare_client(aiohttp_client)
    group1 = await prepare_group(app, 'test1', user1, [user1])

    document1 = await prepare_document_upload(
        app,
        client,
        user=user1,
        reviewers=[
            {'id': group1.id, 'type': GROUP},
            {'id': user1.role_id, 'type': ROLE},
        ],
        is_required_review=True,
        parallel_review=parallel_review,
    )

    data = [{'document_id': document1.id, 'type': ReviewType.approve.value}]
    response = await client.post(
        REVIEWS_URL,
        json=data,
        headers=prepare_auth_headers(user1),
    )
    assert response.status == HTTPStatus.MULTI_STATUS

    async with app['db'].acquire() as conn:
        [review1, review2] = await select_reviews(conn, document_ids=[document1.id])
        assert {review1.group_id, review2.group_id} == {group1.id, None}
        assert review1.role_id == user1.role_id
        assert review1.user_email == user1.email
        assert review2.role_id == user1.role_id
        assert review2.user_email == user1.email


@pytest.mark.parametrize('use_batch', [True, False])
async def test_add_review_order_validation(aiohttp_client, use_batch):
    """
    Test validation for review order (related to DOC-7405)

    Expected: user from group can't add review (as batch or single),
        because approval from user 3 is currently awaited (third user's queue)
    """
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test1', user1, [user1, user2])

    document = await prepare_document_data(
        app=app,
        owner=user1,
        id=TEST_UUID_1,
        date_listing='2022-01-01T00:00:00+2:00',
    )
    await prepare_review_requests(
        client, document, user1, reviewers=[user3, group1], is_required=True, is_ordered=True
    )

    if use_batch:
        await prepare_batch_reviews(
            client=client,
            documents=[document],
            user=user2,
            review_type=ReviewType.approve.value,
            expected_status=HTTPStatus.FORBIDDEN,
        )
    else:
        await prepare_review(
            client,
            user=user2,
            document=document,
            review_type=ReviewType.approve,
            expected_status=HTTPStatus.FORBIDDEN,
        )


@pytest.mark.parametrize('parallel_review', [True, False])
async def test_add_review_batch_multiple_group_review_exist(aiohttp_client, parallel_review):
    """
    The same as test_add_review_batch_multiple
    Add reviews from group as user1 and as single reviewer
    """
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test1', user1, [user1, user2])

    document1 = await prepare_document_upload(
        app,
        client,
        user=user1,
        reviewers=[
            {'id': group1.id, 'type': GROUP},
            {'id': user1.role_id, 'type': ROLE},
        ],
        is_required_review=True,
        parallel_review=parallel_review,
    )
    # Add review from user2 (as group)
    await prepare_review(client, user=user2, document=document1, review_type=ReviewType.approve)

    data = [{'document_id': document1.id, 'type': ReviewType.approve.value}]
    response = await client.post(
        REVIEWS_URL,
        json=data,
        headers=prepare_auth_headers(user1),
    )
    assert response.status == HTTPStatus.MULTI_STATUS

    async with app['db'].acquire() as conn:
        [review1, review2] = await select_reviews(conn, document_ids=[document1.id])

        assert {review1.group_id, review2.group_id} == {group1.id, None}
        assert {review1.role_id, review2.role_id} == {user1.role_id, user2.role_id}


@pytest.mark.parametrize('use_batch', [True, False])
async def test_add_review_versioned(aiohttp_client, use_batch):
    """
    Test case for duplicated review in versioned documents.
    Coverage single and multiple review
    """
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test', user1, [user1, user2])

    document = await prepare_document_data(app, user1)

    # Add reviewers
    await prepare_review_requests(
        client,
        document,
        user1,
        reviewers=[group1, user3],
    )
    async with services.db.acquire() as conn:
        requests = await select_review_requests(
            conn=conn,
            company_id=user1.company_id,
            document_ids=[document.id],
        )
        request_group = next(r for r in requests if r.to_group_id == group1.id)
        request_user3 = next(r for r in requests if r.to_role_id == user3.role_id)

        async def get_statuses() -> tuple[ReviewStatusDB, list[ReviewStatusDB]]:
            _statuses = await select_reviews_statuses(
                conn=conn,
                company_edrpou=user1.company_edrpou,
                documents_ids=[document.id],
            )
            _status_last = next((s for s in _statuses if s.is_last), None)
            _status_prev = [s for s in _statuses if not s.is_last]
            return _status_last, _status_prev

        status, prev_statuses = await get_statuses()
        assert status.document_version_id is None
        assert status.status == ReviewStatus.pending
        assert status.is_last is True
        assert len(status.next_review_requests) == 2
        assert set(status.next_review_requests) == {request_group.id, request_user3.id}

        assert len(prev_statuses) == 0

        # mark document as versioned
        version1 = await document_versions_utils.add_new_upload_document_version(
            conn=conn,
            validated=ValidatedVersionUpload(
                document=Document.from_row(document),
                content=b'First version content',
                extension='.pdf',
                version_count=1,
                date_created='2021-01-01T00:00:00Z',
            ),
            user=User.from_row(user1),
            source=Source.api_internal,
        )
        await prepare_review_status_db(conn, User.from_row(user1), document_id=document.id)

        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version1.id
        assert status.status == ReviewStatus.pending
        assert status.is_last is True
        assert len(status.next_review_requests) == 2
        assert set(status.next_review_requests) == {request_group.id, request_user3.id}

        assert len(prev_statuses) == 1
        assert all(s.is_last is False for s in prev_statuses)

        if use_batch:
            await prepare_batch_reviews(
                client=client,
                documents=[document],
                user=user1,
                review_type=ReviewType.approve,
                expected_status=HTTPStatus.CREATED,
            )
        else:
            await prepare_review(
                client, user=user1, document=document, review_type=ReviewType.approve
            )

        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version1.id
        assert status.status == ReviewStatus.pending
        assert status.is_last is True
        assert len(status.next_review_requests) == 1
        assert set(status.next_review_requests) == {request_user3.id}

        assert len(prev_statuses) == 1
        assert all(s.is_last is False for s in prev_statuses)

        # Add second version - review state should copied from first version
        version1 = await document_versions_utils.add_new_upload_document_version(
            conn=conn,
            validated=ValidatedVersionUpload(
                document=Document.from_row(document),
                content=b'Second version content',
                extension='.pdf',
                version_count=2,
                date_created='2022-01-01T00:00:00Z',
            ),
            user=User.from_row(user1),
            source=Source.api_internal,
        )

        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version1.id
        assert status.status == ReviewStatus.pending
        assert status.is_last is True
        assert len(status.next_review_requests) == 1
        assert set(status.next_review_requests) == {request_user3.id}

        assert len(prev_statuses) == 2
        assert all(s.is_last is False for s in prev_statuses)

        if use_batch:
            await prepare_batch_reviews(
                client=client,
                documents=[document],
                user=user1,
                review_type=ReviewType.reject,
                expected_status=HTTPStatus.CREATED,
            )
        else:
            await prepare_review(
                client, user=user1, document=document, review_type=ReviewType.reject
            )

        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version1.id
        assert status.status == ReviewStatus.rejected
        assert status.is_last is True
        assert len(status.next_review_requests) == 1
        assert set(status.next_review_requests) == {request_user3.id}

        assert len(prev_statuses) == 2
        assert all(s.is_last is False for s in prev_statuses)

        # No matter if any more review added to document
        # if somebody rejected it - its status should be - rejected
        if use_batch:
            await prepare_batch_reviews(
                client=client,
                documents=[document],
                user=user3,
                review_type=ReviewType.approve,
                expected_status=HTTPStatus.CREATED,
            )
        else:
            await prepare_review(
                client, user=user3, document=document, review_type=ReviewType.approve
            )

        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version1.id
        assert status.status == ReviewStatus.rejected
        assert status.is_last is True
        assert len(status.next_review_requests) == 0

        assert len(prev_statuses) == 2
        assert all(s.is_last is False for s in prev_statuses)

        # Add third version, still review state should copied from second version
        version3 = await document_versions_utils.add_new_upload_document_version(
            conn=conn,
            validated=ValidatedVersionUpload(
                document=Document.from_row(document),
                content=b'Third version content',
                extension='.pdf',
                version_count=3,
                date_created='2023-01-01T00:00:00Z',
            ),
            user=User.from_row(user1),
            source=Source.api_internal,
        )

        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version3.id
        assert status.status == ReviewStatus.rejected
        assert status.is_last is True
        assert len(status.next_review_requests) == 0

        assert len(prev_statuses) == 3
        assert all(s.is_last is False for s in prev_statuses)

        # Add forth version, still review state should copied from third version
        version4 = await document_versions_utils.add_new_upload_document_version(
            conn=conn,
            validated=ValidatedVersionUpload(
                document=Document.from_row(document),
                content=b'Forth version content',
                extension='.pdf',
                version_count=4,
                date_created='2024-01-01T00:00:00Z',
            ),
            user=User.from_row(user1),
            source=Source.api_internal,
        )

        # Add approval from user1, user who rejected document previously,
        # but now review should be approved because all previous reviews
        # were approved as well
        if use_batch:
            await prepare_batch_reviews(
                client=client,
                documents=[document],
                user=user1,
                review_type=ReviewType.approve,
                expected_status=HTTPStatus.CREATED,
            )
        else:
            await prepare_review(
                client, user=user1, document=document, review_type=ReviewType.approve
            )

        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version4.id
        assert status.status == ReviewStatus.approved
        assert status.is_last is True
        assert len(status.next_review_requests) == 0

        assert len(prev_statuses) == 4
        assert all(s.is_last is False for s in prev_statuses)

        reviews = await select_reviews(conn, document_ids=[document.id])
        assert len(reviews) == 7

        document_version = await get_latest_document_version_available_for_company(
            conn=conn,
            document_id=document.id,
            company_edrpou=user1.company_edrpou,
        )
        # In last document version exist all reviews.
        reviews = await select_reviews(
            conn,
            document_with_version_ids=[(document.id, document_version.id)],
        )
        assert len(reviews) == 2

        version_count = await get_version_count(conn=conn, document_id=document.id)
        assert version_count == 4


@pytest.mark.parametrize('use_batch', [True, False])
async def test_add_review_versioned_restart_review(aiohttp_client, use_batch):
    """
    Test case for duplicated review in versioned documents.
    Coverage single and multiple review
    """
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test', user1, [user1, user2])

    await set_company_config(
        app,
        company_id=user1.company_id,
        version_settings={'review_flow': VersionReviewFlow.restarted},
    )

    document = await prepare_document_data(app, user1)

    # Add reviewers
    await prepare_review_requests(
        client,
        document,
        user1,
        reviewers=[group1, user3],
    )
    async with services.db.acquire() as conn:
        requests = await select_review_requests(
            conn=conn,
            company_id=user1.company_id,
            document_ids=[document.id],
        )
        request_group = next(r for r in requests if r.to_group_id == group1.id)
        request_user3 = next(r for r in requests if r.to_role_id == user3.role_id)

        async def get_statuses() -> tuple[ReviewStatusDB, list[ReviewStatusDB]]:
            _statuses = await select_reviews_statuses(
                conn=conn,
                company_edrpou=user1.company_edrpou,
                documents_ids=[document.id],
            )
            _status_last = next((s for s in _statuses if s.is_last), None)
            _status_prev = [s for s in _statuses if not s.is_last]
            return _status_last, _status_prev

        status, prev_statuses = await get_statuses()
        assert status.document_version_id is None
        assert status.status == ReviewStatus.pending
        assert status.is_last is True
        assert len(status.next_review_requests) == 2
        assert set(status.next_review_requests) == {request_group.id, request_user3.id}

        assert len(prev_statuses) == 0

        # mark document as versioned
        version1 = await document_versions_utils.add_new_upload_document_version(
            conn=conn,
            validated=ValidatedVersionUpload(
                document=Document.from_row(document),
                content=b'First version content',
                extension='.pdf',
                version_count=1,
                date_created='2021-01-01T00:00:00Z',
            ),
            user=User.from_row(user1),
            source=Source.api_internal,
        )
        await prepare_review_status_db(conn, User.from_row(user1), document_id=document.id)

        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version1.id
        assert status.status == ReviewStatus.pending
        assert status.is_last is True
        assert len(status.next_review_requests) == 2
        assert set(status.next_review_requests) == {request_group.id, request_user3.id}

        assert len(prev_statuses) == 1
        assert all(s.is_last is False for s in prev_statuses)

        if use_batch:
            await prepare_batch_reviews(
                client=client,
                documents=[document],
                user=user1,
                review_type=ReviewType.approve,
                expected_status=HTTPStatus.CREATED,
            )
        else:
            await prepare_review(
                client, user=user1, document=document, review_type=ReviewType.approve
            )

        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version1.id
        assert status.status == ReviewStatus.pending
        assert status.is_last is True
        assert len(status.next_review_requests) == 1
        assert set(status.next_review_requests) == {request_user3.id}

        assert len(prev_statuses) == 1
        assert all(s.is_last is False for s in prev_statuses)

        # Add second version
        version1 = await document_versions_utils.add_new_upload_document_version(
            conn=conn,
            validated=ValidatedVersionUpload(
                document=Document.from_row(document),
                content=b'Second version content',
                extension='.pdf',
                version_count=2,
                date_created='2022-01-01T00:00:00Z',
            ),
            user=User.from_row(user1),
            source=Source.api_internal,
        )

        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version1.id
        assert status.status == ReviewStatus.pending
        assert status.is_last is True
        assert len(status.next_review_requests) == 2
        assert set(status.next_review_requests) == {request_group.id, request_user3.id}

        assert len(prev_statuses) == 2
        assert all(s.is_last is False for s in prev_statuses)

        if use_batch:
            await prepare_batch_reviews(
                client=client,
                documents=[document],
                user=user1,
                review_type=ReviewType.reject,
                expected_status=HTTPStatus.CREATED,
            )
        else:
            await prepare_review(
                client, user=user1, document=document, review_type=ReviewType.reject
            )

        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version1.id
        assert status.status == ReviewStatus.rejected
        assert status.is_last is True
        assert len(status.next_review_requests) == 1
        assert set(status.next_review_requests) == {request_user3.id}

        assert len(prev_statuses) == 2
        assert all(s.is_last is False for s in prev_statuses)

        if use_batch:
            await prepare_batch_reviews(
                client=client,
                documents=[document],
                user=user3,
                review_type=ReviewType.approve,
                expected_status=HTTPStatus.CREATED,
            )
        else:
            await prepare_review(
                client, user=user3, document=document, review_type=ReviewType.approve
            )

        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version1.id
        assert status.status == ReviewStatus.rejected
        assert status.is_last is True
        assert len(status.next_review_requests) == 0

        assert len(prev_statuses) == 2
        assert all(s.is_last is False for s in prev_statuses)

        # Add third version
        version3 = await document_versions_utils.add_new_upload_document_version(
            conn=conn,
            validated=ValidatedVersionUpload(
                document=Document.from_row(document),
                content=b'Third version content',
                extension='.pdf',
                version_count=3,
                date_created='2023-01-01T00:00:00Z',
            ),
            user=User.from_row(user1),
            source=Source.api_internal,
        )

        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version3.id
        assert status.status == ReviewStatus.pending
        assert status.is_last is True
        assert len(status.next_review_requests) == 2
        assert set(status.next_review_requests) == {request_group.id, request_user3.id}

        assert len(prev_statuses) == 3
        assert all(s.is_last is False for s in prev_statuses)

        if use_batch:
            await prepare_batch_reviews(
                client=client,
                documents=[document],
                user=user1,
                review_type=ReviewType.approve,
                expected_status=HTTPStatus.CREATED,
            )
        else:
            await prepare_review(
                client, user=user1, document=document, review_type=ReviewType.approve
            )

        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version3.id
        assert status.status == ReviewStatus.pending
        assert status.is_last is True
        assert len(status.next_review_requests) == 1
        assert set(status.next_review_requests) == {request_user3.id}

        assert len(prev_statuses) == 3
        assert all(s.is_last is False for s in prev_statuses)

        reviews = await select_reviews(conn, document_ids=[document.id])
        assert len(reviews) == 4

        document_version = await get_latest_document_version_available_for_company(
            conn=conn,
            document_id=document.id,
            company_edrpou=user1.company_edrpou,
        )
        # In last document version exist only one review.
        reviews = await select_reviews(
            conn,
            document_with_version_ids=[(document.id, document_version.id)],
        )
        assert len(reviews) == 1

        version_count = await get_version_count(conn=conn, document_id=document.id)
        assert version_count == 3


async def test_add_review_batch_to_versioned_and_unversioned_docs(aiohttp_client):
    """
    Test case for batch reviews requests.
    Add approval to document with a few versions and to document without versions
    """
    app, client, user1 = await prepare_client(aiohttp_client)

    document_with_version = await prepare_document_data(app, user1)
    await prepare_review_requests(
        client,
        document_with_version,
        user1,
        reviewers=[user1],
    )

    document_ = await prepare_document_data(app, user1)
    await prepare_review_requests(
        client,
        document_,
        user1,
        reviewers=[user1],
    )
    async with app['db'].acquire() as conn:
        await add_document_content_version(
            conn=conn,
            document_id=document_with_version.id,
            company_edrpou=user1.company_edrpou,
            company_id=user1.company_id,
            role_id=user1.role_id,
            upload_type=DocumentVersionType.new_upload,
            content=b'new version content',
            name='Test name',
            extension='.pdf',
        )

    await prepare_review(
        client, user=user1, document=document_with_version, review_type=ReviewType.approve
    )

    async with app['db'].acquire() as conn:
        await add_document_content_version(
            conn=conn,
            document_id=document_with_version.id,
            company_edrpou=user1.company_edrpou,
            company_id=user1.company_id,
            role_id=user1.role_id,
            upload_type=DocumentVersionType.new_upload,
            content=b'new version content2222',
            name='Test name',
            extension='.pdf',
        )
        await prepare_review(
            client, user=user1, document=document_with_version, review_type=ReviewType.reject
        )

    async with app['db'].acquire() as conn:
        await add_document_content_version(
            conn=conn,
            document_id=document_with_version.id,
            company_edrpou=user1.company_edrpou,
            company_id=user1.company_id,
            role_id=user1.role_id,
            upload_type=DocumentVersionType.new_upload,
            content=b'new version content3',
            name='Test name',
            extension='.pdf',
        )

    await prepare_batch_reviews(
        client=client,
        documents=[document_with_version, document_],
        user=user1,
        review_type=ReviewType.approve,
        expected_status=HTTPStatus.CREATED,
    )

    async with app['db'].acquire() as conn:
        document_version = await get_latest_document_version_available_for_company(
            conn=conn,
            document_id=document_with_version.id,
            company_edrpou=user1.company_edrpou,
        )
        reviews = await select_reviews(
            conn,
            last_reviews_only=True,
            document_with_version_ids=[(document_with_version.id, document_version.id)],
        )
        assert reviews[0].document_id == document_with_version.id
        assert reviews[0].document_version_id == document_version.id
        assert reviews[0].type == ReviewType.approve

        reviews = await select_reviews(conn, document_ids=[document_.id], last_reviews_only=True)
        assert reviews[0].document_id == document_.id
        assert reviews[0].document_version_id is None
        assert reviews[0].type == ReviewType.approve


async def test_add_reviews_batch_duplicate_case(aiohttp_client):
    """
    Test case for batch duplicate reviews,
     when user use second batch approve to already approved documents. (DOC-6233)
    """
    app, client, user1 = await prepare_client(aiohttp_client)

    document_1 = await prepare_document_data(app, user1)
    document_2 = await prepare_document_data(app, user1)
    document_3 = await prepare_document_data(app, user1)
    for doc in [document_1, document_2, document_3]:
        await prepare_review_requests(
            client,
            doc,
            user1,
            reviewers=[user1],
        )

    await prepare_batch_reviews(
        client=client,
        documents=[document_1, document_2, document_3],
        user=user1,
        review_type=ReviewType.approve,
        expected_status=HTTPStatus.CREATED,
    )

    await prepare_batch_reviews(
        client=client,
        documents=[document_1, document_2, document_3],
        user=user1,
        review_type=ReviewType.approve,
        expected_status=HTTPStatus.CREATED,
    )

    async with app['db'].acquire() as conn:
        reviews = await select_reviews(
            conn,
            document_ids=[document_1.id, document_2.id, document_3.id],
            last_reviews_only=True,
        )
        assert len(reviews) == 3


async def test_add_reviews_batch_negative(aiohttp_client):
    """
    Negative cases for '/internal-api/reviews-batch'
    Try to add review to:
     - document without access
     - not exist document
     - document with sequential review (not the user's turn)
     - document with sequential review (user not in review-requests)
     - document without review-requests, but with access (success)
     - document without user as reviewer (success)
    """

    class FakeDocumentId:
        id = 'd0faed13-f83d-4fc4-9481-11951f9aef9c'

    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(
        app, email='<EMAIL>', user_role=UserRole.user.value, can_view_document=False
    )
    group1 = await prepare_group(app, 'test1', user1, [user1])

    # user2 can't get access
    document = await prepare_document_data(app, user1)
    # Without review-requests, but with access
    document_valid = await prepare_document_data(app, user1, another_owners=[user2])
    # user2 as reviewer
    document_valid2 = await prepare_document_upload(
        app, client, user=user1, reviewers=[{'id': user2.role_id, 'type': ROLE}]
    )

    # Ordered documents
    # user2 is not the following from whom the approval is expected
    document_order1 = await prepare_document_upload(
        app,
        client,
        user=user1,
        reviewers=[
            {'id': group1.id, 'type': GROUP},
            {'id': user2.role_id, 'type': ROLE},
        ],
        parallel_review=False,
    )
    # user2 isn't in review-requests
    document_order2 = await prepare_document_upload(
        app,
        client,
        user=user1,
        reviewers=[{'id': group1.id, 'type': GROUP}],
        parallel_review=False,
    )
    documents = [
        document,
        FakeDocumentId,
        document_order1,
        document_order2,
        document_valid,
        document_valid2,
    ]
    statuses = [
        HTTPStatus.FORBIDDEN,
        HTTPStatus.NOT_FOUND,
        HTTPStatus.FORBIDDEN,
        HTTPStatus.FORBIDDEN,
        HTTPStatus.CREATED,
        HTTPStatus.CREATED,
    ]
    response_data = await prepare_batch_reviews(
        client, user=user2, documents=documents, review_type=ReviewType.approve
    )
    for response, document, status in zip(response_data, documents, statuses):
        assert response['document_id'] == document.id
        assert response['status'] == status


async def test_graphql_reviews_fields(aiohttp_client):
    """
    Test reviews and reviewRequests new fields (groupID)
    """
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test1', user1, [user1])
    reviewers = [
        {'id': user1.role_id, 'type': ROLE},
        {'id': group1.id, 'type': GROUP},
    ]

    document1 = await prepare_document_upload(app, client, user=user1, reviewers=reviewers)
    # Added 2 review (as user1, and as group1)
    await prepare_review(client, user=user1, document=document1, review_type=ReviewType.approve)
    await prepare_review(client, user=user2, document=document1, review_type=ReviewType.approve)
    async with app['db'].acquire() as conn:
        reviews = await select_reviews(conn, document_ids=[document1.id])
        assert len(reviews) == 3

    query = REVIEWS_AND_REQUEST_QUERY.replace('document_id', document1.id)
    data = await fetch_graphql(client, query, prepare_auth_headers(user1))

    reviews = data['document']['reviews']
    assert len(reviews) == 3
    reviews[0]['group_id'] = None
    reviews[1]['group_id'] = group1.id
    reviews[2]['group_id'] = None

    review_requests = data['document']['reviewRequests']
    assert len(review_requests) == 2
    review_requests[0]['group_id'] = None
    review_requests[1]['group_id'] = group1.id


async def test_reviews_order(aiohttp_client):
    """
    Test reviews order
    """

    async def assert_expected_reviews_count(
        count_requests: int = 0, count_reviews: int = 0, order: bool = False
    ):
        query = (
            f'{{ document(id: "{document.id}") {{ '
            f'reviewRequests {{ id order }}'
            f'reviews (add_is_last_condition: true) {{ id }}'
            f'}} }}'
        )

        data = await fetch_graphql(client, query, prepare_auth_headers(user1))
        requests = data['document']['reviewRequests']
        reviews = data['document']['reviews']
        assert len(requests) == count_requests
        assert len(reviews) == count_reviews
        for request in requests:
            assert bool(request['order']) == order

    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test1', user1, [user1])

    document = await prepare_document_data(app, user1)
    await prepare_review(client, user=user1, document=document, review_type=ReviewType.approve)
    await assert_expected_reviews_count(count_reviews=1)

    await prepare_review_requests(
        client, document, user1, reviewers=[user1, group1, user2], is_ordered=True
    )
    await assert_expected_reviews_count(3, 1, True)

    await prepare_review(client, user=user1, document=document, review_type=ReviewType.approve)
    await assert_expected_reviews_count(3, 2, True)

    await prepare_review_requests(
        client,
        document,
        user1,
        reviewers=[user1, group1, user2, user3],
        is_ordered=True,
    )
    await assert_expected_reviews_count(4, 2, True)

    await prepare_review_requests(
        client,
        document,
        user1,
        reviewers=[group1, user1, user2, user3],
        is_ordered=True,
    )
    await assert_expected_reviews_count(4, 2, True)

    await prepare_review(client, user=user2, document=document, review_type=ReviewType.approve)
    await assert_expected_reviews_count(4, 3, True)
    await prepare_review_requests(
        client,
        document=document,
        initiator=user1,
        reviewers=[group1, user1, user3, user2],
        is_ordered=True,
        expected_status=HTTPStatus.FORBIDDEN,
    )
    await prepare_review_requests(client, document, user1, reviewers=[group1, user1, user3, user2])
    await assert_expected_reviews_count(4, 3)
    await prepare_review_requests(
        client, document, user1, reviewers=[group1, user1, user2], is_ordered=True
    )
    await assert_expected_reviews_count(3, 3, True)


async def test_graph_reviews_and_review_requests(aiohttp_client):
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='<EMAIL>')

    document = await prepare_document_data(app, user1)
    await prepare_review(client, user=user1, document=document, review_type=ReviewType.approve)
    query = REVIEWS_AND_REQUEST_QUERY.replace('document_id', document.id)
    data = await fetch_graphql(client, query, prepare_auth_headers(user1))
    reviews = data['document']['reviews']
    assert len(reviews) == 1
    assert reviews[0]['roleId'] == user1.role_id

    review_requests = data['document']['reviewRequests']
    assert len(review_requests) == 0

    await prepare_review_requests(
        client, document, user1, reviewers=[user1, user2, user3], is_ordered=True
    )
    await prepare_review_requests(client, document, user1, reviewers=[user3, user2, user1])

    data = await fetch_graphql(client, query, prepare_auth_headers(user1))
    assert len(data['document']['reviewRequests']) == 3


async def test_change_reviewers_and_order(aiohttp_client):
    """
    If we add review without review-request, review-request is not created.
    The same if after that we add other reviewers without order.
    But if we add review-requests with order - review-request for first reviewer will be created

    Test this and case where we try order and move reviewer with review
    from begin of list to end (after user without review)
    """
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='<EMAIL>')

    document = await prepare_document_data(app, user1)
    query = REVIEWS_AND_REQUEST_QUERY.replace('document_id', document.id)

    await prepare_review(client, user=user1, document=document, review_type=ReviewType.approve)

    data = await fetch_graphql(client, query, prepare_auth_headers(user1))
    assert len(data['document']['reviews']) == 1
    assert len(data['document']['reviewRequests']) == 0

    # OK
    await prepare_review_requests(
        client,
        document=document,
        initiator=user1,
        reviewers=[user2, user3],
        is_ordered=True,
    )

    # OK
    await prepare_review_requests(client, document, user1, reviewers=[user1, user2, user3])

    # Forbidden, cause user2 without review is early than user1, with review.
    await prepare_review_requests(
        client,
        document=document,
        initiator=user1,
        reviewers=[user2, user1, user3],
        is_ordered=True,
        expected_status=HTTPStatus.FORBIDDEN,
    )
    # If is_ordered=False - OK
    await prepare_review_requests(client, document, user1, reviewers=[user2, user1, user3])


async def test_add_the_same_group(aiohttp_client):
    """
    OK, cause we use set and save order
    """
    app, client, user = await prepare_client(aiohttp_client)
    group = await prepare_group(app, 'test1', user, [user])

    document = await prepare_document_data(app, user)
    query = (
        f'{{ document(id: "{document.id}") {{ '
        f'reviewRequests {{ id documentId fromRoleId toRoleId status order }}'
        f'reviews {{ id documentId roleId type groupId }}'
        f'}} }}'
    )

    await prepare_review_requests(client, document, user, reviewers=[group, group])
    data = await fetch_graphql(client, query, prepare_auth_headers(user))
    assert len(data['document']['reviews']) == 0
    assert len(data['document']['reviewRequests']) == 1

    await prepare_review_requests(
        client, document, user, reviewers=[group, user, group], is_ordered=True
    )
    data = await fetch_graphql(client, query, prepare_auth_headers(user))
    assert len(data['document']['reviews']) == 0
    assert len(data['document']['reviewRequests']) == 2


async def test_add_invalid_group(aiohttp_client):
    """
    Test negative case when we add to review requests  not exist group,
    or group from another company
    """

    class FakeGroupId:
        id = str(uuid.uuid4())

    app, client, user1 = await prepare_client(aiohttp_client)
    other_company_user = await prepare_user_data(
        app, email='<EMAIL>', company_edrpou=TEST_COMPANY_EDRPOU_2
    )

    group1 = await prepare_group(app, 'test1', user1, [user1])
    other_group = await prepare_group(app, 'test2', other_company_user, [other_company_user])

    document = await prepare_document_data(app, user1)
    query = REVIEWS_AND_REQUEST_QUERY.replace('document_id', document.id)
    await prepare_review_requests(
        client,
        document=document,
        initiator=user1,
        reviewers=[group1, other_group],
        expected_status=HTTPStatus.NOT_FOUND,
    )
    await prepare_review_requests(
        client,
        document=document,
        initiator=user1,
        reviewers=[group1, FakeGroupId],
        expected_status=HTTPStatus.NOT_FOUND,
    )
    data = await fetch_graphql(client, query, prepare_auth_headers(user1))
    assert len(data['document']['reviewRequests']) == 0


async def test_group_as_link_in_graphql(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    group = await prepare_group(app, 'test1', user, [user])
    document = await prepare_document_data(app, user)
    await prepare_review_requests(client, document, user, reviewers=[group])
    await prepare_review(client, user=user, document=document, review_type=ReviewType.approve)
    query = (
        f'{{ document(id: "{document.id}") {{ '
        'reviewRequests { id documentId fromRoleId toRoleId status order toGroupId '
        'toGroup { id name } }'
        'reviews { id documentId roleId type groupId group { id name } }'
        '} }'
    )
    data = await fetch_graphql(client, query, prepare_auth_headers(user))
    assert len(data['document']['reviewRequests']) == len(data['document']['reviews']) == 1
    group_review_data = data['document']['reviews'][0]['group']
    group_review_request_data = data['document']['reviewRequests'][0]['toGroup']
    assert group_review_data['id'] == group_review_request_data['id'] == group.id
    assert group_review_data['name'] == group_review_request_data['name'] == group.name


async def test_review_ordered_sent_recipient_add_review(aiohttp_client):
    """
    Case: https://tabula-rasa.atlassian.net/browse/DOC-6175
    STR:
     - завантажити документ
     - встановити на ньому почергове погодження і погоджувача, але не погоджувати
     - підписати і надіслати  контрагенту
     - перейти до контрагента і спробувати погодити документ
    ER: документ погоджено
    """

    app, client, owner = await prepare_client(
        aiohttp_client,
        create_billing_account=True,
    )
    recipient = await prepare_user_data(
        app,
        email=TEST_RECIPIENT_EMAIL,
        company_edrpou=TEST_RECIPIENT_EDRPOU,
    )
    document = await prepare_document_data(
        app,
        owner,
        first_sign_by=FirstSignBy.owner,
    )
    await prepare_review_requests(client, document, owner, reviewers=[owner], is_ordered=True)

    response = await client.get(
        API_V2_REVIEWS_REQUESTS_URL.format(document_id=document.id),
        headers=prepare_auth_headers(owner),
    )
    assert response.status == 200
    await sign_and_send_document(
        client,
        document.id,
        owner,
        recipient_email=TEST_RECIPIENT_EMAIL,
        recipient_edrpou=TEST_RECIPIENT_EDRPOU,
    )
    await prepare_review(client, user=recipient, document=document, review_type=ReviewType.approve)


async def test_update_review_add_approve_with_groups(aiohttp_client):
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='<EMAIL>')

    document = await prepare_document_data(app, user1)
    group1 = await prepare_group(app, 'test', user1, [user1])

    # Add reviewers
    await prepare_review_requests(
        client,
        document=document,
        initiator=user1,
        reviewers=[group1, user2, user1, user3],
        is_required=True,
        is_ordered=True,
    )
    await prepare_review(client, user=user1, document=document, review_type=ReviewType.approve)
    await prepare_review(client, user=user2, document=document, review_type=ReviewType.approve)
    await prepare_review(client, user=user3, document=document, review_type=ReviewType.approve)

    async with app['db'].acquire() as conn:
        status = await select_review_status_by_document_id(
            conn=conn,
            company_edrpou=user1.company_edrpou,
            document_id=document.id,
            version_id=None,
        )
        assert status.status == ReviewStatus.approved


async def test_add_reviews_when_user_in_two_groups_ordered(aiohttp_client):
    """
    TEST REVIEW ORDERED
    When two groups approved with same user
    """
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test', user1, [user1, user2])
    group2 = await prepare_group(app, 'test2', user1, [user1])

    document = await prepare_document_data(app, user1)

    # Add reviewers
    await prepare_review_requests(
        client,
        document,
        user1,
        reviewers=[group1, user3, group2],
        is_required=True,
        is_ordered=True,
    )
    async with app['db'].acquire() as conn:
        review_requests = await select_review_requests(
            conn=conn,
            company_id=user1.company_id,
            document_ids=[document.id],
            sort_by_order=True,
        )

    review_request_group1 = next(r for r in review_requests if r.to_group_id == group1.id)
    review_request_user3 = next(r for r in review_requests if r.to_role_id == user3.role_id)

    async with app['db'].acquire() as conn:
        status = await select_review_status_by_document_id(
            conn=conn,
            company_edrpou=user1.company_edrpou,
            document_id=document.id,
            version_id=None,
        )
    assert status.status == ReviewStatus.pending
    assert status.next_review_requests == [review_request_group1.id]

    await prepare_review(client, user=user1, document=document, review_type=ReviewType.approve)
    async with app['db'].acquire() as conn:
        status = await select_review_status_by_document_id(
            conn=conn,
            company_edrpou=user1.company_edrpou,
            document_id=document.id,
            version_id=None,
        )
    assert status.status == ReviewStatus.pending
    assert status.next_review_requests == [review_request_user3.id]

    await prepare_review(client, user=user3, document=document, review_type=ReviewType.approve)
    async with app['db'].acquire() as conn:
        status = await select_review_status_by_document_id(
            conn=conn,
            company_edrpou=user1.company_edrpou,
            document_id=document.id,
            version_id=None,
        )
    assert status.next_review_requests == []
    assert status.status == ReviewStatus.approved


async def test_add_reviews_when_user_in_two_groups_parallel(aiohttp_client):
    """
    TEST REVIEW PARALLEL
    When two groups approved with same user
    """
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test', user1, [user1, user2])
    group2 = await prepare_group(app, 'test2', user1, [user1])

    document = await prepare_document_data(app, user1)

    # Add reviewers
    await prepare_review_requests(
        client,
        document,
        user1,
        reviewers=[group1, user3, group2],
        is_required=True,
        is_ordered=False,
    )
    async with app['db'].acquire() as conn:
        review_requests = await select_review_requests(
            conn=conn,
            company_id=user1.company_id,
            document_ids=[document.id],
        )

    review_request_group1 = next(r for r in review_requests if r.to_group_id == group1.id)
    review_request_user3 = next(r for r in review_requests if r.to_role_id == user3.role_id)
    review_request_group2 = next(r for r in review_requests if r.to_group_id == group2.id)

    async with app['db'].acquire() as conn:
        status = await select_review_status_by_document_id(
            conn=conn,
            company_edrpou=user1.company_edrpou,
            document_id=document.id,
            version_id=None,
        )
    assert status.status == ReviewStatus.pending
    assert sorted(status.next_review_requests) == sorted(
        [review_request_group1.id, review_request_user3.id, review_request_group2.id]
    )

    await prepare_review(client, user=user1, document=document, review_type=ReviewType.approve)
    async with app['db'].acquire() as conn:
        status = await select_review_status_by_document_id(
            conn=conn,
            company_edrpou=user1.company_edrpou,
            document_id=document.id,
            version_id=None,
        )
    assert status.status == ReviewStatus.pending
    assert status.next_review_requests == [review_request_user3.id]

    await prepare_review(client, user=user3, document=document, review_type=ReviewType.approve)
    async with app['db'].acquire() as conn:
        status = await select_review_status_by_document_id(
            conn=conn,
            company_edrpou=user1.company_edrpou,
            document_id=document.id,
            version_id=None,
        )
    assert status.next_review_requests == []
    assert status.status == ReviewStatus.approved


async def test_add_reviews_when_user_in_two_groups_ordered_check_review_history(aiohttp_client):
    """
    Test that history contains all reviews
    """
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')
    user3 = await prepare_user_data(app, email='<EMAIL>')
    group1 = await prepare_group(app, 'test', user1, [user1, user2])

    document = await prepare_document_data(app, user1)

    # Add reviewers
    await prepare_review_requests(
        client,
        document,
        user1,
        reviewers=[group1, user2, user1, user3],
        is_required=True,
        is_ordered=True,
    )

    async def check_history(expected):
        async with app['db'].acquire() as conn:
            r = await select_reviews_for_history(
                conn=conn,
                company_id=user1.company_id,
                document_id=document.id,
            )
            assert len(r) == expected, r

    await prepare_review(client, user=user1, document=document, review_type=ReviewType.reject)
    await check_history(2)
    await prepare_review(client, user=user1, document=document, review_type=ReviewType.approve)
    await check_history(4)

    await prepare_review(client, user=user2, document=document, review_type=ReviewType.reject)
    await check_history(5)
    await prepare_review(client, user=user2, document=document, review_type=ReviewType.approve)
    await check_history(6)

    await prepare_review(client, user=user3, document=document, review_type=ReviewType.approve)
    await check_history(7)

    # just to make sure that review is finished
    async with services.db.acquire() as conn:
        status = await select_review_status_by_document_id(
            conn=conn,
            company_edrpou=user1.company_edrpou,
            document_id=document.id,
            version_id=None,
        )
        assert status.status == ReviewStatus.approved


async def test_mark_as_approved_when_bulk_with_versions(aiohttp_client):
    """
    Test case for case when we approve several documents
    and one of them has versions.
    Given:
    - document with versions
    - document without versions
    - review requests for both documents
    When:
    - approving both documents in batch
    Then:
    - both documents should be marked as approved
    """
    app, client, user = await prepare_client(aiohttp_client)

    document = await prepare_document_data(app, user)
    document2 = await prepare_document_data(app, user)
    await prepare_document_version(
        role_id=user.role_id,
        company_edrpou=user.company_edrpou,
        company_id=user.company_id,
        document_id=document.id,
        content=b'Hello, World! I am very unique content',
    )

    await prepare_review_requests(
        client,
        document,
        user,
        reviewers=[user],
    )
    await prepare_review_requests(
        client,
        document2,
        user,
        reviewers=[user],
    )

    # Act
    await prepare_batch_reviews(
        client=client,
        documents=[document, document2],
        user=user,
        review_type=ReviewType.approve,
        expected_status=HTTPStatus.CREATED,
    )

    # Assert
    async with app['db'].acquire() as conn:
        reviews = await select_reviews_statuses_by_documents_ids(
            conn,
            documents_ids=[document.id, document2.id],
        )
        assert len(reviews) == 2
        assert all(r.status == ReviewStatus.approved for r in reviews), reviews


async def test_add_review_to_document_with_versions_and_delete_version(aiohttp_client):
    """
    Test case to make sure that we update review status when we delete document version
    Given:
    - document with versions
    - v1 (r1) - pending
    - v2 (r1, r2) - approved
    where
    - v1, v2 - document versions,
    - r1 - review from user1
    - r2 - review from user2
    When:
    - v2 is deleted
    Then:
    - v1 should remain with status pending
    """

    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>')

    document = await prepare_document_data(app, user1)

    # Add reviewers
    await prepare_review_requests(
        client,
        document,
        user1,
        reviewers=[user1, user2],
        is_required=True,
    )

    # Create first version
    async with app['db'].acquire() as conn:

        async def get_statuses() -> tuple[ReviewStatusDB, list[ReviewStatusDB]]:
            _statuses = await select_reviews_statuses(
                conn=conn,
                company_edrpou=user1.company_edrpou,
                documents_ids=[document.id],
            )
            _status_last = next((s for s in _statuses if s.is_last), None)
            _status_prev = [s for s in _statuses if not s.is_last]
            return _status_last, _status_prev

        review_requests = await select_review_requests(
            conn=conn,
            company_id=user1.company_id,
            document_ids=[document.id],
        )
        review_request_user2 = next(r for r in review_requests if r.to_role_id == user2.role_id)

        version1 = await document_versions_utils.add_new_upload_document_version(
            conn=conn,
            validated=ValidatedVersionUpload(
                document=Document.from_row(document),
                content=b'First version content',
                extension='.pdf',
                version_count=1,
                date_created='2021-01-01T00:00:00Z',
            ),
            user=User.from_row(user1),
            source=Source.api_internal,
        )
        await prepare_review_status_db(conn, User.from_row(user1), document_id=document.id)

        # Add review from user1 and make sure we still wait for user2 review
        await prepare_review(client, user=user1, document=document, review_type=ReviewType.approve)
        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version1.id
        assert status.status == ReviewStatus.pending
        assert status.is_last is True
        assert set(status.next_review_requests) == {review_request_user2.id}

        version2 = await document_versions_utils.add_new_upload_document_version(
            conn=conn,
            validated=ValidatedVersionUpload(
                document=Document.from_row(document),
                content=b'Second version content',
                extension='.pdf',
                version_count=1,
                date_created='2022-01-01T00:00:00Z',
            ),
            user=User.from_row(user1),
            source=Source.api_internal,
        )

        # Add review for version2 from user2
        # This should update review status to approved
        await prepare_review(client, user=user2, document=document, review_type=ReviewType.approve)
        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version2.id
        assert status.status == ReviewStatus.approved
        assert status.is_last is True
        assert set(status.next_review_requests) == set()

        # Delete the second version (latest)
        await delete_latest_version(
            conn=conn,
            ctx=ValidatedVersionDelete(
                document=document,
                version=version2,
                user=user1,
                request_source=Source.api_internal,
            ),
        )

        # Verify review status is updated after version deletion
        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version1.id
        assert status.status == ReviewStatus.pending
        assert status.is_last is True
        assert set(status.next_review_requests) == {review_request_user2.id}

        # Make sure that we can approve the first version
        await prepare_review(client, user=user2, document=document, review_type=ReviewType.approve)
        status, prev_statuses = await get_statuses()
        assert status.document_version_id == version1.id
        assert status.status == ReviewStatus.approved
        assert status.is_last is True
        assert set(status.next_review_requests) == set()
