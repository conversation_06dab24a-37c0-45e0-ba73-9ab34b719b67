from __future__ import annotations

import asyncio
import logging
import typing as t
from collections import defaultdict
from collections.abc import Async<PERSON>tera<PERSON>, Iterator
from contextlib import asynccontextmanager

import sqlalchemy as sa
from aiohttp import web

from api.db import get_document_listing_date_mapping
from app.auth.types import User
from app.documents.db import (
    insert_listings,
    select_document_uploader_info,
)
from app.documents.enums import AccessSource
from app.documents.types import Document, ListingDataAggregator
from app.groups.db import select_groups
from app.groups.utils import get_group_members_by_group_ids
from app.i18n import _
from app.i18n.types import LazyI18nString
from app.lib.database import DBConnection, DBRow
from app.lib.enums import Source
from app.lib.helpers import build_full_user_name, group_list
from app.lib.types import DataDict
from app.reviews import db
from app.reviews.db import (
    insert_review_request,
    mark_as_deleted_review_requests,
    select_review_request_for_history,
    select_review_status_by_document_id,
    select_reviews_for_history,
    update_review_setting,
)
from app.reviews.enums import ReviewRequestStatus, ReviewSource, ReviewStatus, ReviewType
from app.reviews.notifications import ReviewApprovedNotification, ReviewRequestNotification
from app.reviews.tables import review_status_table
from app.reviews.types import (
    ReplaceReviewRequestsCtx,
    ReviewerId,
    ReviewHistory,
    ReviewHistoryItem,
    ReviewsInfoCtx,
    ReviewState,
    ReviewStatusDB,
)
from app.services import services
from app.uploads.types import ReviewRequestData
from worker import topics

if t.TYPE_CHECKING:
    from app.reviews.validators import ReviewerItemSchema

logger = logging.getLogger(__name__)

get_review_statuses = db.select_review_statuses

REVIEW_TYPE_TO_ACTION: dict[ReviewType | None, LazyI18nString] = {
    ReviewType.approve: _('Погоджує'),
    ReviewType.reject: _('Відхиляє'),
    None: _('Відміняє погодження'),
}


async def add_group_review_request(
    conn: DBConnection,
    user: User,
    data: ReviewRequestData,
    group_role_ids: list[str],
    is_parallel: bool,
) -> None:
    """Insert new request in database and update related objects in database"""

    document_id = data['document_id']

    # create row in review_request table
    await insert_review_request(conn, data)

    # update review settings for document
    settings_data = {'document_id': document_id, 'is_parallel': is_parallel}
    await update_review_setting(conn, user, settings_data)

    listing_aggregator = ListingDataAggregator()
    # open access to document for all group members
    for role_id in group_role_ids:
        listing_aggregator.add(
            document_id=document_id,
            access_edrpou=user.company_edrpou,
            role_id=role_id,
            source=AccessSource.reviewer,
        )
    await insert_listings(conn=conn, data=listing_aggregator.as_db())


async def add_review_request(
    conn: DBConnection,
    user: User,
    data: ReviewRequestData,
    is_parallel: bool,
) -> DBRow:
    """Insert new request in database and update related objects in database"""

    document_id = data['document_id']

    # create row in review_request table
    review_request = await insert_review_request(conn, data)

    # update review settings for document
    settings_data = {'document_id': document_id, 'is_parallel': is_parallel}
    # TODO: Add check if settings is changed, and if not don't do request?
    await update_review_setting(conn, user, settings_data)

    # open access to document
    await insert_listings(
        conn=conn,
        data={
            'document_id': document_id,
            'access_edrpou': user.company_edrpou,
            'role_id': review_request.to_role_id,
            'sources': AccessSource.reviewer,
        },
    )
    return review_request


async def send_document_review_request_notifications(
    conn: DBConnection,
    *,
    initiator_role_id: str,
    recipient_ids: list[ReviewerId],
    documents_ids: list[str],
    request_source: Source,
    state_new: ReviewState | None,
) -> None:
    """
    Notify user about adding him to document reviewing.
    Send notification to each role and group member.
    """

    if not recipient_ids:
        return

    # Don't send notification about waiting review request to next reviewer
    # if one of reviews is rejected
    if state_new and state_new.is_ordered and state_new.status == ReviewStatus.rejected:
        return

    await asyncio.gather(
        _send_review_request_to_roles(
            recipient_ids=recipient_ids,
            documents_ids=documents_ids,
            request_source=request_source,
            initiator_role_id=initiator_role_id,
        ),
        _send_review_request_to_groups(
            conn=conn,
            recipient_ids=recipient_ids,
            documents_ids=documents_ids,
            request_source=request_source,
            initiator_role_id=initiator_role_id,
        ),
    )


async def _send_review_request_to_roles(
    recipient_ids: list[ReviewerId],
    documents_ids: list[str],
    request_source: Source,
    initiator_role_id: str,
) -> None:
    """
    Send notification with request to review if new review request was added
    """

    recipient_roles_ids = [r.role_id for r in recipient_ids if r.role_id]
    if recipient_roles_ids:
        await _perform_send_document_review_request_notifications(
            initiator_role_id=initiator_role_id,
            recipient_roles_ids=recipient_roles_ids,
            documents_ids=documents_ids,
            request_source=request_source,
        )


async def _send_review_request_to_groups(
    conn: DBConnection,
    *,
    recipient_ids: list[ReviewerId],
    documents_ids: list[str],
    request_source: Source,
    initiator_role_id: str,
) -> None:
    """
    Send notification to each group
    because we need to pass group name to notification
    """

    if group_ids := [r.group_id for r in recipient_ids if r.group_id]:
        sent_to_roles_ids = [r.role_id for r in recipient_ids if r.role_id]

        groups = await select_groups(conn, ids=group_ids)
        groups_mapping = {group.id: group for group in groups}

        group_role_map = await get_group_members_by_group_ids(conn, group_ids=group_ids)

        for group_id in group_ids:
            # send only to group if we have not sent as an individual role
            # or we already send as to member of another group to prevent duplicate notifications
            send_to_roles = [
                role_id for role_id in group_role_map[group_id] if role_id not in sent_to_roles_ids
            ]

            if send_to_roles:
                # TODO: maybe it's better to concat all group name in
                #  one notification member is in multiple groups
                await _perform_send_document_review_request_notifications(
                    initiator_role_id=initiator_role_id,
                    recipient_roles_ids=group_role_map[group_id],
                    group_name=groups_mapping[group_id].name,
                    documents_ids=documents_ids,
                    request_source=request_source,
                )
                sent_to_roles_ids.extend(send_to_roles)


async def _perform_send_document_review_request_notifications(
    initiator_role_id: str,
    recipient_roles_ids: list[str],
    documents_ids: list[str],
    request_source: Source,
    group_name: str | None = None,
) -> None:
    # Do not send review request to current user for requests from WEB interface
    if request_source == Source.api_internal:
        recipient_roles_ids = [r for r in recipient_roles_ids if r != initiator_role_id]

    if not recipient_roles_ids or not documents_ids:
        return

    notification = ReviewRequestNotification(
        initiator_role_id=initiator_role_id,
        recipient_roles_ids=recipient_roles_ids,
        documents_ids=documents_ids,
        group_name=group_name,
    )
    await notification.send()


async def delete_review_status(
    conn: DBConnection,
    *,
    document_id: str,
    edrpou: str,
) -> None:
    await conn.execute(
        review_status_table.delete().where(
            sa.and_(
                review_status_table.c.document_id == document_id,
                review_status_table.c.edrpou == edrpou,
            )
        )
    )


def get_is_review_required(reviews: list[DBRow]) -> bool:
    if len(reviews):
        review = reviews[0]
        return bool(review.is_required)
    return False


async def send_first_notification_to_signers(
    documents_ids: list[str],
    current_company_id: str,
    current_role_id: str,
) -> None:
    """
    Send first notification to signer after document update
    or approved review
    """
    if not documents_ids:
        return

    from app.documents.utils import schedule_send_first_notification_to_signers_job

    await schedule_send_first_notification_to_signers_job(
        documents_ids=documents_ids,
        current_company_id=current_company_id,
        current_role_id=current_role_id,
    )


@asynccontextmanager
async def start_reviews_update_transaction(
    conn: DBConnection,
    *,
    documents_ids: list[str],
    user: User,
    request_source: Source,
) -> AsyncIterator[dict[str, ReviewState]]:
    """
    Start an update of the reviews for the given documents and send async jobs,
    like email or telegram notifications.

    This function starts a database transaction, yields control to the calling function
    to make changes in the database, and then updates the review statuses in the
    database and sends the review notifications by comparing review state before
    update and after.

    It's the recommended way to update the review, in other cases you have to select
    the previous state, update the review status and send a notification by yourself.
    There are a few places in the code where we are doing it manually, mostly because
    we update reviews inside a big transaction where asynchronous jobs and writes to
    the database are far away. Such places:
    - On uploading
        * app.uploads.db.add_reviewers
        * app.uploads.utils.upload_files
    - On big document update:
        * app.documents.utils.send_notification_after_document_update
    """

    # Select previous state
    previous_states = await get_reviews_states(
        conn=conn,
        documents_ids=documents_ids,
        company_id=user.company_id,
        company_edrpou=user.company_edrpou,
    )

    # Start database transaction
    async with conn.begin():
        # Make changes in database
        yield previous_states

        # Recalculate and store status of review process
        new_states = await update_review_statuses_in_db(
            conn=conn,
            documents_ids=documents_ids,
            company_edrpou=user.company_edrpou,
            company_id=user.company_id,
        )

    # Send async jobs after transaction
    await send_review_notifications(
        conn=conn,
        documents_ids=documents_ids,
        user=user,
        states=previous_states,
        states_new=new_states,
        request_source=request_source,
    )

    if user.has_few_reviews is True:
        # Send async job to recalculate reviews count for role
        await services.kafka.send_record(
            topic=topics.RECALCULATE_REVIEWS_COUNT_FOR_USEFUL_META,
            value={'role_id': user.role_id},
        )


async def update_review_statuses_in_db(
    conn: DBConnection,
    documents_ids: list[str],
    company_edrpou: str,
    company_id: str,
) -> dict[str, ReviewState]:
    """
    Calculate new review status and update status in database.
    NOTE: consider using "start_reviews_update_transaction", which is a high-level context manager
    that will send notifications on close, otherwise you must do it yourself.
    """

    states_map = await get_reviews_states(
        conn=conn,
        documents_ids=documents_ids,
        company_id=company_id,
        company_edrpou=company_edrpou,
        is_status_unknown=True,
    )

    review_statuses: list[DataDict] = []
    statuses_to_disable: list[str] = []
    for document_id in documents_ids:
        state: ReviewState = states_map[document_id]

        # delete empty reviews from database at all
        if state.is_empty:
            await delete_review_status(conn, document_id=document_id, edrpou=company_edrpou)
            continue

        status = state.calc_review_status()
        next_review_requests = state.calc_next_review_requests()

        review_statuses.append(
            {
                'document_id': document_id,
                'edrpou': company_edrpou,
                'status': status,
                'document_version_id': state.document_version_id,
                'date_updated': sa.text('now()'),
                'next_review_requests': next_review_requests,
                'is_last': True,
            }
        )

        # Update the general status of the review process to avoid selecting status
        # after review update and return state with already new status
        state.status = status

        # Mark statuses for previous versions as not last. Here we can disable also active one,
        # but it's OK, because "upsert_reviews_statuses" will mark it as last again.
        if statuses_versions := state.statuses_versions:
            statuses_to_disable.extend(s.id for s in statuses_versions if s.is_last)

    if statuses_to_disable:
        await db.update_review_statuses(
            conn=conn,
            statuses_ids=statuses_to_disable,
            data={'is_last': False},
        )

    if review_statuses:
        await db.upsert_reviews_statuses(conn, data=review_statuses)

    return states_map


async def send_review_reject_notification(
    user: User,
    documents_ids: list[str],
) -> None:
    """Sending notification about rejecting document review"""

    await services.kafka.send_record(
        topic=topics.SEND_REVIEW_REJECT_NOTIFICATION,
        value={
            'document_ids': documents_ids,
            'rejecter': {
                'first_name': user.first_name,
                'second_name': user.second_name,
                'last_name': user.last_name,
                'email': user.email,
                'company_id': user.company_id,
            },
        },
    )


async def send_review_approve_notification(
    conn: DBConnection,
    document_id: str,
    state_new: ReviewState,
    user: User,
) -> None:
    """
    Sending notification about approved document review to review request initiators,
    and document uploader except current user.
    """

    roles_ids: set[str] = {request.from_role_id for request in state_new.requests}

    uploader = await select_document_uploader_info(conn, document_id=document_id)
    if uploader:
        roles_ids.add(uploader.role_id)

    # Remove the current user from recipients, because he did this action and already know about it
    roles_ids.discard(user.role_id)

    notification = ReviewApprovedNotification(
        roles_ids=list(roles_ids),
        document_id=document_id,
    )
    await notification.send()


async def is_required_reviews_approved(
    conn: DBConnection,
    *,
    edrpou: str,
    document_id: str,
    version_id: str | None,
) -> bool:
    """
    Check if required reviews for a given document were approved.

    In case when a document doesn't have required reviews at all, we consider it as approved.
    """

    review_status = await select_review_status_by_document_id(
        conn,
        company_edrpou=edrpou,
        document_id=document_id,
        version_id=version_id,
    )

    # the document doesn't have required review at all, so consider it as approved
    if not review_status or not review_status.is_required:
        return True

    return review_status.status == ReviewStatus.approved


async def all_required_reviews_was_finished(
    conn: DBConnection,
    *,
    edrpou: str,
    document_id: str,
    version_id: str | None,
) -> bool:
    return await is_required_reviews_approved(
        conn=conn,
        edrpou=edrpou,
        document_id=document_id,
        version_id=version_id,
    )


def _review_requests_to_history_item(
    requests: list[DBRow],
) -> Iterator[ReviewHistoryItem]:
    for request in requests:
        user_name = build_full_user_name(
            first=request.user_from_first_name,
            second=request.user_from_second_name,
            last=request.user_from_last_name,
        )
        coworker_name = build_full_user_name(
            first=request.user_to_first_name,
            second=request.user_to_second_name,
            last=request.user_to_last_name,
        )
        coworker_email = request.user_to_email
        coworker = f'{coworker_name}, {coworker_email}' if coworker_name else coworker_email
        yield ReviewHistoryItem(
            user_name=user_name or '',
            user_email=request.user_from_email,
            action=_('Запросив співробітника {0}').bind(coworker),
            date=request.date_created,
        )


def _reviews_to_history_item(reviews: list[DBRow]) -> Iterator[ReviewHistoryItem]:
    for review in reviews:
        user_name = build_full_user_name(
            first=review.user_first_name,
            second=review.user_second_name,
            last=review.user_last_name,
        )
        action = REVIEW_TYPE_TO_ACTION[review.type]
        yield ReviewHistoryItem(
            user_name=user_name or '',
            user_email=review.user_email,
            action=action,
            date=review.date_created,
        )


async def select_review_history_items(
    conn: DBConnection,
    company_id: str,
    document_id: str,
) -> list[ReviewHistoryItem]:
    requests = await select_review_request_for_history(conn, company_id, document_id)
    reviews = await select_reviews_for_history(conn, company_id, document_id)

    items: list[ReviewHistoryItem] = []
    items.extend(_review_requests_to_history_item(requests))
    items.extend(_reviews_to_history_item(reviews))

    return sorted(items, key=lambda _item: _item.date)


async def select_review_history(
    conn: DBConnection,
    user: User,
    document: Document,
) -> ReviewHistory:
    company_id: str = user.company_id

    items = await select_review_history_items(conn, company_id, document.id)

    auth_user = user
    date_map = await get_document_listing_date_mapping(conn, [document.id], auth_user)
    document_date = date_map.get(document.id)

    return ReviewHistory(
        document_id=document.id,
        document_title=document.title,
        document_number=document.number,
        document_date=document_date,
        items=items,
    )


async def get_reviews_states(
    conn: DBConnection,
    *,
    documents_ids: list[str],
    company_id: str,
    company_edrpou: str,
    is_status_unknown: bool = False,
) -> dict[str, ReviewState]:
    """
    Select full current state of review for given documents

    — is_status_unknown — you can pass True in cases when you know that review was
    updated, but a status object is not updated yet. One case for it is selecting
    state of review before update in update_review_statuses_in_db function.
    """

    from app.document_versions.utils import (
        get_latest_document_versions_available_for_company,
    )

    document_versions = await get_latest_document_versions_available_for_company(
        conn=conn,
        document_ids=documents_ids,
        company_edrpou=company_edrpou,
    )
    document_versions_mapping = {version.document_id: version.id for version in document_versions}
    reviews = await db.select_reviews(
        conn=conn,
        company_edrpou=company_edrpou,
        document_with_version_ids=[
            (document_id, document_versions_mapping.get(document_id))
            for document_id in documents_ids
        ],
    )
    requests = await db.select_review_requests(
        conn=conn,
        company_id=company_id,
        document_ids=documents_ids,
    )
    statuses = await db.select_reviews_statuses(
        conn=conn,
        company_edrpou=company_edrpou,
        documents_ids=documents_ids,
    )
    settings = await db.select_reviews_settings(
        conn=conn,
        company_id=company_id,
        documents_ids=documents_ids,
    )

    # Group selected object by document ID
    reviews_map: defaultdict[str, list[DBRow]]
    reviews_map = group_list(reviews, lambda r: r.document_id)

    requests_map: defaultdict[str, list[DBRow]]
    requests_map = group_list(requests, lambda r: r.document_id)

    statuses_map: defaultdict[str, list[ReviewStatusDB]]
    statuses_map = group_list(statuses, lambda r: r.document_id)

    settings_map = {setting.document_id: setting for setting in settings}

    document_versions_map = {version.document_id: version for version in document_versions}

    statuses_version_map: defaultdict[str, list[ReviewStatusDB]]
    statuses_version_map = group_list(statuses, lambda s: s.document_version_id or '')

    res = {}
    for document_id in documents_ids:
        document_version = document_versions_map.get(document_id)

        status: ReviewStatusDB | None
        if is_status_unknown:
            status = None

        else:
            if document_version:
                # get latest status for given document version
                # because each new version of the document have each own
                # review status
                statuses = statuses_version_map[document_version.id]
                status = statuses[0] if statuses else None
            else:
                # for non-versioned documents just get status by document_id.
                statuses = statuses_map[document_id]
                status = statuses[0] if statuses else None

        res[document_id] = ReviewState(
            reviews=reviews_map[document_id],
            requests=requests_map[document_id],
            status=status,
            settings=settings_map.get(document_id),
            document_version=document_version,
            statuses_versions=statuses_map[document_id],
        )
    return res


async def get_review_state(
    conn: DBConnection,
    *,
    document_id: str,
    company_id: str,
    company_edrpou: str,
) -> ReviewState:
    """Select review status for single document"""
    statuses = await get_reviews_states(
        conn=conn,
        documents_ids=[document_id],
        company_id=company_id,
        company_edrpou=company_edrpou,
    )
    return statuses[document_id]


async def replace_review_requests_in_db(
    conn: DBConnection, user: User, ctx: ReplaceReviewRequestsCtx
) -> None:
    """
    Replace review requests in database in transaction.
    WARNING: call that function in transaction and with start_reviews_update
    """
    await update_review_setting(
        conn=conn,
        user=user,
        data={
            'document_id': ctx.document.id,
            'is_required': ctx.is_required,
            'is_parallel': not ctx.is_ordered,
        },
    )
    # Remove all current review request for given document
    group_ids, role_ids = [], []
    for request in ctx.state.requests:
        if request.to_group_id:
            group_ids.append(request.to_group_id)
        else:
            role_ids.append(request.to_role_id)

    await mark_as_deleted_review_requests(
        conn=conn,
        document_id=ctx.document.id,
        roles_ids=role_ids,
        group_ids=group_ids,
    )

    from app.document_versions.utils import (
        get_latest_document_version_available_for_company,
    )

    document_version = await get_latest_document_version_available_for_company(
        conn=conn,
        document_id=ctx.document.id,
        company_edrpou=user.company_edrpou,
    )

    for idx, reviewer in enumerate(ctx.reviewers, start=1):
        data = ReviewRequestData(
            document_id=ctx.document.id,
            document_version_id=document_version.id if document_version else None,
            from_role_id=user.role_id,
            to_role_id=reviewer.role_id,
            to_group_id=reviewer.group_id,
            order=idx if ctx.is_ordered else None,
            status=ReviewRequestStatus.active,
        )

        if reviewer.is_role:
            await add_review_request(conn, user, data=data, is_parallel=not ctx.is_ordered)
        elif reviewer.is_group:
            await add_group_review_request(
                conn,
                user=user,
                data=data,
                group_role_ids=reviewer.group_role_ids,
                is_parallel=not ctx.is_ordered,
            )
        else:
            raise NotImplementedError


def _should_sent_first_signer_notification(
    state_old: ReviewState,
    state_new: ReviewState,
) -> bool:
    # Changed from required to not required, signer should receive notification
    if state_old.is_required and not state_new.is_required:
        return True

    # if status changed to approved in case of required review, send notification
    # to signer. In case when review is not required, notification sends to signer when
    # signer is assigned to document.
    status_changed = state_new.status != state_old.status and state_new.status
    if status_changed == ReviewStatus.approved and state_new.is_required:
        return True

    # In other case do not send notification to signer
    return False


def _should_send_review_reject_notification(
    *,
    state_old: ReviewState,
    state_new: ReviewState,
) -> bool:
    """
    Send review reject notification only if status of review was changed from
    non-rejected to rejected
    """
    return state_new.status == ReviewStatus.rejected and state_old.status != ReviewStatus.rejected


def _should_send_review_approved_notification(
    *,
    state_old: ReviewState,
    state_new: ReviewState,
) -> bool:
    """
    Send review approve notification only if status of review was changed from
    non-approved to approved
    """
    return state_new.status == ReviewStatus.approved and state_old.status != ReviewStatus.approved


def _get_review_request_notification_recipients(
    *,
    state_old: ReviewState,
    state_new: ReviewState,
) -> list[ReviewerId]:
    # get recipients of current review state and previous review state
    # and remove recipients that already received review request notification
    prev_recipients = set(state_old.review_request_notification_recipients)
    next_recipients = set(state_new.review_request_notification_recipients)

    return list(next_recipients - prev_recipients)


async def _send_review_notification(
    conn: DBConnection,
    state_old: ReviewState,
    state_new: ReviewState,
    document_id: str,
    user: User,
    request_source: Source,
) -> None:
    """
    Compare previous and old state and send notification after review update:
     - for the first signer
     - review request
     - review rejected
     - review approved (not implemented yet)
    """

    # TODO: remove database connection from this function, because it's used in loop

    # Send notification for first signer after review update
    if _should_sent_first_signer_notification(
        state_old=state_old,
        state_new=state_new,
    ):
        from app.flow.utils import send_multilateral_document_notification_job

        await send_first_notification_to_signers(
            documents_ids=[document_id],
            current_company_id=user.company_id,
            current_role_id=user.role_id,
        )
        await send_multilateral_document_notification_job(
            document_id=document_id,
            company_edrpou=user.company_edrpou,
        )

    recipients = _get_review_request_notification_recipients(
        state_old=state_old,
        state_new=state_new,
    )
    if not recipients and (
        # new version of document was uploaded
        state_old.document_version
        and not _should_send_review_reject_notification(
            state_old=state_old,
            state_new=state_new,
        )
        and not _should_send_review_approved_notification(
            state_old=state_old,
            state_new=state_new,
        )
    ):
        recipients = state_new.review_request_notification_recipients

    if recipients:
        initiator_role_id = user.role_id
        if state_new.is_ordered:
            # in this case we have only one recipient
            initiator_role_id = (
                state_new.get_review_request_initiator_role_id(reviewer=recipients[0])
                or initiator_role_id
            )

        await send_document_review_request_notifications(
            conn=conn,
            initiator_role_id=initiator_role_id,
            documents_ids=[document_id],
            request_source=request_source,
            recipient_ids=recipients,
            state_new=state_new,
        )

    # Send notification that review was rejected
    if _should_send_review_reject_notification(
        state_old=state_old,
        state_new=state_new,
    ):
        await send_review_reject_notification(
            user=user,
            documents_ids=[document_id],
        )

    # Send notification that review was approved
    if _should_send_review_approved_notification(
        state_old=state_old,
        state_new=state_new,
    ):
        await send_review_approve_notification(
            conn=conn,
            user=user,
            state_new=state_new,
            document_id=document_id,
        )


async def send_review_notifications(
    conn: DBConnection,
    *,
    documents_ids: list[str],
    user: User,
    states: dict[str, ReviewState],
    request_source: Source,
    states_new: dict[str, ReviewState] | None = None,
) -> None:
    """
    Send review notifications to the given user based on the old and new review
    states of the documents.
    """

    # Review state after all updates in database
    if not states_new:
        states_new = await get_reviews_states(
            conn=conn,
            documents_ids=documents_ids,
            company_id=user.company_id,
            company_edrpou=user.company_edrpou,
        )

    for document_id in documents_ids:
        state_old: ReviewState = states[document_id]
        state_new: ReviewState = states_new[document_id]

        await _send_review_notification(
            conn=conn,
            state_old=state_old,
            state_new=state_new,
            document_id=document_id,
            user=user,
            request_source=request_source,
        )


def is_review_finished(*, requests: list[DBRow], reviews: list[DBRow]) -> bool:
    active_requests = [req for req in requests if req.status == ReviewRequestStatus.active]
    finished_reviews = [rev for rev in reviews if rev.type is not None]

    return len(active_requests) == len(finished_reviews)


def get_next_review_request_order(requests: list[DBRow]) -> int:
    requests = sorted(requests, key=lambda r: r.order)
    ordered_requests = [req for req in requests if req.order is not None]

    return ordered_requests[-1].order + 1 if ordered_requests else 1


def separate_reviewers(
    reviewers: list[ReviewerItemSchema],
) -> dict[ReviewsInfoCtx.EntityType, list[str]]:
    reviewers_result: dict[ReviewsInfoCtx.EntityType, list[str]] = {
        ReviewsInfoCtx.EntityType.role: [],
        ReviewsInfoCtx.EntityType.group: [],
    }
    for reviewer in reviewers:
        reviewer_type = reviewer.type
        if reviewer_type and reviewer_type in reviewers_result:
            reviewers_result[reviewer_type].append(reviewer.id)
        else:
            raise NotImplementedError
    return reviewers_result


def convert_reviewers(
    reviewers: list[ReviewerItemSchema], group_role_map: defaultdict[str, list[str]]
) -> list[ReviewsInfoCtx]:
    reviewer_infos: list[ReviewsInfoCtx] = []

    for reviewer in reviewers:
        if reviewer.type == ReviewsInfoCtx.EntityType.role:
            reviewer_infos.append(
                ReviewsInfoCtx(
                    entity=ReviewsInfoCtx.EntityType.role,
                    role_id=reviewer.id,
                    group_id=None,
                    group_role_ids=[],
                )
            )
        elif reviewer.type == ReviewsInfoCtx.EntityType.group:
            reviewer_infos.append(
                ReviewsInfoCtx(
                    entity=ReviewsInfoCtx.EntityType.group,
                    role_id=None,
                    group_id=reviewer.id,
                    group_role_ids=group_role_map[reviewer.id],
                )
            )
        else:
            raise NotImplementedError

    return reviewer_infos


async def set_version_for_review_process(
    conn: DBConnection,
    *,
    document_id: str,
    version_id: str | None,
) -> None:
    """
    Update a version of reviews, review requests and review statuses by document ID.

    WARNING: that function works correctly only when a document has only one or zero versions,
    otherwise it will make a mess in the database.
    """
    await db.update_reviews_by_document(
        conn=conn,
        document_id=document_id,
        data={'document_version_id': version_id},
    )
    await db.update_review_requests_by_document(
        conn=conn,
        document_id=document_id,
        data={'document_version_id': version_id},
    )
    await db.update_review_statuses_by_document(
        conn=conn,
        document_id=document_id,
        data={'document_version_id': version_id},
    )


async def copy_reviews_between_versions(
    conn: DBConnection,
    *,
    document_id: str,
    prev_version_id: str,
    new_version_id: str,
    company_edrpou: str | None = None,  # copy only for given company
) -> None:
    """
    Copy reviews from one document version to another
    """

    reviews = await db.select_reviews(
        conn=conn,
        document_with_version_ids=[(document_id, prev_version_id)],
        company_edrpou=company_edrpou,
    )
    if not reviews:
        return

    # Review might be from different companies
    companies = {(review.company_id, review.edrpou) for review in reviews}

    async with conn.begin():
        # We expected from 1 to 2 companies most of the time, so loop is not a problem here
        for company_id, edrpou in companies:
            next_reviews = [
                {
                    'document_id': document_id,
                    'document_version_id': new_version_id,
                    'role_id': review.role_id,
                    'group_id': review.group_id,
                    'type': review.type,
                    'is_last': True,
                    'date_created': sa.text('now()'),
                    'user_email': review.user_email,
                }
                for review in [r for r in reviews if r.company_id == company_id]
            ]
            await db.insert_document_reviews(conn, next_reviews)

            # Unlike in other places where we update review, here we don't use the function
            # "start_reviews_update_transaction", because we don't need to send notifications
            # and reviews might be from different companies, which is not supported in that
            # function
            await update_review_statuses_in_db(
                conn=conn,
                documents_ids=[document_id],
                company_edrpou=edrpou,
                company_id=company_id,
            )


def get_review_request_source_from_request(request: web.Request) -> ReviewSource:
    """
    Parse request path and determine review request source
    """
    if request.path.startswith('/mobile-api'):
        return ReviewSource.mobile
    return ReviewSource.web
