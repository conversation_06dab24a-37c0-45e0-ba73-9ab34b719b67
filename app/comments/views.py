from aiohttp import web

from api.public.decorators import api_handler
from api.utils import api_response
from app.auth.types import AuthUser, User
from app.comments.utils import handle_add_comment, list_comments_db, list_comments_es
from app.comments.validators import validate_list_comments
from app.es.utils import send_to_indexator
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib.datetime_utils import soft_isoformat
from app.services import services


async def add(request: web.Request, user: AuthUser | User) -> web.Response:
    comment, __ = await handle_add_comment(request, user)
    await send_to_indexator(
        redis=services.redis,
        document_ids=[comment.document_id],
        to_slow_queue=False,
    )
    return web.json_response(status=201)


@api_handler
async def list_comments(request: web.Request, user: User) -> web.Response:
    """
    List comments for all documents to which user has access.
    """
    data = validate_list_comments(request)

    if get_flag(FeatureFlags.USE_ES_FOR_COMMENT_LIST):
        comments, next_page_num = await list_comments_es(
            user=user,
            date_from=data.date_from,
            date_to=data.date_to,
            cursor=data.cursor or 0,
            limit=data.limit or 100,
        )
    else:
        comments, next_page_num = await list_comments_db(
            user=user,
            date_from=data.date_from,
            date_to=data.date_to,
            cursor=data.cursor or 0,
            limit=data.limit or 100,
        )

    resp = {
        'comments': [
            {
                'id': comment.id,
                'text': comment.text,
                'document_id': comment.document_id,
                # TODO: think about adding version_id in response🤔
                'date_created': soft_isoformat(comment.date_created),
                'email': comment.author_email,
                'edrpou': comment.author_edrpou,
                'is_internal': comment.access_company_id == user.company_id,
                'type': comment.type,
            }
            for comment in comments
        ],
        'next_cursor': next_page_num,
    }
    return api_response(request, resp)
