import typing as t

import pydantic
from aiohttp import web

from api.errors import DoesNotExist, Object
from app.auth.types import AuthUser, User
from app.comments.constants import MAX_COMMENT_TEXT_LENGTH
from app.comments.db import select_last_comment
from app.comments.types import (
    AddCommentOptions,
    to_comment_author,
)
from app.document_versions.utils import convert_document_version_marker
from app.i18n import _
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.database import DBConnection
from app.lib.types import DataDict

CommentText = t.Annotated[
    str, pydantic.StringConstraints(strip_whitespace=True, max_length=MAX_COMMENT_TEXT_LENGTH)
]

VersionText = t.Annotated[str, pydantic.StringConstraints(strip_whitespace=True, max_length=36)]


class AddCommentSchema(pydantic.BaseModel):
    # From URL path
    document_id: pv.UUID
    # From current user
    user_edrpou: pv.EDRPOU
    user_is_legal: bool
    user_role_id: pv.UUID
    # From request body
    text: CommentText
    is_internal: bool | None = None
    version: VersionText | None = None


class ListCommentsSchema(pydantic.BaseModel):
    date_from: pv.LeftDatetime
    date_to: pv.RightDatetime
    cursor: int | None = None
    limit: int | None = pydantic.Field(None, ge=1, le=100)


async def validate_add_comment(
    conn: DBConnection, data: DataDict, user: AuthUser | User
) -> AddCommentOptions:
    """Validate data and ability to comment document by given user."""
    from app.documents.validators import (
        validate_document_access,
        validate_document_exists,
    )

    data['user_is_legal'] = user.is_legal
    data['user_edrpou'] = user.company_edrpou
    data['user_role_id'] = user.role_id
    data.setdefault('is_internal', False)

    valid_data = validators.validate_pydantic(AddCommentSchema, data)

    # Convert pydantic model to dict for compatibility with existing functions
    valid_data_dict = valid_data.model_dump()

    document = await validate_document_exists(conn, valid_data_dict)
    await validate_document_access(conn, user, document_id=valid_data.document_id)

    company_id = user.company_id
    is_internal = valid_data.is_internal
    text = valid_data.text
    access_company_id = company_id if is_internal else None

    # If document is versioned add comment to the latest version
    document_version = None
    if user.company_edrpou and valid_data.version:
        document_version = await convert_document_version_marker(
            conn=conn,
            version_marker=valid_data.version,
            document_id=document.id,
            company_edrpou=user.company_edrpou,
        )

        if not document_version:
            raise DoesNotExist(Object.document_version, version_id=valid_data.version)

        if document_version and not is_internal and not document_version.is_sent:
            raise validators.InvalidRequest(
                reason=_(
                    'Документ не відправлений. Зовнішні коментарі можна залишати '
                    'тільки до відправлених документів.'
                )
            )

    # Prevent comments spamming
    # Do not allow adding same comment twice in a row
    last_comment = await select_last_comment(
        conn=conn,
        document_id=document.id,
    )
    is_already_commented_with_text = last_comment and (
        last_comment.text == text
        and last_comment.document_version_id == (document_version.id if document_version else None)
        and last_comment.role_id == valid_data.user_role_id
        and last_comment.access_company_id == access_company_id
    )
    if is_already_commented_with_text:
        raise validators.InvalidRequest(
            reason=_('Коментар з ідентичним вмістом вже доданий на документ')
        )

    return AddCommentOptions(
        document=document,
        author=to_comment_author(user),
        document_version_id=document_version.id if document_version else None,
        role_id=valid_data.user_role_id,
        text=text,
        access_company_id=access_company_id,
    )


def validate_list_comments(request: web.Request) -> ListCommentsSchema:
    return validators.validate_pydantic(ListCommentsSchema, dict(request.rel_url.query))
