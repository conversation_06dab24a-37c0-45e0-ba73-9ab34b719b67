from dataclasses import dataclass

import pydantic

import app.lib.validators_pydantic as pv
from api.errors import (
    AccessDenied,
    DoesNotExist,
    Object,
)
from app.auth.db import select_roles
from app.auth.types import User
from app.auth.utils import has_permission
from app.groups.db import (
    exist_groups,
    select_group,
    select_group_members,
    select_groups,
)
from app.groups.types import Group
from app.lib import validators
from app.lib.database import DBConnection
from app.lib.types import AnyDict


@dataclass(frozen=True)
class AddGroup:
    name: str
    initiator: User

    class Schema(pydantic.BaseModel):
        name: str = pydantic.Field(min_length=1, max_length=100)


@dataclass(frozen=True)
class EditGroup:
    group: Group
    name: str
    creator: User

    class Schema(pydantic.BaseModel):
        name: str = pydantic.Field(min_length=1, max_length=100)


@dataclass(frozen=True)
class DeleteGroup:
    group_id: str
    initiator: User

    class Schema(pydantic.BaseModel):
        pass


@dataclass(frozen=True)
class AddGroupMembers:
    group: Group
    role_ids: list[str]
    initiator: User

    class Schema(pydantic.BaseModel):
        role_ids: list[pv.UUID] = pydantic.Field(min_length=1, max_length=10_000)


@dataclass(frozen=True)
class DeleteGroupMembers:
    group: Group
    group_member_ids: list[str]
    initiator: User

    class Schema(pydantic.BaseModel):
        group_members: list[pv.UUID] = pydantic.Field(min_length=1, max_length=10_000)


def check_group_permission(user: User) -> None:
    """
    Only admin or user with can_edit_roles permission can create, edit or delete groups.
    """
    if not has_permission(user, {'can_edit_roles'}):
        raise AccessDenied()


async def validate_add_group(*, conn: DBConnection, raw_data: AnyDict, user: User) -> AddGroup:
    check_group_permission(user)

    data = validators.validate_pydantic(AddGroup.Schema, raw_data)
    return AddGroup(initiator=user, name=data.name)


async def validate_edit_group(
    *,
    conn: DBConnection,
    raw_data: AnyDict,
    user: User,
    group_id: str,
) -> EditGroup:
    check_group_permission(user)

    group = await select_group(conn=conn, id=group_id, company_id=user.company_id)
    if not group:
        raise AccessDenied()

    data = validators.validate_pydantic(EditGroup.Schema, raw_data)

    return EditGroup(group=group, creator=user, name=data.name)


async def validate_delete_group(
    *,
    conn: DBConnection,
    user: User,
    group_id: str,
) -> DeleteGroup:
    check_group_permission(user)

    if not await exist_groups(
        conn=conn,
        ids=[group_id],
        company_id=user.company_id,
    ):
        raise AccessDenied()

    return DeleteGroup(group_id=group_id, initiator=user)


async def validate_insert_group_member(
    *,
    conn: DBConnection,
    raw_data: AnyDict,
    user: User,
    group_id: str,
) -> AddGroupMembers:
    check_group_permission(user)

    group = await select_group(
        conn=conn,
        id=group_id,
        company_id=user.company_id,
    )
    if not group:
        raise AccessDenied()

    data = validators.validate_pydantic(AddGroupMembers.Schema, raw_data)

    roles = await select_roles(
        conn=conn,
        roles_ids=data.role_ids,
    )

    if len(roles) != len(data.role_ids):
        raise DoesNotExist(
            Object.roles,
            role_ids=set(data.role_ids) - {role.id_ for role in roles},
        )

    return AddGroupMembers(
        group=group,
        role_ids=data.role_ids,
        initiator=user,
    )


async def validate_delete_group_member(
    *,
    conn: DBConnection,
    raw_data: AnyDict,
    user: User,
    group_id: str,
) -> DeleteGroupMembers:
    check_group_permission(user)

    group = await select_group(
        conn=conn,
        id=group_id,
        company_id=user.company_id,
    )
    if not group:
        raise AccessDenied()

    data = validators.validate_pydantic(DeleteGroupMembers.Schema, raw_data)

    members = await select_group_members(
        conn=conn,
        ids=data.group_members,
        group_ids=[group_id],
        company_id=user.company_id,
        is_deleted=False,
    )

    if len(members) != len(data.group_members):
        raise DoesNotExist(
            Object.group_members,
            group_member_ids=set(data.group_members) - {member.id for member in members},
        )

    return DeleteGroupMembers(
        group_member_ids=data.group_members,
        initiator=user,
        group=group,
    )


async def validate_groups_exists(
    conn: DBConnection,
    group_ids: list[str] | set[str],
    company_id: str,
) -> list[Group]:
    groups = await select_groups(conn, ids=group_ids, company_id=company_id)
    if not_found_groups := list(set(group_ids) - {group.id for group in groups}):
        raise DoesNotExist(Object.groups, role_ids=not_found_groups)
    return groups
