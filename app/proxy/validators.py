import base64
import binascii
import logging

import pydantic
from aiohttp import web
from yarl import URL

from api.errors import (
    Code,
    Error,
)
from app.lib import validators
from app.lib import validators_pydantic as pv
from app.lib.types import DataDict
from app.services import services

logger = logging.getLogger(__name__)


class ProxyRequestSchema(pydantic.BaseModel):
    address: pv.URL
    token: bytes

    @pydantic.field_validator('token', mode='before')
    @classmethod
    def validate_token(cls, v: str | bytes) -> bytes:
        """Decode base64 token to bytes."""
        if isinstance(v, bytes):
            return v

        if not v:
            raise ValueError('Token cannot be empty')

        try:
            return base64.b64decode(v)
        except binascii.Error:
            logger.warning(
                'Got invalid token from frontend. Unable to decode',
                exc_info=True,
                extra={'value': v},
            )
            raise Error(Code.invalid_token)


class CASchema(pydantic.BaseModel):
    model_config = pydantic.ConfigDict(extra='ignore')

    address: str = ''
    ocsp_access_point_address: str = pydantic.Field('', alias='ocspAccessPointAddress')
    cmp_address: str = pydantic.Field('', alias='cmpAddress')
    tsp_address: str = pydantic.Field('', alias='tspAddress')

    @pydantic.field_validator('address', 'ocsp_access_point_address', 'cmp_address', 'tsp_address')
    @classmethod
    def validate_host_address(cls, v: str) -> str:
        """Convert address to host format."""
        if not v:
            return v
        value = f'http://{v}'  # hack for yarl
        return URL(value).host or ''

    def to_dict(self) -> DataDict:
        return self.model_dump(mode='json', by_alias=True)


class CAListSchema(pydantic.BaseModel):
    ca_list: list[CASchema]


class ProxyGenericRequestSchema(pydantic.BaseModel):
    address: pv.URL


def validate_proxy_request(data: dict[str, str | bytes]) -> ProxyRequestSchema:
    return validators.validate_pydantic(ProxyRequestSchema, data)


def validate_proxy_cloud_signer_request(request: web.Request) -> str:
    data = {'address': request.rel_url.query.get('address')}

    valid_data = validators.validate_pydantic(ProxyGenericRequestSchema, data)
    address = valid_data.address

    config = services.config.cloud_signer
    if not config or not address.startswith(config.host):
        raise web.HTTPForbidden()

    return address


def validate_proxy_kep_request(request: web.Request) -> str:
    data = {'address': request.rel_url.query.get('address')}

    valid_data = validators.validate_pydantic(ProxyGenericRequestSchema, data)
    address = valid_data.address

    config = services.config.kep
    if not config or not address.startswith(config.host):
        raise web.HTTPForbidden()

    return address
