import asyncio
import base64
import json
import logging
from functools import cache
from pathlib import Path
from typing import (
    Any,
)
from urllib.parse import urljoin

import httpx
from aiohttp import ClientSession
from aiohttp.client_exceptions import ClientError
from py_eusign import eusign

from api.errors import (
    Code,
    Error,
)
from app.config.helpers import get_level
from app.config.schemas import AppConfig
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib.enums import AppLevel
from app.lib.eusign_utils import (
    client_dynamic_key_session_create,
    session_load,
    verify_internal_sync,
)
from app.lib.helpers import (
    decode_base64_str,
    generate_base64_str,
)
from app.lib.types import DataDict
from app.proxy import constants
from app.proxy.exceptions import ProxyRequestError
from app.proxy.types import CAResponse
from app.proxy.validators import CAListSchema
from app.services import services

logger = logging.getLogger(__name__)

EXTRA_PROXY_HOSTS = {
    'czo.gov.ua',
    'acsk.er.gov.ua',
    'zc.bank.gov.ua',
    'ca.tax.gov.ua',
    'ca.diia.gov.ua',
    'va1-knedp.ssu.gov.ua',
    'acskidd.gov.ua',
    'ca.alfabank.kiev.ua',
}

CA_JSON_PATH: dict[AppLevel, str] = {
    AppLevel.prod: 'eusign/prod/CAs.json',
    AppLevel.dev: 'eusign/dev/CAs.json',
    AppLevel.local: 'eusign/dev/CAs.json',
    AppLevel.test: 'eusign/dev/CAs.json',
}


def check_response_headers(url: str, headers: httpx.Headers) -> None:
    """Check that OCSP/TSP request respond with proper headers."""
    expected_headers: dict[str, Any] = {}
    if is_ocsp_request(url):
        expected_headers = constants.OCSP_RESPONSE_HEADERS
    elif is_tsp_request(url):
        expected_headers = constants.TSP_RESPONSE_HEADERS

    if not expected_headers:
        return

    for key, value in expected_headers.items():
        full_header = headers.get(key)
        header = full_header.split(';')[0] if full_header else full_header

        matched = header in value if isinstance(value, set) else header == value
        if not matched:
            logger.warning(
                'Invalid header value on response from CA Server',
                extra={
                    'expected': value,
                    'header': key,
                    'value': header,
                    'value_full': full_header,
                    'url': url,
                },
            )


def is_ocsp_request(url: str) -> bool:
    return url.endswith('/ocsp') or url.endswith('/ocsp/')


def is_tsp_request(url: str) -> bool:
    return url.endswith('/tsp') or url.endswith('/tsp/')


def prepare_request_headers(url: str) -> dict[str, str]:
    """Prepare proper request headers to CA Server.

    Right now 2 endpoints require special headers:

    - OCSP
    - TSP

    So if URL ends with ``/ocsp/`` or ``/tsp/`` we'll pass custom headers, if
    not we'll pass empty headers to CA Server.
    """
    if is_ocsp_request(url):
        return constants.OCSP_REQUEST_HEADERS
    if is_tsp_request(url):
        return constants.TSP_REQUEST_HEADERS
    return {}


async def request_ca_server(
    url: str,
    token: bytes,
    app_config: AppConfig,
) -> CAResponse:
    """Helper function to request CA Server.

    In general this is just a wrapper around ``session.post`` call, but it
    also:

    - Handle all errors and reraise ``ProxyRequestError``
    - Check for proper headers to be sent, to be received from CA Server
    """

    # Some external CA require being in Ukraine to make requests.
    # For such use cases, we will be using a proxy located in Ukraine.
    proxy_ua: str | None = app_config.proxy_ua
    proxy_timeout_default: float = app_config.sign_timeout.default
    proxy_timeout_slow: float = app_config.sign_timeout.slow

    if get_flag(FeatureFlags.DISABLE_PROXY_UA):
        proxy_ua = None

    # increase timeout for slow CAs (CASARIS, NBU)
    proxy_timeout = proxy_timeout_default
    if any(_url in url for _url in constants.SLOW_CA_SERVERS):
        proxy_timeout = proxy_timeout_slow

    request_headers = prepare_request_headers(url)
    try:
        # docs/decision-log.md#use-httpx-as-http-client
        async with httpx.AsyncClient(proxies=proxy_ua) as client:
            response = await client.post(
                url=url,
                content=token,
                headers=request_headers,
                timeout=proxy_timeout,
            )
            response.raise_for_status()
            check_response_headers(url, response.headers)
            content = base64.b64encode(response.read())
            return CAResponse(content, response.status_code)
    except (TimeoutError, httpx.HTTPError, ValueError) as err:
        raise ProxyRequestError() from err


@cache
def read_allowed_acsk_hosts() -> set[str]:
    # TODO: Add auto refresh CAs.json
    # using https://iit.com.ua/download/productfiles/CAs.json

    from app.lib import validators

    hosts = EXTRA_PROXY_HOSTS.copy()

    path = get_cas_file_path()
    with open(path) as f:
        raw_data = json.load(f)

    data = validators.validate_pydantic(CAListSchema, {'ca_list': raw_data})
    for ca in data.ca_list:
        addresses = ca.to_dict()
        hosts.update(list(addresses.values()))

    # skip possible blank lines
    hosts.add('')
    hosts.remove('')

    return hosts


def get_cas_file_path() -> Path:
    """
    For dev/test/local environments we use custom CAs.json provided by Diia
    """

    return Path(__file__).parent.parent.parent / CA_JSON_PATH[get_level()]


async def get_server_key_certificate(session: ClientSession, cert_url: str) -> bytes:
    ssl = not services.config.app.debug
    try:
        async with asyncio.timeout(3):
            async with session.get(cert_url, ssl=ssl) as response:
                response.raise_for_status()
                certs = await response.json()
                return base64.b64decode(certs['certificates'][0])
    except (TimeoutError, ClientError, ValueError, IndexError) as err:
        raise ProxyRequestError('Get server certificate error') from err


async def prepare_encrypted_client_session(
    session: ClientSession, cert_url: str
) -> tuple[eusign.EncryptedSession, bytes]:
    redis = services.redis

    client_session_data_key = f'ENCRYPTION_CLIENT_SESSION_DATA_{cert_url}'
    client_data_key = f'ENCRYPTION_CLIENT_DATA_{cert_url}'

    # cached data
    c_client_session_data, c_client_data = await redis.mget(
        client_session_data_key,
        client_data_key,
    )
    if c_client_session_data and c_client_data:
        client_session = await session_load(decode_base64_str(c_client_session_data))
        return client_session, decode_base64_str(c_client_data)

    certificate = await get_server_key_certificate(session, cert_url)

    client_session, client_data = await client_dynamic_key_session_create(
        expire_time=constants.ENCRYPTED_SESSION_DURATION, certificate=certificate
    )

    async with redis.pipeline(transaction=True) as pipe:
        await (
            pipe.setex(
                name=client_session_data_key,
                time=constants.ENCRYPTED_SESSION_DURATION,
                value=generate_base64_str(client_session.session_save()),
            )
            .setex(
                name=client_data_key,
                time=constants.ENCRYPTED_SESSION_DURATION,
                value=generate_base64_str(client_data),
            )
            .execute()
        )

    return client_session, client_data


async def encrypted_request(
    session: ClientSession,
    *,
    cert_url: str,
    url: str,
    data: DataDict,
    headers: DataDict | None = None,
) -> DataDict:
    client_session, client_data = await prepare_encrypted_client_session(session, cert_url)

    request_json_bytes = json.dumps(data).encode()
    encrypted_request_data = client_session.session_encrypt(request_json_bytes)

    _json = {
        'authData': base64.b64encode(client_data).decode('utf-8'),
        'encryptedData': base64.b64encode(encrypted_request_data).decode('utf-8'),
    }

    ssl = not services.config.app.debug
    try:
        async with asyncio.timeout(10):
            async with session.post(url, json=_json, headers=headers, ssl=ssl) as response:
                response.raise_for_status()
                encrypted_response = await response.json()

            encrypted_data: bytes = base64.b64decode(encrypted_response['encryptedData'])
            decrypted_data: bytes = client_session.session_decrypt(encrypted_data)
            signed_json = json.loads(decrypted_data)
            signed_data = base64.b64decode(signed_json['signedData'])
            _, original_data_bytes = verify_internal_sync(0, signed_data)
            return json.loads(original_data_bytes)

    except (TimeoutError, ClientError, RuntimeError, ValueError) as err:
        raise ProxyRequestError() from err


async def encrypted_request_cloud_signer(
    session: ClientSession,
    url: str,
    data: DataDict,
    *,
    headers: DataDict | None = None,
) -> DataDict:
    config = services.config.cloud_signer
    if not config:
        raise ProxyRequestError('CloudSigner config is missing')

    host = config.host
    assert url.startswith(host)
    cert_url = urljoin(host, '/ss/get-certificates')

    try:
        return await encrypted_request(
            session, cert_url=cert_url, url=url, data=data, headers=headers
        )
    except ProxyRequestError as err:
        logger.warning(
            'Got exception on proxy request to CloudSigner',
            exc_info=True,
        )
        raise Error(Code.cloud_signer_error) from err
