import pytest

from api.errors import Error
from app.proxy.validators import validate_proxy_request

TEST_INVALID_TOKEN = 'dGVzdA='
TEST_ADDRESS = 'http://acsk.ua/ocsp/'
TEST_TOKEN = 'dGVzdA=='
TEST_TOKEN_DECODED = b'test'


def test_validate_proxy_request():
    data = {'address': TEST_ADDRESS, 'token': TEST_TOKEN}
    result = validate_proxy_request(data)
    assert result.address == TEST_ADDRESS
    assert result.token == TEST_TOKEN_DECODED


@pytest.mark.parametrize(
    'invalid_data',
    [
        {'address': TEST_ADDRESS},
        {'token': TEST_TOKEN},
        {'address': '', 'token': TEST_TOKEN},
        {'address': TEST_ADDRESS, 'token': ''},
        {'address': TEST_ADDRESS, 'token': TEST_INVALID_TOKEN},
    ],
)
def test_validate_proxy_request_invalid(invalid_data):
    with pytest.raises(Error):
        validate_proxy_request(invalid_data)
