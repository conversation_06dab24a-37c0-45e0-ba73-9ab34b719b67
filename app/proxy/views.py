import logging
import time

import aiohttp_session
from aiohttp import (
    ClientTimeout,
    web,
)
from yarl import URL

from api.errors import (
    <PERSON>,
    Error,
    LoginRequired,
    ServerError,
    Timeout,
)
from app.auth import concierge
from app.auth.constants import (
    AUTHORIZATION_HEADER,
)
from app.auth.decorators import sign_session_base_login_required
from app.auth.middlewares import authenticate_by_sign_session_id
from app.auth.types import AuthUser, BaseUser, User, is_wide_user_type
from app.auth.utils import (
    allow_unathenticated_ua,
    get_request_user,
    get_user_id,
)
from app.lib.hrs.client import HRSClient
from app.proxy.exceptions import ProxyRequestError
from app.proxy.utils import (
    read_allowed_acsk_hosts,
    request_ca_server,
)
from app.proxy.validators import (
    validate_proxy_cloud_signer_request,
    validate_proxy_kep_request,
    validate_proxy_request,
)
from app.services import services

logger = logging.getLogger(__name__)


async def proxy_request(request: web.Request) -> web.Response:
    """Proxy requests to CA Servers.

    Necessity of having proxy: UI cannot make direct requests to CA Servers
    due to CORS policies.
    """
    user: AuthUser | BaseUser | None = get_request_user(request)

    sign_session_id = request.match_info.get('sign_session_id')
    if user is None and sign_session_id:
        user = await authenticate_by_sign_session_id(request, sign_session_id=sign_session_id)

    if user is None and not allow_unathenticated_ua(request, family='IE'):
        raise LoginRequired()

    # Prepare and validate data for communicate with CA Server
    start_time = time.time()
    raw_token = await request.read()
    data = validate_proxy_request(
        {'address': request.rel_url.query.get('address', ''), 'token': raw_token}
    )

    # Data is valid, prepare everything to make request to CA Server
    address = data.address
    token = data.token

    log_extra = {
        'address': address,
        'b64_request': raw_token,
        'b64_request_length': len(raw_token),
        'user_id': user.id if user else None,
        'user_edrpou': user.company_edrpou if user and is_wide_user_type(user) else None,
        'user_email': user.email if user else None,
    }

    if str(URL(address).host) not in read_allowed_acsk_hosts():
        raise Error(Code.access_denied, log_extra=log_extra)

    # Make response to CA Server
    try:
        ca_response = await request_ca_server(
            url=address,
            token=token,
            app_config=services.config.app,
        )
    # Handle error
    except ProxyRequestError as err:
        err_context = err.__context__
        logger.exception(
            'Unhandled exception on proxy request to CA Server',
            extra={
                **log_extra,
                'error_code': getattr(err_context, 'code', 0),
                'error_message': getattr(err_context, 'message', ''),
            },
        )
        raise Error(Code.proxy_error)

    # Log done request for debug purposes
    logger.info(
        'Proxy request to CA server done',
        extra={
            **log_extra,
            'b64_response_length': len(ca_response.body),
            'response_status': ca_response.status,
            'time': time.time() - start_time,
        },
    )

    return web.Response(body=ca_response.body, content_type='x-user/base64-data')


@sign_session_base_login_required
async def proxy_cloud_signer(
    request: web.Request, user: AuthUser | User | BaseUser
) -> web.Response:
    """
    Proxy request from frontend to the signer
    """

    config = services.config.cloud_signer
    if not config:
        raise Error(Code.access_denied)

    address = validate_proxy_cloud_signer_request(request)

    request_data = await request.read()

    _request = services.http_client.request(
        method=request.method,
        url=address,
        params=request.rel_url.query,
        data=request_data.decode('utf-8'),
        timeout=ClientTimeout(total=10),
        headers={
            'Authorization': config.auth_token,
        },
    )
    try:
        async with _request as response:
            data = await response.read()
            logger.info(
                '[Proxy Cloud] Got response',
                {
                    'method': request.method,
                    'url': address,
                    'status': response.status,
                },
            )
            return web.Response(
                body=data, status=response.status, content_type=response.content_type
            )
    except TimeoutError:
        logger.info(
            'Request timeout',
            {
                'method': request.method,
                'url': address,
                'params': request.rel_url.query,
                'data': request_data.decode('utf-8'),
                'headers': {
                    'Content-Type': request.headers.get('Content-Type'),
                },
            },
        )
        raise Timeout()
    except Exception:
        logger.exception('Unhandled exception on KEP proxy')
        raise ServerError(code=Code.error_500)


async def _concierge_sign_in(request: web.Request, user: AuthUser | User) -> None:
    if not user or not user.id:
        raise Error(Code.access_denied)

    logger.info('[Proxy Kep] Concierge sign in', extra={'user_id': user.id})

    await concierge.sign_in()


async def _concierge_sign_out(
    request: web.Request, response: web.Response, user: AuthUser | User
) -> None:
    logger.info(
        '[Proxy Kep] Concierge sing out',
        extra={'user_id': user.id},
    )

    concierge.delete_concierge_cookie(response)

    await concierge.sign_out()


async def proxy_kep(request: web.Request, user: AuthUser | User) -> web.Response:
    """
    Proxy request from frontend to the kep

    TODO: remove this method, it was replaced by new method "/internal-api/kep/certificates"
    """
    address = validate_proxy_kep_request(request)

    request_data = await request.read()

    session = await aiohttp_session.get_session(request)
    session_user = get_user_id(session)
    token = request.headers.get(AUTHORIZATION_HEADER)

    # do not change concierge cookie if user is already signed in
    # via token or session. In this case concierge's cookies are already linked to a user
    # but not for reuests from mobile. In this case we need to sign in concierge
    edit_concierge = not any([isinstance(user, User), session_user, token])
    if edit_concierge:
        await _concierge_sign_in(request, user)

    logger.info(
        '[Proxy Kep] Concierge sign in conditions',
        extra={
            'user': bool(user),
            'user_id': user.id,
            'token': bool(token),
            'session_user': session_user,
        },
    )

    _request = services.http_client.request(
        method=request.method,
        url=address,
        params=request.rel_url.query,
        data=request_data.decode('utf-8'),
        timeout=ClientTimeout(total=10),
        cookies=request.cookies,
    )
    try:
        async with _request as response:
            data = await response.read()
            logger.info(
                '[Proxy Kep] Got response',
                {
                    'method': request.method,
                    'url': address,
                    'status': response.status,
                },
            )
            http_response = web.Response(
                body=data, status=response.status, content_type=response.content_type
            )
            if edit_concierge:
                await _concierge_sign_out(request, http_response, user)

            return http_response
    except TimeoutError:
        logger.info(
            'Request timeout',
            {
                'method': request.method,
                'url': address,
                'params': request.rel_url.query,
                'data': request_data.decode('utf-8'),
                'headers': {
                    'Content-Type': request.headers.get('Content-Type'),
                },
            },
        )

    http_response = ServerError(code=Code.error_500).to_http_exception(request)
    if edit_concierge:
        await _concierge_sign_out(request, http_response, user)

    return http_response


async def hrs_mobile_proxy(request: web.Request, user: User) -> web.Response:
    """
    Proxy for mobile requests to HRS private integration, add user vchasno_id.
    """
    path = request.match_info['path']
    method = request.method
    params = request.query

    client: HRSClient = services.hrs_client

    content = await request.content.read()
    # We should use header, because it contains boundary for multipart request
    content_type = request.headers.get('Content-Type')

    mobile_response = await client.mobile_proxy(
        vchasno_id=user.id,
        method=method,
        path=path,
        params=dict(params),
        content=content,
        content_type=content_type,
    )

    return web.Response(
        status=mobile_response.status,
        body=mobile_response.content,
        headers={
            'Content-Type': mobile_response.content_type,
            **mobile_response.extra_headers,  # type: ignore
        },
    )
