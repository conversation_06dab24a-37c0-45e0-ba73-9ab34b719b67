from app.actions.tables import (
    system_action_table,
    user_visits_table,
)
from app.actions.validators import SaveVisitResult
from app.lib.database import DBConnection
from app.lib.types import DataDict


async def insert_system_actions(conn: DBConnection, data: list[DataDict]) -> None:
    await conn.execute(system_action_table.insert().values(data))


async def insert_user_visit(conn: DBConnection, data: SaveVisitResult) -> None:
    await conn.execute(user_visits_table.insert().values(data.model_dump()))
