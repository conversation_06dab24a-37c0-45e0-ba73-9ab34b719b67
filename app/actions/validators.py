import pydantic
from aiohttp import web

from app.actions.enums import ActionPage
from app.auth.types import User
from app.lib import validators

ACTIONS_COUNT_LIMIT = 100_000


class SaveVisitValidator(pydantic.BaseModel):
    page: ActionPage


class SaveVisitResult(pydantic.BaseModel):
    page: ActionPage
    email: str
    edrpou: str


async def validate_save_visit(request: web.Request, user: User) -> SaveVisitResult:
    raw_data = await validators.validate_json_request(request)
    data = validators.validate_pydantic(SaveVisitValidator, raw_data)
    return SaveVisitResult(page=data.page, email=user.email, edrpou=user.company_edrpou)
