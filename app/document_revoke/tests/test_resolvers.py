from app.document_revoke.db import select_document_revoke_signatures
from app.document_revoke.enums import DocumentRevokeStatus
from app.document_revoke.tests.utils import prepare_revoke, prepare_revoke_signature
from app.services import services
from app.tests.common import (
    TEST_DOCUMENT_EDRPOU_RECIPIENT,
    TEST_DOCUMENT_EMAIL_RECIPIENT,
    fetch_graphql,
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    prepare_user_data,
    with_elastic,
)


async def test_graphql_revolve_document_revoke(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)
    revoke = await prepare_revoke(user)
    revoke2 = await prepare_revoke(user, status=DocumentRevokeStatus.in_progress)

    # Act
    query = """{
        allDocuments {
         documents {
          id
          revoke {
           status
           initiatorCompany {
            edrpou
           }
           initiatorRole {
            email
           }
          }
         }
        }
    }"""
    async with with_elastic(app, [revoke.document_id, revoke2.document_id]):
        response = await fetch_graphql(
            client=client,
            query=query,
            headers=prepare_auth_headers(user),
        )

    # Assert
    assert sorted(response['allDocuments']['documents'], key=lambda x: x['id']) == sorted(
        [
            {
                'id': revoke.document_id,
                'revoke': {
                    'status': revoke.status.value,
                    'initiatorCompany': {'edrpou': user.company_edrpou},
                    'initiatorRole': {'email': user.email},
                },
            },
            {
                'id': revoke2.document_id,
                'revoke': {
                    'status': revoke2.status.value,
                    'initiatorCompany': {'edrpou': user.company_edrpou},
                    'initiatorRole': {'email': user.email},
                },
            },
        ],
        key=lambda x: x['id'],
    )


async def test_graphql_revolve_document_revoke_signatures(aiohttp_client, eusign_mock):
    app, client, user = await prepare_client(aiohttp_client)
    recipient = await prepare_user_data(
        app,
        company_edrpou=TEST_DOCUMENT_EDRPOU_RECIPIENT,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
    )
    document1 = await prepare_document_data(
        app,
        user,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
    )
    document2 = await prepare_document_data(
        app,
        user,
        document_recipients=[
            {
                'edrpou': TEST_DOCUMENT_EDRPOU_RECIPIENT,
                'emails': [TEST_DOCUMENT_EMAIL_RECIPIENT],
            }
        ],
    )
    revoke = await prepare_revoke(user, document=document1)
    revoke2 = await prepare_revoke(
        user, status=DocumentRevokeStatus.in_progress, document=document2
    )
    await prepare_revoke_signature(
        client=client,
        user=user,
        revoke=revoke,
    )
    async with services.db.acquire() as conn:
        signatures = await select_document_revoke_signatures(
            conn=conn,
            revoke_ids=[revoke.id],
        )

    # Act
    query = """{ allDocuments {documents { id revoke { signatures { id user { id }  } } } } }"""
    async with with_elastic(app, [revoke.document_id, revoke2.document_id]):
        response_owner = await fetch_graphql(
            client=client,
            query=query,
            headers=prepare_auth_headers(user),
        )
        response_recipient = await fetch_graphql(
            client=client,
            query=query,
            headers=prepare_auth_headers(recipient),
        )

    # Assert
    assert sorted(response_owner['allDocuments']['documents'], key=lambda x: x['id']) == sorted(
        [
            {
                'id': revoke.document_id,
                'revoke': {'signatures': [{'id': signatures[0].id, 'user': {'id': user.id}}]},
            },
            {'id': revoke2.document_id, 'revoke': {'signatures': []}},
        ],
        key=lambda x: x['id'],
    )
    assert sorted(response_recipient['allDocuments']['documents'], key=lambda x: x['id']) == sorted(
        [
            {
                'id': revoke.document_id,
                'revoke': {'signatures': [{'id': signatures[0].id, 'user': {'id': user.id}}]},
            },
            {'id': revoke2.document_id, 'revoke': {'signatures': []}},
        ],
        key=lambda x: x['id'],
    )
