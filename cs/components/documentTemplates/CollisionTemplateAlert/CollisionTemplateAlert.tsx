import React from 'react';

import { BlackTooltip, Paragraph, Text } from '@vchasno/ui-kit';

import { useCompanyTemplatesQuery } from 'components/DocumentsUploader/useCompanyTemplatesQuery';
import ConditionsList from 'components/documentTemplates/ConditionsList';
import { ConditionsKey, Template } from 'components/documentTemplates/types';
import { t } from 'ttag';

import css from './CollisionTemplateAlert.css';

export interface CollisionTemplateAlertProps {
    condition: ConditionsKey;
    templateId?: string;
    conditionsList: Template['conditionsList'];
}

const CollisionTemplateAlert: React.FC<CollisionTemplateAlertProps> = ({
    condition,
    conditionsList,
    templateId,
}) => {
    const [expanded, setExpanded] = React.useState(false);
    const templateQuery = useCompanyTemplatesQuery({ enabled: true });

    const currentCondition = conditionsList.filter(
        (item) => item[0] === condition,
    );
    const otherTemplates = (templateQuery.data || [])
        .filter((item) => (templateId ? templateId !== item.id : true))
        .filter((template) => {
            if (template.conditionsList.length === 0) {
                return false;
            }

            return template.conditionsList.some(
                (item) => item[0] === condition,
            );
        });

    if (otherTemplates.length === 0 || currentCondition.length === 0) {
        return null;
    }

    const sameConditionCollision = otherTemplates.filter((template) => {
        const templateConditions = template.conditionsList.filter(
            (item) => item[0] === condition,
        );

        if (templateConditions.length !== currentCondition.length) {
            return false;
        }

        return templateConditions.every((item) => {
            return currentCondition.some(
                (currentItem) => currentItem[1] === item[1],
            );
        });
    });

    if (sameConditionCollision.length === 0) {
        return null;
    }

    if (!expanded) {
        return (
            <Paragraph>
                <Text type="secondary">
                    {t`Вказана умова зазначена в інших сценаріях.`}
                </Text>{' '}
                <Text onClick={() => setExpanded(true)} type="link">
                    {t`Показати`} ({sameConditionCollision.length})
                </Text>
            </Paragraph>
        );
    }

    return (
        <Paragraph>
            <Text type="secondary">
                {t`Така умова вже існує в наступних сценаріях: `}
                {sameConditionCollision.map((item, index) => {
                    return (
                        <BlackTooltip
                            disableInteractive
                            key={item.id}
                            title={
                                <>
                                    <ConditionsList
                                        list={item.conditionsList}
                                    />
                                </>
                            }
                        >
                            <Text strong className={css.collisionTemplateName}>
                                "{item.name}"
                            </Text>
                            {index < otherTemplates.length - 1 ? ', ' : ''}
                        </BlackTooltip>
                    );
                })}
            </Text>
        </Paragraph>
    );
};

export default CollisionTemplateAlert;
