import React, { useEffect, useMemo } from 'react';
import { useDispatch } from 'react-redux';

import { Button } from '@vchasno/ui-kit';

import { KEP_SIGN_FLOW_ANALYTICS_EVENT } from 'components/SignWithKepFlow/constants';
import {
    useSignWithApp,
    useUpdateFirebaseStatusMutation,
} from 'components/SignWithKepFlow/hooks';
import { signWithKepFlowActions } from 'components/SignWithKepFlow/signWithKepFlowSlice';
import {
    CloudCertificateInfo,
    SignResult,
} from 'components/SignWithKepFlow/types';
import Popup from 'components/ui/popup/popup';
import eventTracking from 'services/analytics/eventTracking';
import { Document } from 'services/documents/ts/types';
import { t } from 'ttag';

import phoneNotificationSrc from '../../assets/phone-with-notification.png';

import css from './SignWithAppPopup.css';

interface SignWithAppPopupProps {
    onClose: () => void;
    onNoAppSelect: () => void;
    onSignComplete: (signResults: SignResult[]) => void;
    certificates: CloudCertificateInfo[];
    documents: Document[];
}

const SignWithAppPopup: React.FC<SignWithAppPopupProps> = ({
    onClose,
    onNoAppSelect,
    onSignComplete,
    certificates,
    documents,
}) => {
    const updateFirebaseStatusMutation = useUpdateFirebaseStatusMutation();
    const dispatch = useDispatch();

    const {
        countdown,
        resendPushNotification,
        signOperationError,
        signStatusError,
        isResendingPushNotification,
        isSignOperationLoading,
    } = useSignWithApp({
        certificates,
        documents,
        onSignComplete: (signResults) => {
            eventTracking.sendToGTMV4({
                event: KEP_SIGN_FLOW_ANALYTICS_EVENT.APP_SIGN_SUCCESS,
            });
            onSignComplete(signResults);
        },
    });

    const isSignOperationRunning =
        isSignOperationLoading || countdown.status === 'running';

    const errorMessage = useMemo(() => {
        if (signOperationError instanceof Error) {
            return signOperationError.message;
        }

        if (signStatusError instanceof Error) {
            return signStatusError.message;
        }

        if (signOperationError || signStatusError) {
            return t`Виникла неочікувана помилка. Спробуйте, будь ласка, ще раз`;
        }

        return null;
    }, [signOperationError, signStatusError]);

    useEffect(() => {
        eventTracking.sendToGTMV4({
            event: KEP_SIGN_FLOW_ANALYTICS_EVENT.APP_SIGN_SHOW,
        });
    }, []);

    const handleResendPushNotification = async () => {
        eventTracking.sendToGTMV4({
            event: KEP_SIGN_FLOW_ANALYTICS_EVENT.APP_SIGN_RETRY,
        });
        await resendPushNotification();
    };

    const handleNoApp = async () => {
        const clientId = certificates[0]?.acsk_key_id;

        dispatch(
            signWithKepFlowActions.setMeta({
                isMobileAppLogged: false,
                isSignWithAppAvailable: false,
            }),
        );
        eventTracking.sendToGTMV4({
            event: KEP_SIGN_FLOW_ANALYTICS_EVENT.APP_SIGN_NO_APP,
        });

        if (clientId) {
            updateFirebaseStatusMutation.mutate(
                { clientId },
                {
                    onSettled: () => {
                        onNoAppSelect();
                    },
                },
            );
            return;
        }

        onNoAppSelect();
    };

    return (
        <Popup
            active
            onClose={onClose}
            overlayClassName={css.overlay}
            className={css.root}
        >
            <div className={css.content}>
                <div className={css.iconWrapper}>
                    <img
                        src={phoneNotificationSrc}
                        alt={t`Смартфон зі сповіщенням`}
                        className={css.phoneIcon}
                        width={104}
                        height={104}
                    />
                </div>

                <div className={css.heading}>
                    <h1 className={css.title}>
                        {t`Підписання в застосунку «Вчасно.КЕП»`}
                    </h1>
                    <p className={css.description}>
                        {t`Ми надіслали запит на підписання у застосунок «Вчасно.КЕП» на вашому телефоні`}
                    </p>
                </div>

                <div className={css.countdownContainer}>
                    {isSignOperationRunning && !errorMessage && (
                        <>
                            <span className={css.countdownText}>
                                {t`Надіслати повторно через: `}
                            </span>
                            <span className={css.countdownTime}>
                                {countdown.value} {t`сек.`}
                            </span>
                        </>
                    )}

                    {!!errorMessage && (
                        <span className={css.errorText}>{errorMessage}</span>
                    )}
                </div>

                <div className={css.buttonsContainer}>
                    <Button
                        theme={'pink'}
                        size="lg"
                        wide
                        disabled={isSignOperationRunning}
                        loading={isResendingPushNotification}
                        onClick={handleResendPushNotification}
                    >
                        {t`Надіслати повторно`}
                    </Button>

                    <Button
                        theme="secondary"
                        size="lg"
                        wide
                        onClick={handleNoApp}
                        loading={updateFirebaseStatusMutation.isLoading}
                    >
                        {t`Підписати без застосунка`}
                    </Button>
                </div>
            </div>
        </Popup>
    );
};

export default SignWithAppPopup;
