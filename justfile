default:
    @just --list

# Build python image for x64/ARM arhcitecture. All python services in docker compose use

# the same Docker image, so it's enough to build just one python service
build-python-image:
    docker compose build web

# Compile requirements from requirements.in to requirements.txt

# HINT: to install requirements use "just install-python-deps" or "just build-python-image"
pip-compile:
    docker compose run --rm python-dev bash -c 'uv pip compile pyproject.toml requirements/base.in -o requirements/base.txt --emit-index-url'
    docker compose run --rm python-dev bash -c 'uv pip compile pyproject.toml requirements/base.in requirements/dev.in -o requirements/dev.txt --emit-index-url'

# Install python dependencies from requirements.txt to python docker image.

# It works as faster alternative to use "just build-python-image" command, if only requirements.txt was changed.
install-python-deps:
    . ./scripts/install-python-deps.sh

# Prepare test environment and run bash shell. Can be used for debugging tests.
start-test-shell *args='-n auto':
    docker compose run --rm pytest --shell {{ args }}

# Run tests
run-test *args='':
    docker compose run --rm pytest -x -vv {{ args }}

# Run ipython shell for working server
ipython:
    docker compose exec web bash -c 'ipython profile create evodoc'
    docker compose exec web bash -c 'cp .ipython/profile_default/startup/startup.py /root/.ipython/profile_evodoc/startup/'
    docker compose exec web bash -c 'ipython --profile=evodoc'

# Start backend services
start-backend *args='':
    docker compose up web {{ args }}

# Start caddy server. Caddy is used for https connection on localhost for collabora
caddy:
    docker compose up caddy

# ============ Frontend =============

# Start frontend in development mode
start-frontend:
    docker compose up webpack

# Update graphql schema and generate types for frontend
update-graphql-schema:
    yarn run graphql:schema
    yarn run graphql:codegen

# ============ Linting and formatting =============
lint *args='':
    docker compose run {{ args }} --rm ruff-formatter
    docker compose run {{ args }} --rm ruff-linter --fix --unsafe-fixes
    docker compose run {{ args }} --rm mypy
    docker compose run {{ args }} --rm python-dev ./scripts/translations/update_catalog.py

lint-local *args='':
    ruff format api app cron scripts worker indexator conftest.py
    ruff check api app cron scripts worker indexator conftest.py --fix --unsafe-fixes
    mypy api app cron scripts worker indexator
    PYTHONPATH=. python scripts/translations/update_catalog.py

lint-frontend:
    docker compose run --rm cslint
    docker compose run --rm cstest
    docker compose run --rm cs-vulnerabilities-check

format-markdown-docs:
    docker compose run --rm yarn markdown:format

# ============ Code coverage =============

# Run coverage and create xml-report.
coverage *args='':
    docker compose run --rm pytest -n auto {{ args }} --cov --cov-report xml

coverage-html *args='':
    docker compose run --rm pytest -n auto {{ args }} --cov --cov-report html
    open htmlcov/index.html || xdg-open htmlcov/index.html

# Run coverage and append xml-report to existant.
coverage-append *args='':
    docker compose run --rm pytest -n auto {{ args }} --cov --cov-append --cov-report xml

# ============ Database =============

# Connect to postgres database
psql:
    docker compose exec postgres bash -c 'psql -U evodoc'

# Connect to postgres database for events
psql-events:
    docker compose exec postgres-events bash -c 'psql -U evodoc -d evodoc_events'

# Prepare development environment and run bash shell
start-shell:
    docker compose run --rm python-dev bash

# Remove test database to start from scratch. Use it in case of migrations issues
remove-test-db:
    docker compose rm --stop --force postgres-test

# Copy data from DEV environment
copy-dev: && alembic-upgrade
    # Encrypted with following command:
    # zip trunk.zip trunk.sql
    # openssl enc -aes-256-cbc -salt -in trunk.zip -out trunk.zip.enc -pass pass:G7MouLCxDZWzqb3o -pbkdf2

    curl "https://drive.usercontent.google.com/download?id=1O4kOWxN_W_lXWFOPYQNcouKgg5pj5TYk&confirm=xxx" -o /tmp/trunk.zip.enc
    openssl enc -d -aes-256-cbc -in /tmp/trunk.zip.enc -out scripts/trunk.zip -pass pass:G7MouLCxDZWzqb3o -pbkdf2
    unzip -o scripts/trunk.zip -d scripts

    docker-compose rm -fs postgres
    docker-compose up -d postgres
    docker-compose exec postgres bash -c 'cd /scripts && ./wait-for-it.sh -t 0 localhost:5432 -- bash ./postgres/copy-trunk-db.sh'

    rm scripts/trunk.zip scripts/trunk.sql scripts/trunk.zip.enc || true

# ====== Elasticsearch =====

# Recreate index in elasticsearch from scratch
prepare-elasticsearch:
    just run-elastic-script index create

# Run elasticsearch script with custom arguments
run-elastic-script *args='':
    docker compose run --rm web bash -c 'python -m scripts.elastic {{ args }}'

# ====== Alembic migrations =====


# NOTE: it's internal recipe, use it only as a part of other recipes. To pass arguments wrap them in quotes
# e.g. just alembic 'upgrade heads' to avoid shell expansion issues
_alembic args='':
    docker compose run --rm web bash -c '/work/scripts/wait-for-it.sh -t 0 postgres:5432 -- alembic -n alembic:vchasno-docker {{ args }}'

_alembic-events args='':
    docker compose run --rm web bash -c '/work/scripts/wait-for-it.sh -t 0 postgres-events:5432 -- alembic --config ./app/events/alembic.ini -n alembic:vchasno-docker {{ args }}'

# Migrate database to the latest version
alembic-upgrade:
    just _alembic 'upgrade heads'

# Downgrade database to the specified level
alembic-downgrade LEVEL:
    just _alembic 'downgrade "{{ LEVEL }}"'

# Generate new migration file
alembic-autogenerate MESSAGE:
    just _alembic 'revision --autogenerate -m "{{ MESSAGE }}"'

# Migrate events database to the latest version
alembic-upgrade-events:
    just _alembic-events 'upgrade heads'

# Generate new migration file for events database
alembic-autogenerate-events MESSAGE:
    just _alembic-events 'revision --autogenerate -m "{{ MESSAGE }}"'

alembic-downgrade-events LEVEL:
    just _alembic-events 'downgrade "{{ LEVEL }}"'

# You can use this command to fix this issue: "Multiple head revisions are present for given argument head"
alembic-merge-heads:
    just _alembic 'merge heads'

# ====== Translations =====

# Update translations catalog
translations-update:
    docker compose run --rm python-dev ./scripts/translations/update_catalog.py

translations-compile:
    docker compose run --rm python-dev ./scripts/translations/compile_catalog.py

translations-check-catalog:
    docker compose run --rm python-dev ./scripts/translations/check_catalog.py

translations-check-coverage:
    docker compose run --rm python-dev ./scripts/translations/check_coverage.py

translations-get-unwrapped-messages:
    docker compose run --rm python-dev ./scripts/translations/get_unwrapped_messages.py

translations-update-frontend:
    . ./scripts/translations/update-frontend-catalog.sh

# ====== Deploy ======

# Generate new deploy tag and push it. (Creation of new tag and pushing is optinal, can be used only for generation of changelog)
push-new-tag:
    ./scripts/push-new-deploy-tag.sh

# Update IIT certificates Requires `wget` and `yarn` to be installed for usage.

# Vchasno-signer update must be published before running this command
update-certs:
    wget --output-document ./eusign/prod/CAs.json https://iit.com.ua/download/productfiles/CAs.json
    wget --output-document ./eusign/dev/CAs.json https://iit.com.ua/download/productfiles/CAs.Test.All.json
    wget --output-document ./eusign/prod/certificates/CACertificates.p7b https://iit.com.ua/download/productfiles/CACertificates.p7b
    wget --output-document ./eusign/dev/certificates/CACertificates.p7b https://iit.com.ua/download/productfiles/CACertificates.Test.All.p7b
    yarn upgrade --latest @evo/vchasno-signer
    yarn install --update @evo/vchasno-signer
